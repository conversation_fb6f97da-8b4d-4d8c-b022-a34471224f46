using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using Unity.Collections;
using System.IO;
using UnityEngine.Experimental.Rendering;
using UnityEngine.SocialPlatforms;
using System.Security.Cryptography;
using System.Linq;
using Unity.Mathematics;
using System.Reflection;
using System.Linq.Expressions;
using static System.Net.Mime.MediaTypeNames;
using System.Xml.Linq;
using System;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class RegionTexBake_ShaderParams
{
    public static int _REdgeWidth = Shader.PropertyToID("_REdgeWidth");
    public static int _BEdgeWidth = Shader.PropertyToID("_BEdgeWidth");
    public static int _GEdgeWidth = Shader.PropertyToID("_GEdgeWidth");
    public static int _Offset = Shader.PropertyToID("_Offset");
    public static int _GEdgeTex = Shader.PropertyToID("_GEdgeTex");
    public static int _BlurSize = Shader.PropertyToID("_BlurSize");
    public static int _Scale = Shader.PropertyToID("_Scale");
    public static int _EnableBicubic = Shader.PropertyToID("_EnableBicubic");
    public static int _AlphaTex = Shader.PropertyToID("_AlphaTex");
    public static int _HasAlphaChannel = Shader.PropertyToID("_HasAlphaChannel");
}




public class PolygonDecomposer 
{
    public List<Vector2> vertices; // List of vertices defining the concave polygon

    public List<List<Vector2>> DecomposeIntoConvexPolygons(List<Vector2> vertices)
    {
        List<List<Vector2>> convexPolygons = new List<List<Vector2>>();

        // Step 1: Triangulate the polygon
        List<int> triangles = EarClippingTriangulation(vertices);

        // Step 2: Group triangles into convex polygons (each triangle here is already convex)
        for (int i = 0; i < triangles.Count; i += 3)
        {
            List<Vector2> triangle = new List<Vector2>
            {
                vertices[triangles[i]],
                vertices[triangles[i + 1]],
                vertices[triangles[i + 2]]
            };
            convexPolygons.Add(triangle);
        }

        return convexPolygons;
    }

    List<int> EarClippingTriangulation(List<Vector2> vertices)
    {
        List<int> indices = new List<int>();
        if (vertices.Count < 3) return indices;

        // Make a copy of vertices indices
        List<int> remainingIndices = new List<int>();
        for (int i = 0; i < vertices.Count; i++)
            remainingIndices.Add(i);

        while (remainingIndices.Count > 3)
        {
            bool earFound = false;
            for (int i = 0; i < remainingIndices.Count; i++)
            {
                int iPrev = (i - 1 + remainingIndices.Count) % remainingIndices.Count;
                int iNext = (i + 1) % remainingIndices.Count;

                int vPrev = remainingIndices[iPrev];
                int vCurr = remainingIndices[i];
                int vNext = remainingIndices[iNext];

                if (IsEar(vertices, vPrev, vCurr, vNext, remainingIndices))
                {
                    // Add the triangle indices
                    indices.Add(vPrev);
                    indices.Add(vCurr);
                    indices.Add(vNext);

                    // Remove the "ear" vertex from the polygon
                    remainingIndices.RemoveAt(i);
                    earFound = true;
                    break;
                }
            }

            if (!earFound)
            {
                Debug.LogError("Ear clipping failed; possibly due to a non-simple polygon or degenerate vertices.");
                return indices;
            }
        }

        // Add the last remaining triangle
        indices.Add(remainingIndices[0]);
        indices.Add(remainingIndices[1]);
        indices.Add(remainingIndices[2]);

        return indices;
    }

    bool IsEar(List<Vector2> vertices, int vPrev, int vCurr, int vNext, List<int> remainingIndices)
    {
        Vector2 a = vertices[vPrev];
        Vector2 b = vertices[vCurr];
        Vector2 c = vertices[vNext];

        // Check if triangle (a, b, c) is counter-clockwise
        if (Vector2.SignedAngle(b - a, c - b) <= 0) return false;

        // Check if no other points are inside this triangle
        for (int i = 0; i < remainingIndices.Count; i++)
        {
            int vi = remainingIndices[i];
            if (vi == vPrev || vi == vCurr || vi == vNext) continue;
            if (IsPointInTriangle(vertices[vi], a, b, c)) return false;
        }
        return true;
    }

    bool IsPointInTriangle(Vector2 p, Vector2 a, Vector2 b, Vector2 c)
    {
        // Barycentric technique for checking if a point is within a triangle
        float area = 0.5f * (-b.y * c.x + a.y * (-b.x + c.x) + a.x * (b.y - c.y) + b.x * c.y);
        float s = 1 / (2 * area) * (a.y * c.x - a.x * c.y + (c.y - a.y) * p.x + (a.x - c.x) * p.y);
        float t = 1 / (2 * area) * (a.x * b.y - a.y * b.x + (a.y - b.y) * p.x + (b.x - a.x) * p.y);
        return s > 0 && t > 0 && (s + t) < 1;
    }
}
public enum EdgeType
{
    Red,
    Green,
    Blue,
}

public enum TextureSize
{
    _32 = 32,
    _64 = 64,
    _128 = 128,
    _256 = 256,
}
public class EightSSEDT
{
     static Vector4 EncodeFloatRGBA(float v)
     {
        float4 kEncodeMul = new float4(1.0f, 255.0f, 65025.0f, 16581375.0f);
        float kEncodeBit = 1.0f / 255.0f;
        float4 enc = kEncodeMul * v;
        enc.x = enc.x -  Mathf.Floor(enc.x);
        enc.y = enc.y - Mathf.Floor(enc.y);
        enc.z = enc.z - Mathf.Floor(enc.z);
        enc.w = enc.w - Mathf.Floor(enc.w);
        enc -= enc.yzww * kEncodeBit;
        return enc;
     }

    static float2 EncodeFloatRG(float v)
    {
        float2 kEncodeMul = new float2(1.0f, 255.0f);
        float kEncodeBit = 1.0f / 255.0f;
        float2 enc = kEncodeMul * v;
        //enc = frac(enc);
        enc.x = enc.x - Mathf.Floor(enc.x);
        enc.y = enc.y - Mathf.Floor(enc.y);
        enc.x -= enc.y * kEncodeBit;
        return enc;
    }

    static float DecodeFloatRG(float2 enc)
    {
        float2 kDecodeDot = new float2(1.0f, 1.0f / 255.0f);
        return Vector2.Dot(enc, kDecodeDot);
    }

    static float DecodeFloatRGBA(float4 enc)
    {
        float4 kDecodeDot = new float4(1.0f, 1 / 255.0f, 1 / 65025.0f, 1 / 16581375.0f);
        return Vector4.Dot(enc, kDecodeDot);
    }

    public static Texture2D ComputeEDT(Texture2D tex, float basicMaxDistance,  EdgeType edgeType,bool reverse = false)
    {
        bool[,] binaryImage = new bool[tex.height, tex.width];
        for(int wIndex = 0; wIndex < binaryImage.GetLength(0); wIndex++)
        {
            for(int hIndex = 0; hIndex < binaryImage.GetLength(1); hIndex++)
            {
                Color color =  tex.GetPixel(hIndex, wIndex);
                switch(edgeType)
                {
                    case EdgeType.Red:
                        if(reverse)
                        {
                            binaryImage[wIndex, hIndex] = ((1 - color.r) > 0) ? true : false;
                        }
                        else
                        {
                            binaryImage[wIndex, hIndex] = ((color.r > 0)) ? true : false;
                        }
            
                        break;
                    case EdgeType.Green:
                        if(reverse)
                        {
                            binaryImage[wIndex, hIndex] = ((1 - color.g) > 0) ? true : false;
                        }
                        else
                        {
                            binaryImage[wIndex, hIndex] = ((color.g > 0)) ? true : false;
                        } 
                        break;
                    case EdgeType.Blue:
                        if(reverse)
                        {
                            binaryImage[wIndex, hIndex] = ((1 - color.b) > 0) ? true : false;
                        }
                        else
                        {
                            binaryImage[wIndex, hIndex] = ((color.b > 0)) ? true : false;
                        }
            
                        break;
                }
                
            }
        }
        int width = binaryImage.GetLength(0);
        int height = binaryImage.GetLength(1);
        float[,] distanceMap = new float[width, height];
        float inf = width * height;

        // Initialization: set distances for foreground/background pixels
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                distanceMap[x, y] =  binaryImage[x, y] ? 0 : inf;
            }
        }

        float maxDistance = 0;
        // First pass: scan from top-left to bottom-right
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                if (distanceMap[x, y] > 0)
                {
                    // Check top-left neighbors
                    if (x > 0) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x - 1, y] + 1);
                    if (y > 0) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x, y - 1] + 1);
                    if (x > 0 && y > 0) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x - 1, y - 1] + 1.4142135623f); // Diagonal distance (sqrt(2))
                    if (distanceMap[x, y] != inf)
                    {
                        maxDistance = Mathf.Max(maxDistance, distanceMap[x, y]);
                    }
                }
            }
        }

        // Second pass: scan from bottom-right to top-left
        for (int x = width - 1; x >= 0; x--)
        {
            for (int y = height - 1; y >= 0; y--)
            {
                if (distanceMap[x, y] > 0)
                {
                    // Check bottom-right neighbors
                    if (x < width - 1) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x + 1, y] + 1);
                    if (y < height - 1) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x, y + 1] + 1);
                    if (x < width - 1 && y < height - 1) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x + 1, y + 1] + 1.4142135623f); // Diagonal distance
                    if (distanceMap[x, y] != inf)
                    {
                        maxDistance = Mathf.Max(maxDistance, distanceMap[x, y]);
                    }
                }
            }
        }

        // Third pass: scan from top-right to bottom-left
        for (int x = width - 1; x >= 0; x--)
        {
            for (int y = 0; y < height; y++)
            {
                if (distanceMap[x, y] > 0)
                {
                    if (x < width - 1) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x + 1, y] + 1);
                    if (y > 0) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x, y - 1] + 1);
                    if (x < width - 1 && y > 0) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x + 1, y - 1] + 1.4142135623f);
                    if (distanceMap[x, y] != inf)
                    {
                        maxDistance = Mathf.Max(maxDistance, distanceMap[x, y]);
                    }
                }
            }
        }

        // Fourth pass: scan from bottom-left to top-right
        for (int x = 0; x < width; x++)
        {
            for (int y = height - 1; y >= 0; y--)
            {
                if (distanceMap[x, y] > 0)
                {
                    if (x > 0) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x - 1, y] + 1);
                    if (y < height - 1) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x, y + 1] + 1);
                    if (x > 0 && y < height - 1) distanceMap[x, y] = Mathf.Min(distanceMap[x, y], distanceMap[x - 1, y + 1] + 1.4142135623f);
                    if (distanceMap[x, y] != inf)
                    {
                        maxDistance = Mathf.Max(maxDistance, distanceMap[x, y]);
                    }
                }
            }
        }

        //float maxDistance = Mathf.Max(tex.width, tex.height);
        int row = distanceMap.GetLength(0);
        int col = distanceMap.GetLength(1);
        Color[] sdfColors = new Color[row * col];
        for (int x = 0; x < row; x++)
        {
            for (int y = 0; y < col; y++)
            {
                float distance = distanceMap[x, y];
                maxDistance = maxDistance > 0 ? maxDistance : Mathf.Max(row, col);
                float percent = (maxDistance / basicMaxDistance);
                distance = (distance / maxDistance)* percent;
                distance = Mathf.Clamp(distance, 0.0f, 0.999999f);
                //distance = Mathf.Clamp((distance / maxDistance), 0.0f, 0.999999999999f);
                //Vector4 encode = EncodeFloatRGBA(distance);
                //float tmp =  DecodeFloatRGBA(encode);
                Vector2 encode = EncodeFloatRG(distance);
                sdfColors[x * col + y] = new Vector4(encode.x,encode.y,0,0);
            }
        }
        var tmpTex =new Texture2D(col, row, TextureFormat.RGBA32, false);
        tmpTex.SetPixels(sdfColors);
        tmpTex.Apply();
        return tmpTex;
    }
}

public class RegionTexExporter : MonoBehaviour
{
#if UNITY_EDITOR
    #region private filed
    private LineRenderer m_lineRender;
    private RenderTexture m_regionRT;
    private Vector2 m_texSize = new Vector2(2048, 2048);
    private Shader m_bakeShader;
    private Shader m_combineShader;
    private string m_folderPath;
    private float m_frustPlaneDisOffset = 0.5f;

    private Vector2 m_uvScale = Vector2.one;

    private bool m_debugMesh = false;

    private float m_basicMaxDistance = 443.6493f;

    private bool m_generateSpriteObject = true;

    private Material m_spriteMat;

    private TextureSize m_textureSize = global::TextureSize._64;
    #endregion

    #region public propertis

    public Vector2 TextureSize
    {
        get
        {
            return m_texSize;
        }
        set
        {
            m_texSize = value;
        }
    }

    public string FolderPath
    {
        get { return m_folderPath; }
        set
        {
            m_folderPath = value;
        }
    }

    public float FrustPlaneDisOffset
    {
        get
        {
            return m_frustPlaneDisOffset;
        }
        set
        {
            m_frustPlaneDisOffset = value;
        }
    }

    public bool DebugMesh
    {
        get
        {
            return m_debugMesh;
        }
        set
        {
            m_debugMesh = value;
        }
    }

    public float BasicMaxDistance
    {
        get
        {
            return m_basicMaxDistance;
        }
        set
        {
            m_basicMaxDistance = value;
        }
    }

    public bool GenerateSprite
    {
        get
        {
            return m_generateSpriteObject;
        }
        set
        {
            m_generateSpriteObject = value;
        }
    }

    public Material SpriteMat
    {
        get
        {
            return m_spriteMat;
        }
        set
        {
            m_spriteMat = value;
        }
    }

    public TextureSize MAX_Texture_Size
    {
        get
        {
            return m_textureSize;
        }
        set
        {
            m_textureSize = value;
        }
    }
    #endregion

    #region private method
    private void GenerateTexture()
    {
        m_regionRT = new RenderTexture((int)m_texSize.x, (int)m_texSize.y, 0, RenderTextureFormat.ARGB32);
    }
    private void Release()
    {
        m_regionRT.Release();
        m_regionRT = null;
    }

    private static Mesh s_FullscreenMesh = null;
    public static Mesh fullScreenMesh
    {
        get
        {
            if(s_FullscreenMesh!=null)
            {
                return s_FullscreenMesh;
            }

            float y = 1f;
            float y2 = 0f;
            s_FullscreenMesh = new Mesh
            {
                name = "Fullscreen Quad"
            };

            s_FullscreenMesh.SetVertices(new List<Vector3>
            {
                new Vector3(-1f,-1f,0f),
                new Vector3(-1f,1f,0f),
                new Vector3(1f,-1f,0f),
                new Vector3(1f,1f,0f)
            });

            s_FullscreenMesh.SetUVs(0, new List<Vector2>
            {
                new Vector2(0f,y2),
                new Vector2(0f,y),
                new Vector2(1f,y2),
                new Vector2(1f,y)
            });

            s_FullscreenMesh.SetIndices(new int[6] { 0, 1, 2, 2, 1, 3 }, MeshTopology.Triangles, 0, calculateBounds: false);
            s_FullscreenMesh.UploadMeshData(markNoLongerReadable: true);
            return s_FullscreenMesh;
        }
    }

    private Texture2D RTToTex(RenderTexture rt)
    {
        Texture2D texture2D = new Texture2D((int)m_texSize.x, (int)m_texSize.y, TextureFormat.ARGB32, true);
        var oriRT = RenderTexture.active;
        RenderTexture.active = rt;
        texture2D.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
        texture2D.Apply();
        RenderTexture.active = oriRT;
        return texture2D;
    }

    private void SaveTex(Texture2D tex,string texName)
    {
        string tex_name = texName + ".png";
        string fileName = Path.Combine(m_folderPath, tex_name);
        byte[] bytes = tex.EncodeToPNG();
        File.WriteAllBytes(fileName, bytes);

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    private void SaveTex(RenderTexture rt,string texName)
    {
        Texture2D texture2D = new Texture2D((int)rt.width, (int)rt.height, TextureFormat.ARGB32,true);
        var oriRT = RenderTexture.active;
        RenderTexture.active = rt;
        texture2D.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
        texture2D.Apply();
        RenderTexture.active = oriRT;
        string tex_name = texName + ".png";
        string fileName = Path.Combine(m_folderPath, tex_name);
        byte[] bytes = texture2D.EncodeToPNG();
        File.WriteAllBytes(fileName, bytes);

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();

        TextureImporterStandardSetting(fileName, TextureImporterFormat.RGB24);
    }

    private void TextureImporterStandardSetting(string filePath, TextureImporterFormat format)
    {
        var textureImporter = AssetImporter.GetAtPath(filePath) as TextureImporter;

        textureImporter.textureType = TextureImporterType.Sprite;

        TextureImporterPlatformSettings androidImporterSettings = textureImporter.GetPlatformTextureSettings("Android");
        androidImporterSettings.overridden = true;
        androidImporterSettings.format = format;
        androidImporterSettings.maxTextureSize = (int)m_textureSize;
        textureImporter.SetPlatformTextureSettings(androidImporterSettings);

        TextureImporterPlatformSettings iponeImporterSettings = textureImporter.GetPlatformTextureSettings("iPone");
        iponeImporterSettings.overridden = true;
        iponeImporterSettings.format = format;
        textureImporter.SetPlatformTextureSettings(iponeImporterSettings);
        iponeImporterSettings.maxTextureSize = (int)m_textureSize;
        textureImporter.filterMode = FilterMode.Bilinear;
        textureImporter.wrapMode = TextureWrapMode.Clamp;

        textureImporter.SaveAndReimport();
    }

    void CombineTex(RenderTexture src,RenderTexture gEdge, RenderTexture dst, RenderTexture alpha, Material mat)
    {
        mat.SetTexture(RegionTexBake_ShaderParams._GEdgeTex, gEdge);
        mat.SetVector(RegionTexBake_ShaderParams._Scale,m_uvScale);
        mat.SetTexture(RegionTexBake_ShaderParams._AlphaTex, alpha);
        Graphics.Blit(src, dst, mat);
    }


    Vector2 CalculateCentroid(List<Vector2> vertices)
    {
        float area = 0f;
        float cx = 0f;
        float cy = 0f;

        int n = vertices.Count;

        for (int i = 0; i < n; i++)
        {
            Vector2 current = vertices[i];
            Vector2 next = vertices[(i + 1) % n];  // Wrap to the first vertex

            float crossProduct = current.x * next.y - next.x * current.y;
            area += crossProduct;
            cx += (current.x + next.x) * crossProduct;
            cy += (current.y + next.y) * crossProduct;
        }

        area *= 0.5f;
        cx /= (6 * area);
        cy /= (6 * area);

        return new Vector2(cx, cy);
    }

    private void GenerateSpriteGo(Material spriteMat, string spriteName,Vector2 curExtent,Vector2 curCenter)
    {
        if(m_generateSpriteObject)
        {
            GameObject zoomRoot = GameObject.Find("ZoomRoot");
            if (zoomRoot == null)
            {
                zoomRoot = new GameObject("ZoomRoot");
                zoomRoot.transform.localEulerAngles = new Vector3(90, 0, 0);
                zoomRoot.transform.localPosition = Vector3.zero;
                zoomRoot.transform.localScale = Vector3.one;
            }

            string tex_name = spriteName + ".png";
            string fileName = Path.Combine(m_folderPath, tex_name);
            var sprite_tex = AssetDatabase.LoadAssetAtPath<Sprite>(fileName);

            GameObject sprite = new GameObject(spriteName);
            var spriteRender = sprite.AddComponent<SpriteRenderer>();
            sprite.transform.parent = zoomRoot.transform;
            sprite.transform.localPosition = new Vector3(curCenter.x*sprite_tex.bounds.extents.x*0.985f, curCenter.y* sprite_tex.bounds.extents.y*0.985f, 1);
            sprite.transform.localRotation = Quaternion.identity;
            sprite.transform.localScale = new Vector3(curExtent.x, curExtent.y, 1);
            spriteRender.sprite = sprite_tex;
            spriteRender.material = spriteMat;
        }
    }
    #endregion

    #region public Method

    public void ExportRegionTex()
    {
        m_lineRender = GetComponent<LineRenderer>();
        if (m_lineRender == null)
        {
            Debug.LogError("[Region Tex Exporter] Cur Go:" + transform.name + " line render is invalide");
            return;
        }
        m_bakeShader = Shader.Find("Comic/RegionTex/RegionTexBake");
        Material bakeMat = new Material(m_bakeShader);

        m_combineShader = Shader.Find("Comic/RegionTex/RegionTexCombine");
        Material combineMat = new Material(m_combineShader);

        var positionCount = m_lineRender.positionCount;
        Vector3[] point_positions = new Vector3[positionCount];
        m_lineRender.GetPositions(point_positions);
        Vector2[] _2dpoint_positions = new Vector2[positionCount];
        Dictionary<Vector2, int> pointsDict = new Dictionary<Vector2, int>();
        for(int positionIndex= 0;positionIndex<point_positions.Length; positionIndex++)
        {
            _2dpoint_positions[positionIndex] = point_positions[positionIndex];

            if (pointsDict.ContainsKey(_2dpoint_positions[positionIndex]))
                continue;

            pointsDict.Add(_2dpoint_positions[positionIndex], positionIndex);
        }

        PolygonDecomposer decomposer = new PolygonDecomposer();
        List<List<Vector2>> polygons = decomposer.DecomposeIntoConvexPolygons(_2dpoint_positions.ToList());

        Mesh mesh = new Mesh();
        Vector3[] vertices = new Vector3[positionCount];
        Color[] colors = new Color[positionCount];
        List<int> indicesList = new List<int>();

        for (int i = 0; i < positionCount; i++)
        {
            vertices[i] = _2dpoint_positions[i];
            colors[i] = new Vector4(1, 1, 1, 1);
        }
        for (int polygonIndex = 0; polygonIndex < polygons.Count; polygonIndex++)
        {
            List<Vector2> polygon = polygons[polygonIndex];
            for (int positionIndex = polygon.Count-1; positionIndex >=0; positionIndex--)
            {
                int indice;
                pointsDict.TryGetValue(polygon[positionIndex], out indice);
                indicesList.Add(indice);
            }
        }

        mesh.vertices = vertices;
        mesh.colors = colors;
        mesh.SetIndices(indicesList.ToArray(), MeshTopology.Triangles, 0);
        mesh.RecalculateBounds();

        GameObject tempGo = new GameObject();
        tempGo.transform.position = transform.position;
        tempGo.transform.rotation = transform.rotation;
        tempGo.transform.localScale = transform.lossyScale;

        var mf = tempGo.AddComponent<MeshFilter>();
        var mr = tempGo.AddComponent<MeshRenderer>();
        mf.sharedMesh = mesh;
        mr.sharedMaterial = bakeMat;

        Bounds m_bounds =  mf.sharedMesh.bounds;
        m_bounds = m_bounds.Transform(transform.localToWorldMatrix);
        Vector2 m_extent_ws = new Vector2(m_bounds.extents.x, m_bounds.extents.z);
        Vector2 m_center_ws = new Vector2(m_bounds.center.x, m_bounds.center.z);

        Vector3 cameraPos = new Vector3(m_bounds.center.x, m_bounds.center.y + m_bounds.extents.y * 2+1, m_bounds.center.z);
        Matrix4x4 viewMatrix = Matrix4x4.LookAt(cameraPos, m_bounds.center, Vector3.forward).inverse;
        m_bounds = m_bounds.Transform(viewMatrix);

        float aspect = 1;
        m_texSize.y = m_texSize.x;

        float xSizeAdd = m_frustPlaneDisOffset;
        float ySizeAdd = xSizeAdd * aspect;
        Matrix4x4 projectMatrix = Matrix4x4.Ortho(-m_bounds.extents.x - xSizeAdd, m_bounds.extents.x + xSizeAdd,
            -m_bounds.extents.y - ySizeAdd,
           m_bounds.extents.y + ySizeAdd,
            0, -10);
        float uScale = m_bounds.extents.x / (m_bounds.extents.x + xSizeAdd);
        float vScale = m_bounds.extents.y / (m_bounds.extents.y + ySizeAdd);
        m_uvScale.Set(uScale, vScale);

        GenerateTexture();

        CommandBuffer commnandBuffer = new CommandBuffer();
        commnandBuffer.name = "Region Tex Exporter";
        commnandBuffer.SetRenderTarget(m_regionRT);

        commnandBuffer.ClearRenderTarget(false, true, Color.black, 1);
        commnandBuffer.SetViewProjectionMatrices(viewMatrix, projectMatrix);
        commnandBuffer.SetViewport(new Rect(0, 0, m_texSize.x, m_texSize.y));

        commnandBuffer.DrawRenderer(mr, bakeMat, 0, 0);
        Graphics.ExecuteCommandBuffer(commnandBuffer);

        commnandBuffer.Clear();
        commnandBuffer.Release();
        commnandBuffer = null;

        //Shader blurShader = Shader.Find("Comic/RegionTex/StandardKawaseBlur");
        //Material blurMat = new Material(blurShader);
        //float blurRadius = 0.15f;
        //RenderTexture tmpBlur = new RenderTexture(m_regionRT);
        //Graphics.Blit(m_regionRT, tmpBlur);
        //for (int blurIndex = 0; blurIndex < 20; blurIndex++)
        //{
        //    Shader.SetGlobalFloat(RegionTexBake_ShaderParams._Offset, blurRadius * blurIndex);
        //    Graphics.Blit(tmpBlur, m_regionRT, blurMat);
        //    Graphics.Blit(m_regionRT, tmpBlur);
        //}

        if (m_debugMesh)
        {
            SaveTex(m_regionRT, "Mesh_Bake");
        }

        RenderTexture alphaTex = new RenderTexture(m_regionRT);
        Graphics.Blit(m_regionRT, alphaTex);

        var inner = RTToTex(m_regionRT);
        Texture2D sdfTex = EightSSEDT.ComputeEDT(inner, m_basicMaxDistance, EdgeType.Blue, true);

        Shader sdfRepShader = Shader.Find("Comic/RegionTex/RegionTexSDFRep");
        Material sdfRepMaterial = new Material(sdfRepShader);

        sdfRepMaterial.SetTexture(RegionTexBake_ShaderParams._GEdgeTex, sdfTex);
        sdfRepMaterial.SetFloat(RegionTexBake_ShaderParams._HasAlphaChannel, 0);
        sdfRepMaterial.SetVector(RegionTexBake_ShaderParams._Scale, m_uvScale);

        RenderTexture newTex = new RenderTexture((int)sdfTex.width, (int)sdfTex.height, 0, RenderTextureFormat.ARGB32);
        Graphics.Blit(alphaTex, newTex, sdfRepMaterial);

        SaveTex(newTex, transform.gameObject.name);

        DestroyImmediate(sdfRepMaterial);
        sdfRepMaterial = null;

        newTex.Release();
        newTex = null;

        GenerateSpriteGo(m_spriteMat, transform.gameObject.name, m_extent_ws, m_center_ws);


        DestroyImmediate(bakeMat);
        bakeMat = null;

        DestroyImmediate(combineMat);
        combineMat = null;
        Release();

        alphaTex.Release();
        alphaTex = null;

        //DestroyImmediate(blurMat);
        //tmpBlur.Release();
        //tmpBlur = null;

        if (m_debugMesh==false)
        {
            DestroyImmediate(tempGo);
        }    
    }
    #endregion
#endif


}

public static class BoundsEx
{
    public static Bounds Transform(this Bounds bounds, Matrix4x4 matrix)
    {
        var center = matrix.MultiplyPoint3x4(bounds.center);
        var extents = bounds.extents;

        var axisX = matrix.MultiplyVector(new Vector3(extents.x, 0, 0));
        var axisY = matrix.MultiplyVector(new Vector3(0, extents.y, 0));
        var axisZ = matrix.MultiplyVector(new Vector3(0, 0, extents.z));

        extents.x = Mathf.Abs(axisX.x) + Mathf.Abs(axisY.x) + Mathf.Abs(axisZ.x);
        extents.y = Mathf.Abs(axisX.y) + Mathf.Abs(axisY.y) + Mathf.Abs(axisZ.y);
        extents.z = Mathf.Abs(axisX.z) + Mathf.Abs(axisY.z) + Mathf.Abs(axisZ.z);

        return new Bounds { center = center, extents = extents };
    }
}

