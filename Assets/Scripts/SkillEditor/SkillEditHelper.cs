using Cinemachine;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;
using War.Script;
using UnityEngine.UI;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System.IO;

//[ExecuteInEditMode]
public class SkillEditHelper : MonoBehaviour
{
    public BattlePlayer battlePlayer;
    public PlayableAsset AttackTimeline;
    public bool useMeshSimplify = false;
    public Card[] team_1 = new Card[6];
    public Card[] team_2 = new Card[6];

    Card[] team_sum = new Card[12];
    int attackerPos = -1;

    int targetNum = 6;
    List<int> defenderPos = new List<int>();

    public PlayableAsset OnHitTimeline;
    public PlayableAsset OnDeadTimeline;

    public Button[] posButton;
    public Dropdown skillDropdown;
    public Text damageText;

    private BattleActorNode[] node_sum = new BattleActorNode[12];
    private BattleActorNode attackNode = null;
    private List<BattleActorNode> defenderNodes = new List<BattleActorNode>();

    private PlayableDirector director = null;
    private List<PlayableDirector> dest_director = new List<PlayableDirector>();
    private Role[] allRole  = new Role[12];
    private EpisodeAction action = null;
    private List<EpisodeAction> hitActions = new List<EpisodeAction>();
    private BattleTracksBinding trackBinding = new BattleTracksBinding();

    private int SkilType = 0;
    private float attackDamage = 0;
    private bool isUseRefresh = false;
    public bool onebyone;

    private void Start()
    {
        War.Script.TSControllerManeger.Instance.SetTimeScaleByGame(1);
        Load();
        isUseRefresh = useMeshSimplify;
        SetMeshSimplify(useMeshSimplify);
    }

    private void OnDestroy()
    {
        Unload();
    }

    private void Update()
    {
        if (useMeshSimplify != isUseRefresh)
        {
            Debug.LogError(useMeshSimplify);
            isUseRefresh = useMeshSimplify;
            SetMeshSimplify(useMeshSimplify);
        }
    }

    private void SetMeshSimplify(bool useOMesh)
    {
        for (int i = 0; i < node_sum.Length; i++)
        {
            if (node_sum[i] != null && node_sum[i].card != null)
            {
                Debug.LogError(useMeshSimplify);
                node_sum[i].card.SetSimplifyMesh(useOMesh);
            }
        }

    }

    public void Load()
    {
        Unload();

        for (int i = 0; i < 6; i++)
        {
            team_sum[i] = team_1[i];
        }
        for (int i = 0; i < 6; i++)
        {
            team_sum[i + 6] = team_2[i];
        }

        BattleActorManager manager = battlePlayer.hub.actorManager;

        for (int i = 0; i < team_sum.Length; i++)
        {
            BattleActorNode _node = manager.nodeList[i];
            if (team_sum[i] != null)
            {
                GameObject obj = GameObject.Instantiate<GameObject>(team_sum[i].gameObject);
                IniatializeCard(obj);
                _node.AttachActor(obj);
                _node.AutoAssignRenderOrder();

                BattleHudData hudData = new BattleHudData();
                hudData.id = (uint)_node.order;
                hudData.hp = 1000;
                hudData.mp = 100;
                hudData.hp_max = 1000;
                hudData.mp_max = 50;
                hudData.level = 1;
                hudData.faction = "duoluo";
                hudData.isLargeHp = false;
                hudData.layerCount = 1;
                hudData.bossHpData.isBoss = false;
                _node.Hud.SetData(hudData);
                //_node.Hud.ResetHud();
                node_sum[i] = _node;
            }

        }
        manager.InitializeAfterLoad();

        foreach (var item in battlePlayer.hub.actorManager.nodeList)
        {
            battlePlayer.cinemachineController.Add(item.transform);
        }

    }

    void BindingRole()
    {
        attackNode = node_sum[attackerPos];
        if (attackNode != null)
        {
            for (int i = 0; i < defenderPos.Count; i++)
            {
                int targetPos = defenderPos[i];
                BattleActorNode _node = node_sum[targetPos];
                defenderNodes.Add(_node);

                PlayableDirector destDirector = _node.GetComponent<PlayableDirector>();
                dest_director.Add(destDirector);

                Role destRole = CreateTempRole((uint)_node.order, targetPos, _node.card.gameObject, _node, destDirector);
                allRole[targetPos] = destRole;
                hitActions.Add(CreateTempAction(destDirector, destRole, true));
            }


            director = attackNode.GetComponent<PlayableDirector>();
            if (director)
            {
                allRole[attackerPos] = CreateTempRole((uint)attackNode.order, attackerPos, attackNode.card.gameObject, attackNode, director);
                action = CreateTempAction(director, allRole[attackerPos]);
                OnSkillTypeChange(SkilType);

                if (attackNode && director)
                {
                    battlePlayer.internalHandlers.Remove("OnHit");
                    battlePlayer.internalHandlers.Remove("OnLuaCallback");
                    battlePlayer.internalHandlers.Add("OnHit", OnHitHandler);
                    battlePlayer.internalHandlers.Add("OnLuaCallback", OnLuaCallBackHandler);
                    
                    battlePlayer.hub.outsideManager.skillEditorCaster = (uint)attackNode.order;
                }
            }
        }
        else
        {
            Debug.LogError("选择的位置为空");
        }

    }

    private void OnHitHandler(string param, Episode ep, UnityEngine.Object bindObject)
    {
        if (onebyone)
        {
            for (int i = 0; i < dest_director.Count; i++)
            {
                if (dest_director[i])
                {
                    defenderNodes[i].Hud.ModifyHp((uint)attackNode.order, defenderNodes[i].Hud.hp - attackDamage);
                    if (defenderNodes[i].Hud.hp <= 0)
                    {
                        dest_director[i].playableAsset = OnDeadTimeline;
                        BindDefender(dest_director[i], hitActions[i]);
                        dest_director[i].Play();
                        dest_director[i] = null;
                        continue;
                    }
                    else
                    {
                        dest_director[i].playableAsset = OnHitTimeline;
                        BindDefender(dest_director[i], hitActions[i]);
                        dest_director[i].Play();
                        break;
                    }
                }
            }
        }
        else
        {
            for (int i = 0; i < dest_director.Count; i++)
            {
                if (dest_director[i])
                {
                    //defenderNodes[i].Hud.ModifyHp((uint)attackNode.order, defenderNodes[i].Hud.hp - attackDamage);
                    //if (defenderNodes[i].Hud.hp <= 0)
                    //{
                    //    dest_director[i].playableAsset = OnDeadTimeline;
                    //    BindDefender(dest_director[i], hitActions[i]);
                    //    dest_director[i].Play();
                    //    dest_director[i] = null;
                    //}
                    //else
                    //{
                        dest_director[i].playableAsset = OnHitTimeline;
                        BindDefender(dest_director[i], hitActions[i]);
                        dest_director[i].Play();
                    //}
                }
            }
        }

    }

    private void OnLuaCallBackHandler(string param, Episode ep, UnityEngine.Object bindObject)
    {
        Debug.LogError(param);
        string[] arr = param.Split('#');
        string param1 = arr[0];
        if (param1 == "change,1")
            Change_avatar_call(1);
        else if (param1 == "change,2")
            Change_avatar_call(2);
        else if (param1 == "hideBody")
            SetCardModelActive(false);
    }

    private void SetCardModelActive(bool isShow)
    {
        if (defenderPos.Count > 0)
        {
            Card card = node_sum[defenderPos[0]].card;
            card.gameObject.SetActive(isShow);
            if (card.standEffectObject)
            {
                card.standEffectObject.gameObject.SetActive(isShow);
            }
            BattleHud hud = node_sum[defenderPos[0]].Hud;
            hud.transform.GetChild(0).gameObject.SetActive(isShow);
            StartCoroutine(SetBodyShow());
        }

    }

    private IEnumerator<WaitForSeconds> SetBodyShow()
    {
        yield return new WaitForSeconds(3f);
        SetCardModelActive(true);
    }
    private void Change_avatar_call(int comInd)
    {
        Card card = allRole[attackerPos].roleGO.GetComponent<Card>();
        if (comInd == 2)
            card.animator.SetBool("IsHuman", false);
        else
            card.animator.SetBool("IsHuman", true);
    }

    public void Unload()
    {
        //foreach (var item in allRole)
        //{
        //    DestroyTempRole(item);
        //}

        if (director)
        {
            director.Stop();
        }

        foreach (PlayableDirector dire in dest_director)
        {
            dire.Stop();
        }

        foreach (BattleActorNode item in node_sum)
        {
            if (item && item.card)
            {
                item.Hud.Clear();
                DestroyImmediate(item.card.gameObject);
                item.DettachActor();
            }
        }

        battlePlayer.hub.outsideManager.Clear();
        ClearSelection();

        //source = null;
        //dest = null;
        //War.Base.Config.EnableMoveAttackBehaviour = false;
    }

    public void BindAttacker()
    {
        if (attackerPos == -1 || defenderPos.Count < 1)
        {
            Debug.LogError("请先选择攻击者和受击者");
            return;
        }
        TimelineAsset timeline = director.playableAsset as TimelineAsset;
        foreach (PlayableBinding binding in timeline.outputs)
        {
            trackBinding.Bind(director, binding, action);
        }
    }

    public void BindDefender(PlayableDirector diretor,EpisodeAction action)
    {
        if (attackerPos == -1 || defenderPos.Count < 1)
        {
            Debug.LogError("请先选择攻击者和受击者");
            return;
        }
        TimelineAsset hit_timeline = diretor.playableAsset as TimelineAsset;
        foreach (PlayableBinding binding in hit_timeline.outputs)
        {
            //Debug.LogError($"Bind dest_director>>{dest_director[i]},hitActions ={hitActions[i]}");
            trackBinding.Bind(diretor, binding, action);
        }
    }

    public void PlayDirector()
    {
        if (attackerPos == -1 || defenderPos.Count <1)
        {
            Debug.LogError("请先选择攻击者和受击者");
            return;
        }

        if (targetNum > defenderPos.Count)
        {
            targetNum = defenderPos.Count;
            foreach (Button item in posButton)
            {
                item.interactable = false;
            }

            BindingRole();
        }
        if (director)
        {
            BindAttacker();
            director.Stop();
            director.Play();
        }
    }

    Role CreateTempRole(uint id, int pos, GameObject go, BattleActorNode node, PlayableDirector director)
    {
        if(director == null) return null;
        Role role = new Role(battlePlayer);
        role.id = id;
        role.audio = node.GetComponent<AudioSource>();
        role.SetDirector(director, go);
        role.node = node;
        role.roleGO = go;

        battlePlayer.roles[id] = role;

        return role;
    }

    //void DestroyTempRole(Role role)
    //{
    //    if (role != null)
    //    {
    //        role.StopAllDirector();
    //        if (role.node)
    //        {
    //            role.node.DettachActor();
    //            role.node = null;
    //        }
    //        if (role.roleGO)
    //        {
    //            ClearCard(role.roleGO);
    //            DestroyImmediate(role.roleGO);
    //        }

    //        role = null;
    //    }
    //}

    EpisodeAction CreateTempAction(PlayableDirector director, Role role,bool isDefender = false)
    {
        if(director == null) return null;
        EpisodeAction action = new EpisodeAction();
        action.res = null;
        action.skill = director.playableAsset as TimelineAsset;
        action.caster = 1;

        List<uint> TGrounp = new List<uint>();
        if (isDefender)
        {
            TGrounp.Add((uint)attackNode.order);
        }
        else
        {
            for (int i = 0; i < defenderNodes.Count; i++)
            {
                BattleActorNode _node = defenderNodes[i];
                TGrounp.Add((uint)_node.order);
            }
        }
        action.rootSourceIds.Clear();
        for (int i = 0; i < defenderPos.Count; i++)
        {
            action.rootSourceIds.Add((uint)node_sum[(uint)defenderPos[i]].order);
        }
        action.targets.Add(TGrounp);
        action.role = role;
        return action;
    }

    void IniatializeCard(GameObject actor)
    {
        Card card = actor.GetComponent<Card>();
        if (card == null)
        {
            card = actor.AddComponent<Card>();
            card.Initialize();
        }
    }

    void ClearCard(GameObject actor)
    {
        if (actor)
        {
            Card card = actor.GetComponent<Card>();
            if (card != null)
            {
                card.Clear();
            }
        }
    }

    public void SelectRole(int index)
    {
        if(attackerPos == -1)
        {
            attackerPos = index;
            posButton[index].GetComponent<Image>().color = Color.green ;
        }
        else if(targetNum > defenderPos.Count)
        {
            defenderPos.Add(index);
            posButton[index].GetComponent<Image>().color = Color.red ;
        }

        if(targetNum == defenderPos.Count)
        {
            foreach (Button item in posButton)
            {
                item.interactable = false;
            }

            BindingRole();
        }
    }

    public void ClearSelection()
    {
        attackerPos = -1;
        //defenderPos = -1;
        defenderPos.Clear();
        hitActions.Clear();
        dest_director.Clear();
        defenderNodes.Clear();
        foreach (Button item in posButton)
        {
            item.interactable = true;
            item.GetComponent<Image>().color = Color.white;
        }
        //targetNum = int.Parse(Input_targetNum.text);
        targetNum = 6;
    }

    public void OnSkillTypeChange(int index)
    {
        if (!attackNode) return;
        SkilType = index;
        string actorName = attackNode.card.gameObject.name;
        actorName = actorName.Replace("(Clone)", "");

        string prefabResPath = "Assets/Art/Effects_Source/Timeline_battle/[name]_[skill].playable";
        string pfName = prefabResPath.Replace("[name]", actorName);

        //Debug.Log("pfName:" + pfName + " /selectValue" + selectValue + " /text=" + skillDropdown.options[selectValue].text);

        string skillType = skillDropdown.options[index].text;

        pfName = pfName.Replace("[skill]", skillType);

        if (File.Exists(pfName))
        {
#if UNITY_EDITOR
            director.playableAsset = AssetDatabase.LoadAssetAtPath<PlayableAsset>(pfName);
#endif
        }
        else
        {
            director.playableAsset = AttackTimeline;
            Debug.LogError("没有找到技能配置timeline文件：" + pfName);
        }
    }

    public void OnPlayCustomTimeline()
    {
        director.playableAsset = AttackTimeline;

        PlayDirector();
    }

    public static Action staticEditorFunc;
    public void OpenModelWin()
    { 
        if (staticEditorFunc!=null)
        {
            staticEditorFunc();
        }
    }
    
    public void SelectAttackerInScene()
    {
#if UNITY_EDITOR
        if (attackerPos == -1)
        {
            Debug.Log("请先选择攻击者");
        }
        else
        {
            BattleActorNode _node = node_sum[attackerPos];
            Selection.activeGameObject = _node.gameObject;
        }
#endif
    }

    public void ResetHUD(Role role)
    {
        role.node.Hud.ResetHud();
    }

    public void OnDamageChange(float value)
    {
        damageText.text = value.ToString();
        attackDamage = value;
    }

    public void SelectAndPlay(int index)
    {
        if (attackerPos == -1 || defenderPos.Count <1)
        {
            ClearSelection();
            SelectRole(4);
            SelectRole(6);
        }
        
        OnSkillTypeChange(index);
        PlayDirector();
    }
}
