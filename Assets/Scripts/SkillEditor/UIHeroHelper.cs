using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using War.Battle;
public class UIHeroHelper : MonoBehaviour
{
    public LightController controller;
    public int lightGroupIndex = 2;
    public Card uiActorObj;
    public UIActorNode node;
    public UIActorNodeConfig config;
    public SkillEffectConfig bodyEffect;
    GameObject obj;
    Card objCard;

    [Range(0,360)]
    public float ShadowAngle = 0;
    [Range(0, 1)]
    public float ShadowLength = 0;
    [Range(0, 1)]
    public float ShadowAlpha = 0.6f;

    private void Start()
    {
        if (obj == null || objCard == null)
        {
            obj = uiActorObj.gameObject;
            objCard = uiActorObj;
        }

    }

    //public void InstanceObject()
    //{
    //    DestroyObject();
    //    obj = Instantiate(uiActorObj.gameObject, node.transform);
    //    objCard = obj.GetComponent<Card>();
    //    objCard.Initialize();
    //    objCard.SetupShadow();
    //}



    public void PlayAnimator()
    {
        bodyEffect.transform.SetParent(node.transform, false);
        node.AttachActor(uiActorObj.gameObject);
        //node.PlayShowUIEffect(bodyEffect.gameObject);
    }

    //public void DestroyObject()
    //{
    //    if (obj == null)
    //    {
    //        objCard = node.GetComponentInChildren<Card>();
    //        if(objCard != null)
    //        {
    //            obj = objCard.gameObject;
    //        }
    //    }
    //    if (obj)
    //    {
    //        DestroyImmediate(obj);
    //    }
    //}

    float tempValue_angle = -1;
    float tempValue_length = -1;
    float tempValue_alpha = -1;

    SkinnedMeshRenderer meshRender;
    public void ChangeShadow()
    {
        if (uiActorObj == null)
        {
            return;
        }
        if (obj == null || objCard == null || meshRender == null)
        {
            obj = uiActorObj.gameObject;
            objCard = uiActorObj;
            meshRender = objCard.GetComponentInChildren<SkinnedMeshRenderer>();
        }
        

        if (tempValue_angle != ShadowAngle)
        {
            tempValue_angle = ShadowAngle;
            foreach (Material mat in meshRender.materials)
            {
                if (mat.name.Contains("Shadow"))
                {
                    mat.SetFloat("_Shadow_Rotated", tempValue_angle);
                }
            }

        }

        if (tempValue_length != ShadowLength)
        {
            tempValue_length = ShadowLength;
            foreach (Material mat in meshRender.materials)
            {
                if (mat.name.Contains("Shadow"))
                {
                    mat.SetFloat("_Shadow_Length", tempValue_length);
                }
            }
        }

        if (tempValue_alpha != ShadowAlpha)
        {
            tempValue_alpha = ShadowAlpha;
            foreach (Material mat in meshRender.materials)
            {
                if (mat.name.Contains("Shadow"))
                {
                    mat.SetColor("_Shadow_Color", new Color(0,0,0, tempValue_alpha));
                }
            }
        }
    }

    public void SaveData()
    {
        config.m_mainLightColor = controller.lightGroup[lightGroupIndex].mainLight.color;
        config.m_mainLightRota = controller.lightGroup[lightGroupIndex].mainLight.transform.rotation;

        config.m_subLightColor = controller.lightGroup[lightGroupIndex].subLight.color;
        config.m_subLightRota = controller.lightGroup[lightGroupIndex].subLight.transform.rotation;

        config.m_shadowAlpha = ShadowAlpha;
        config.m_shadowLength = ShadowLength;
        config.m_shadowAngle = ShadowAngle;
    }

    [Space]
    [Header("���߰���")]
    public Material targetMat;
    public void ChangeMat()
    {
        objCard.SetupTempMaterial(targetMat);
    }
    public void ResumeOriginMaterial()
    {
        objCard.RestoreOriginMaterial();
    }
}
