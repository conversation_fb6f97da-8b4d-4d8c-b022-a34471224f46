// using UnityEditor;
// using UnityEngine;

// [CustomEditor(typeof(RectTransform))]
// [CanEditMultipleObjects]
// public class RectTransformCustomInspector : Editor
// {
//     public override void OnInspectorGUI()
//     {
//         serializedObject.Update();
//         DrawDefaultInspector();

//         // 获取RectTransform组件
//         RectTransform rectTransform = (RectTransform)target;

//         Rect widthRect = EditorGUILayout.GetControlRect();
//         EditorGUI.BeginProperty(widthRect, new GUIContent("Width"), serializedObject.FindProperty("m_SizeDelta"));
//         EditorGUI.PropertyField(widthRect, serializedObject.FindProperty("m_SizeDelta"), new GUIContent("Width"));
//         EditorGUI.EndProperty();

//         if (Event.current.type == EventType.MouseDown && widthRect.Contains(Event.current.mousePosition))
//         {
//             Debug.Log("Width cannot be set.");
//         }
//     }
// }