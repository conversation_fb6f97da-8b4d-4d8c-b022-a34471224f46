using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GameObjectLifeTest : MonoBehaviour
{
    // Start is called before the first frame update
    void Awake()
    {
        startTime = 0;
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:Awake", this.name);
    }
    void Start()
    {
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:Start", this.name);
    }

    // Update is called once per frame
    void Update()
    {
        // Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:Update", this.name);
    }

    void OnBecameVisible()
    {
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:OnBecameVisible", this.name);
    }

    void OnBecameInvisible()
    {
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:OnBecameInvisible", this.name);
    }

    float startTime;
    void OnEnable()
    {
        startTime = Time.time;
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:OnEnable;  startTime:{1}", this.name, startTime);
    }

    void OnDisable()
    {
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:OnDisable;  durTimer:{1}", this.name, Time.time - startTime);
    }

    void OnDestroy()
    {
        Debug.LogWarningFormat("GameObjectLifeTest----name:{0};event:OnDestroy", this.name);
    }


}
