#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(SpaceExplorationMapEditHelper))]
public class SpaceExplorationMapEditHelperEditor : Editor {

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        SpaceExplorationMapEditHelper t = target as SpaceExplorationMapEditHelper;

        if (GUILayout.But<PERSON>("Load-加载"))
        {
            t.Load();
        }

        if (GUILayout.Button("Unload-卸载"))
        {
            t.Unload();
        }

        if (GUILayout.But<PERSON>("DrawPoint-画点"))
        {
            t.DrawPoint();
        }

        if (GUILayout.But<PERSON>("DrawLine-画线"))
        {
            t.DrawLine();
        }

        if (GUILayout.But<PERSON>("Save-保存（地图）"))
        {
            t.Save1();
        }

        if (GUILayout.But<PERSON>("Save-保存（科技树）"))
        {
            t.Save2();
        }

        if (GUILayout.But<PERSON>("GenerateMapGrid-生成地图网格"))
        {
            t.GenerateMapGrid();
        }

        if (GUILayout.<PERSON><PERSON>("ModifyByConfig-根据配置修改"))
        {
            t.ModifyPointByConfig();
        }
    }
}
#endif