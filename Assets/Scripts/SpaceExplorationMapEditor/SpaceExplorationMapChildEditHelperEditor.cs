#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Text;

[CustomEditor(typeof(SpaceExplorationMapChildEditHelper))]
[CanEditMultipleObjects]
public class SpaceExplorationMapChildEditHelperEditor : Editor {

    private static string id = "1001";
    private static string lineInfo = "";

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        id = EditorGUILayout.TextField("ID-星球ID", id);

        SpaceExplorationMapChildEditHelper t = target as SpaceExplorationMapChildEditHelper;

        if (GUILayout.Button("Create-创建"))
        {
            t.Create(int.Parse(id));
        }

        if (GUILayout.Button("Modify-修改"))
        {
            t.Modify(int.Parse(id));
        }

        if (GUILayout.But<PERSON>("Delete-删除"))
        {
            t.Delete();
        }

        lineInfo = EditorGUILayout.TextField("LineInfo-连线信息", lineInfo);

        if (GUILayout.Button("Link-连线"))
        {
            t.<PERSON>(lineInfo);
        }

        Dictionary<string, string> config = SpaceExplorationCsvHelper.GetConfig(t.ID);
        if(config != null)
        {
            if(config.ContainsKey("Type") && config.ContainsKey("WormholeID"))
            {
                int type = int.Parse(config["Type"]);
                if(type == 6)
                {
                    int wormholeID = int.Parse(config["WormholeID"]);
                    EditorGUILayout.LabelField("WormholeID-虫洞ID", wormholeID.ToString());

                    List<int> wormholeList = t.helper.GetWormhole(wormholeID);
                    StringBuilder wormholeListStr = new StringBuilder();
                    for(int i = 0; i < wormholeList.Count; i++)
                    {
                        wormholeListStr.Append(wormholeList[i].ToString());
                        if (i != wormholeList.Count - 1)
                        {
                            wormholeListStr.Append(",");
                        }
                    }
                    EditorGUILayout.LabelField("WormholeList-同ID虫洞列表", wormholeListStr.ToString());
                }
                else
                {
                    EditorGUILayout.LabelField("WormholeID-虫洞ID", "");
                    EditorGUILayout.LabelField("WormholeList-同ID虫洞列表", "");
                }
            }
        }
    }
}
#endif