#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

[ExecuteInEditMode]
public class SpaceExplorationMapEditHelper : MonoBehaviour {

	public GameObject prefab;
	public int mapID = 1;
	private string prefabName = "SpaceExplorationMap";
	public string savePath = "Art/Maps/SpaceExplorationMap/";

	private GameObject go;
	private List<Transform> childList = new List<Transform>();
	private Dictionary<int, Transform> childDict = new Dictionary<int, Transform>();
	private List<Transform> lineList = new List<Transform>();
	private Dictionary<string, Transform> lineDict = new Dictionary<string, Transform>();

	private Dictionary<int, int> idMap = new Dictionary<int, int>(); 

	public class LineInfo
    {
		public int begin;
		public int end;

		public LineInfo(int b, int e)
        {
			begin = b;
			end = e;
        }
    }
	private List<LineInfo> lineInfoList = new List<LineInfo>();

	private enum Operate
    {
		DrawPoint = 0,
		DrawLine = 1,
    }
	private List<Operate> operateStack = new List<Operate>();

	private bool isInit = false;

    public void Load()
    {
		if (isInit)
			return;

		SpaceExplorationCsvHelper.LoadConfig();

		go = GameObject.Instantiate(prefab, this.transform.Find("mapRoot"));
		go.name = prefabName + mapID.ToString();

		Transform lineRoot = go.transform.Find("lineRoot");
		for (int i = 0; i < lineRoot.childCount; i++)
		{
			Transform line = lineRoot.GetChild(i);
			if (!lineList.Contains(line))
				lineList.Add(line);

			SpaceExplorationMapLineEditHelper helper = line.gameObject.GetComponent<SpaceExplorationMapLineEditHelper>();
			if(helper == null)
				helper = line.gameObject.AddComponent<SpaceExplorationMapLineEditHelper>();
			helper.helper = this;

			int split = line.name.IndexOf("-");
			string beginNumber = line.name.Substring(0, split);
			string endNumber = line.name.Substring(split + 1);
			LineInfo lineInfo = new LineInfo(int.Parse(beginNumber), int.Parse(endNumber));
			helper.lineInfo = lineInfo;
			if (!lineInfoList.Contains(lineInfo))
				lineInfoList.Add(lineInfo);
			if(!lineDict.ContainsKey(line.name))
				lineDict.Add(line.name, line);
		}
		Transform childRoot = go.transform.Find("childRoot");
		for (int i = 0; i < childRoot.childCount; i++)
		{
			Transform child = childRoot.GetChild(i);
			child.GetChild(0).gameObject.SetActive(true);
			child.GetChild(1).gameObject.SetActive(true);

			if (!childList.Contains(child))
				childList.Add(child);

			int number = int.Parse(child.name);
			if (!childDict.ContainsKey(number))
				childDict.Add(number, child);

			if(!idMap.ContainsKey(number))
            {
				Text text = child.GetComponentInChildren<Text>();
				int id = int.Parse(text.text);
				idMap[number] = id;
            }
		}

		isInit = true;
	}

	public void LoadData()
    {
		Transform lineRoot = go.transform.Find("lineRoot");
		for (int i = 0; i < lineRoot.childCount; i++)
		{
			Transform line = lineRoot.GetChild(i);
			if (!lineList.Contains(line))
				lineList.Add(line);

			SpaceExplorationMapLineEditHelper helper = line.gameObject.GetComponent<SpaceExplorationMapLineEditHelper>();
			helper.helper = this;

			int split = line.name.IndexOf("-");
			string beginNumber = line.name.Substring(0, split);
			string endNumber = line.name.Substring(split + 1);
			LineInfo lineInfo = new LineInfo(int.Parse(beginNumber), int.Parse(endNumber));
			helper.lineInfo = lineInfo;
			if (!lineInfoList.Contains(lineInfo))
				lineInfoList.Add(lineInfo);
			if (!lineDict.ContainsKey(line.name))
				lineDict.Add(line.name, line);
		}
		Transform childRoot = go.transform.Find("childRoot");
		for (int i = 0; i < childRoot.childCount; i++)
		{
			Transform child = childRoot.GetChild(i);

			if (!childList.Contains(child))
				childList.Add(child);

			int number = int.Parse(child.name);
			if (!childDict.ContainsKey(number))
				childDict.Add(number, child);

			if (!idMap.ContainsKey(number))
			{
				Text text = child.GetComponentInChildren<Text>();
				int id = int.Parse(text.text);
				idMap[number] = id;
			}
		}
	}

	public void Unload()
    {
		if (!isInit)
		{
			return;
		}
		isInit = false;

		GameObject.DestroyImmediate(go);

		childList.Clear();
		childDict.Clear();
		lineList.Clear();
		lineDict.Clear();
		lineInfoList.Clear();
		operateStack.Clear();
		idMap.Clear();
	}

	public Color GetColor(int type)
    {
		Color color;
		string colorStr = "#FFFFFFFF";
		switch (type)
		{
			case 1:
				colorStr = "#FFD600FF";
				break;
			case 2:
				colorStr = "#00EDFFFF";
				break;
			case 3:
				colorStr = "#D9D2BDFF";
				break;
			case 4:
				colorStr = "#FF5353FF";
				break;
			case 5:
				colorStr = "#0EFF00FF";
				break;
			case 6:
				colorStr = "#7A00FFFF";
				break;
			default:
				break;
		}
		if (ColorUtility.TryParseHtmlString(colorStr, out color))
			return color;
		else
			return Color.white;
	}

	public void DrawPoint(int n = 0, int id = 1)
    {
		if (!isInit)
		{
			Debug.LogError("请先Load预制体!");
			return;
		}

		int number = n;
		if (n == 0)
		{ 
			if (Selection.objects.Length < 1)
			{
				Debug.LogError("请选择一个点!");
				return;
			}
			Object selection = Selection.objects[0];

			number = int.Parse(selection.name);
		}

		if (childDict.ContainsKey(number))
			return;

		GameObject child = GameObject.Instantiate(go.transform.Find("child").gameObject, go.transform.Find("childRoot"));
		child.SetActive(true);
		child.name = number.ToString();

		if (!operatePointDict.ContainsKey(number))
			GenerateMapGrid();

		child.transform.position = operatePointDict[number].position;

		int type = 0;
		Dictionary<string, string> config = SpaceExplorationCsvHelper.GetConfig(id);
		if(config != null)
        {
			if(config.ContainsKey("Type"))
            {
				type = int.Parse(config["Type"]);
            }
        }

		Image image = child.GetComponentInChildren<Image>();
		image.color = GetColor(type);

		Text text = child.GetComponentInChildren<Text>();
		text.text = id.ToString();

		War.Game.MapGrid mapGrid = child.GetComponent<War.Game.MapGrid>();
		mapGrid.eventSid = number;
		mapGrid.eventID = id;

		if (!idMap.ContainsKey(number))
			idMap.Add(number, id);
		if (!childDict.ContainsKey(number))
			childDict.Add(number, child.transform);
		if (!childList.Contains(child.transform))
			childList.Add(child.transform);
		operateStack.Add(Operate.DrawPoint);

		EditorUtility.SetDirty(go);
		Debug.Log("创建点成功：" + number.ToString() + " 数量：" + childList.Count.ToString());
	}

	public void ModifyPoint(int n, int id = 0)
    {
		if (!childDict.ContainsKey(n))
			LoadData();

		if (childDict.ContainsKey(n))
		{
			if (idMap.ContainsKey(n))
				idMap[n] = id;

			int type = 0;
			Dictionary<string, string> config = SpaceExplorationCsvHelper.GetConfig(id);
			if (config != null)
			{
				if (config.ContainsKey("Type"))
				{
					type = int.Parse(config["Type"]);
				}
			}

			Transform child = childDict[n]; 
			Image image = child.GetComponentInChildren<Image>();
			image.color = GetColor(type);

			Text text = child.GetComponentInChildren<Text>();
			text.text = id.ToString();

			War.Game.MapGrid mapGrid = child.GetComponent<War.Game.MapGrid>();
			mapGrid.eventSid = n;
			mapGrid.eventID = id;

			EditorUtility.SetDirty(go);
			Debug.Log("修改点成功：" + n.ToString());
		}
	}

	public void DeletePoint(int number)
    {
		if (!childDict.ContainsKey(number))
			LoadData();

		if (childDict.ContainsKey(number))
        {
			if (idMap.ContainsKey(number))
				idMap.Remove(number);

			Transform child = childDict[number];
			childList.Remove(child);
			childDict.Remove(number);
			GameObject.DestroyImmediate(child.gameObject);

			List<LineInfo> deleteList = new List<LineInfo>();
			foreach(var lineInfo in lineInfoList)
            {
				if (lineInfo.begin == number || lineInfo.end == number)
                {
					string lineName = lineInfo.begin.ToString() + "-" + lineInfo.end.ToString();
					if (lineDict.ContainsKey(lineName))
					{
						Transform line = lineDict[lineName];
						lineList.Remove(line);
						lineDict.Remove(lineName);

						GameObject.DestroyImmediate(line.gameObject);

						deleteList.Add(lineInfo);
					}
                }
            }
			foreach(var lineInfo in deleteList)
            {
				lineInfoList.Remove(lineInfo);
            }

			EditorUtility.SetDirty(go);
			Debug.Log("删除点成功：" + number.ToString());
		}
    }

	public void DrawLine(string info = null)
	{
		if (!isInit)
		{
			Debug.LogError("请先Load预制体!");
			return;
		}

		int beginNumber = 0;
		int endNumber = 0;

		if (info == null || info == "")
		{
			if (Selection.objects.Length != 2)
			{
				Debug.LogError("请选择两个点!");
				return;
			}
			Object selection1 = Selection.objects[0];
			Object selection2 = Selection.objects[1];

			beginNumber = int.Parse(selection1.name);
			endNumber = int.Parse(selection2.name);
		}
		else
		{
			int split = info.IndexOf("-");
			beginNumber = int.Parse(info.Substring(0, split));
			endNumber = int.Parse(info.Substring(split + 1));
		}

		if (beginNumber == endNumber)
        {
			Debug.LogError("请勿选择相同的两个点!");
			return;
		}

		int temp;
		if(beginNumber > endNumber)
        {
			temp = beginNumber;
			beginNumber = endNumber;
			endNumber = temp;
        }

		string lineName = beginNumber.ToString() + "-" + endNumber.ToString();
		string opposite = endNumber.ToString() + "-" + beginNumber.ToString();

		if (lineDict.ContainsKey(lineName) || lineDict.ContainsKey(opposite))
			return;


		LineInfo lineInfo = new LineInfo(beginNumber, endNumber);
		if (!lineInfoList.Contains(lineInfo))
			lineInfoList.Add(lineInfo);

		GameObject line = GameObject.Instantiate(go.transform.Find("line").gameObject, go.transform.Find("lineRoot"));
		line.SetActive(true);

		SpaceExplorationMapLineEditHelper helper = line.AddComponent<SpaceExplorationMapLineEditHelper>();
		helper.helper = this;
		helper.lineInfo = lineInfo;

		line.name = lineName;

		Transform beginChild = childDict[beginNumber];
		Transform endChild = childDict[endNumber];

		float dist = Vector3.Distance(beginChild.localPosition, endChild.localPosition);
		Vector3 dir = Vector3.one;
		if (beginNumber > endNumber)
			dir = beginChild.localPosition - endChild.localPosition;
		else
			dir = endChild.localPosition - beginChild.localPosition;

		line.transform.localPosition = new Vector3((beginChild.localPosition.x + endChild.localPosition.x) / 2, (beginChild.localPosition.y + endChild.localPosition.y) / 2, (beginChild.localPosition.z + endChild.localPosition.z) / 2);
		line.transform.localScale = new Vector3(dist, line.transform.localScale.y, line.transform.localScale.z);
		line.transform.eulerAngles = new Vector3(0, 0, Vector3.Angle(dir, -Vector3.right));

		if (!lineList.Contains(line.transform))
			lineList.Add(line.transform);
		if (!lineDict.ContainsKey(lineName))
			lineDict.Add(lineName, line.transform);
		operateStack.Add(Operate.DrawLine);

		EditorUtility.SetDirty(go);
		Debug.Log("创建线成功, 起点：" + beginNumber + " 终点：" + endNumber);
	}

	public void DeleteLine(string line, LineInfo info)
    {
		if (!lineDict.ContainsKey(line))
			LoadData();

		if (lineDict.ContainsKey(line))
        {
			Transform lineTran = lineDict[line];
			if (lineInfoList.Contains(info))
				lineInfoList.Remove(info);
			if (lineList.Contains(lineTran))
				lineList.Remove(lineTran);

			GameObject.DestroyImmediate(lineTran.gameObject);
			lineDict.Remove(line);
		}
    }

	public void Revoke()
	{
		if (!isInit)
		{
			Debug.LogError("请先Load预制体!");
			return;
		}

		int lastIndex = operateStack.Count;
		if (lastIndex == 0)
			return;

		Operate pop = operateStack[lastIndex - 1];
		switch(pop)
		{
			case Operate.DrawPoint:
				Transform child = childList[childList.Count - 1];
				GameObject.DestroyImmediate(child.gameObject);
				childList.RemoveAt(childList.Count - 1);
				break;
			case Operate.DrawLine:
				Transform line = lineList[lineList.Count - 1];
				GameObject.DestroyImmediate(line.gameObject);
				childList.RemoveAt(lineList.Count - 1);

				lineInfoList.RemoveAt(lineInfoList.Count - 1);
				break;
			default:
				break;
        }
		operateStack.RemoveAt(lastIndex - 1);

		EditorUtility.SetDirty(go);
	}

	public void Save1()
	{
		if (!isInit)
		{
			Debug.LogError("请先Load预制体!");
			return;
		}

		if (go == null)
			return;

		bool isContainOrigin = false;
		foreach(var number in idMap.Values)
        {
			if(number == 0)
            {
				isContainOrigin = true;
				break;
			}
        }
		if(!isContainOrigin)
        {
			EditorUtility.DisplayDialog("提示", "没有创建起点坐标，请创建起点后导出", "确定");
			return;
		}

		Dictionary<int, List<int>> dic = new Dictionary<int, List<int>>();
		foreach (var number in idMap.Keys)
        {
			if (!dic.ContainsKey(number))
				dic[number] = new List<int>();
		}
		if (lineInfoList.Count > 0)
        {
			StringBuilder text = new StringBuilder();
			text.Append("ID,StarPosition,StarID,Link\n");
			text.Append(mapID.ToString());
			text.Append(",");

			foreach (var info in lineInfoList)
            {
				if(!dic.ContainsKey(info.begin))
					dic[info.begin] = new List<int>();
				dic[info.begin].Add(info.end);

				if (!dic.ContainsKey(info.end))
					dic[info.end] = new List<int>();
				dic[info.end].Add(info.begin);
            }

			StringBuilder starPosition = new StringBuilder();
			List<int> starNumberList = new List<int>();
			foreach(var i in idMap.Keys)
            {
				starNumberList.Add(i);
            }
			starNumberList.Sort();

			StringBuilder starID = new StringBuilder();

			List<string> linkList = new List<string>();
			for (int i = 0; i < starNumberList.Count; i++)
            {
				int number = starNumberList[i];
				starPosition.Append(number);
				starID.Append(idMap[number]);
				if (i != starNumberList.Count - 1)
                {
					starPosition.Append("#");
					starID.Append("#");
				}

				if (dic.ContainsKey(number))
				{
					if (dic[number].Count == 0)
					{
						EditorUtility.DisplayDialog("提示", "坐标" + number.ToString() + "星球没有与任何星球连线，请检查", "确定");
						return;
					}
					for(int j = 0; j < dic[number].Count; j++)
                    {
						int startNumber = number;
						int endNumber = dic[number][j];
						
						if(startNumber > endNumber)
                        {
							int temp = startNumber;
							startNumber = endNumber;
							endNumber = temp;
                        }

						string linkName = startNumber + "#" + endNumber;
						if (!linkList.Contains(linkName))
							linkList.Add(linkName);
                    }
				}
            }

			StringBuilder link = new StringBuilder();
			for(int i = 0; i < linkList.Count; i++)
            {
				link.Append(linkList[i]);
                if (i != linkList.Count - 1)
                {
					link.Append(";");
                }
            }

			text.Append(starPosition.ToString());
			text.Append(",");

			text.Append(starID.ToString());
			text.Append(",");

			text.Append(link.ToString());

			if (!Directory.Exists("SpaceExplorationMapConfig"))
				Directory.CreateDirectory("SpaceExplorationMapConfig");

			File.WriteAllText("SpaceExplorationMapConfig/" + prefabName + mapID.ToString() + ".csv", text.ToString());



			Transform childRoot = go.transform.Find("childRoot");
			for (int i = 0; i < childRoot.childCount; i++)
			{
				Transform child = childRoot.GetChild(i);
				child.GetChild(0).gameObject.SetActive(false);
				child.GetChild(1).gameObject.SetActive(false);
			}

			Transform lineRoot = go.transform.Find("lineRoot");
			for (int i = 0; i < lineRoot.childCount; i++)
			{
				Transform line = lineRoot.GetChild(i);
				RectTransform rect = line.GetComponent<RectTransform>();
				if(rect != null)
                {
					rect.sizeDelta = new Vector2(1f, 10f);
                }
				Image image = line.GetComponent<Image>();
				if(image != null)
                {
					image.color = Color.white;

				}
				SpaceExplorationMapLineEditHelper helper = line.gameObject.GetComponent<SpaceExplorationMapLineEditHelper>();
				DestroyImmediate(helper);
			}

			string prefabSavePath = savePath + prefabName + mapID.ToString() + ".prefab";
			if (File.Exists(prefabSavePath))
			{
				if (!EditorUtility.DisplayDialog("提示", "已存在相同名称预制体，是否覆盖预制体？", "是", "否"))
				{
					Debug.LogError("预制体已存在，请修改预制体名称!");
					return;
				}
			}

			PrefabUtility.CreatePrefab(prefabSavePath, go);
			AssetDatabase.SaveAssets();

			Debug.Log("保存预制体成功! 导出配置成功!");

			Unload();
		}
        else
		{
			EditorUtility.DisplayDialog("提示", "地图没有任何星球连线，请检查", "确定");
		}
	}

	public void Save2()
	{
		if (!isInit)
		{
			Debug.LogError("请先Load预制体!");
			return;
		}

		if (go == null)
			return;

		Dictionary<int, List<string>> dic = new Dictionary<int, List<string>>();
		foreach (var number in idMap.Values)
		{
			if (number == 0)
				continue;
			if (!dic.ContainsKey(number))
				dic[number] = new List<string>();
		}
		if (lineInfoList.Count > 0)
		{
			StringBuilder text = new StringBuilder();

			foreach (var info in lineInfoList)
			{
				int beginID = idMap[info.begin];
				int endID = idMap[info.end];

				if (beginID != 0)
				{
					if (!dic.ContainsKey(beginID))
					{
						dic[beginID] = new List<string>();
					}
					if (endID != 0)
					{
						dic[beginID].Add(endID.ToString());
					}
				}

				if (endID != 0)
				{
					if (!dic.ContainsKey(endID))
					{
						dic[endID] = new List<string>();
					}
					if (beginID != 0)
					{
						dic[endID].Add(beginID.ToString());
					}
				}
			}

			foreach(var pairs in dic)
            {
				if (pairs.Key != 0)
				{
					text.Append(pairs.Key.ToString() + "01");
					text.Append(",");
					text.Append(string.Join("#", pairs.Value.ToArray()));
					text.Append("\n");
				}
            }


			if (!Directory.Exists("SpaceExplorationMapConfig"))
				Directory.CreateDirectory("SpaceExplorationMapConfig");

			File.WriteAllText("SpaceExplorationMapConfig/StarScience" + mapID.ToString() + ".csv", text.ToString());

			string prefabSavePath = savePath + prefabName + mapID.ToString() + ".prefab";
			if (File.Exists(prefabSavePath))
			{
				if (!EditorUtility.DisplayDialog("提示", "已存在相同名称预制体，是否覆盖预制体？", "是", "否"))
				{
					Debug.LogError("预制体已存在，请修改预制体名称!");
					return;
				}
			}

			PrefabUtility.CreatePrefab(prefabSavePath, go);
			AssetDatabase.SaveAssets();

			Debug.Log("保存预制体成功! 导出配置成功!");
		}
		else
		{
			EditorUtility.DisplayDialog("提示", "没有任何科技连线，请检查", "确定");
		}
	}

	public int width = 40;
	public int height = 40;

	private float xoffset = 540f;
	private float yoffset = 960f;

	private float yGridSize = 86.6f;
	private float xGridSize = 100f;

	void OnDrawGizmos()
    {
		DrawGizmos();
	}

	private Dictionary<int, Transform> operatePointDict = new Dictionary<int, Transform>();

	private Transform operatePointRoot;
	private Transform operatePointTemplate;
	public void DrawGizmos()
    {
		Gizmos.color = new Color(1, 1, 1, 0.3f);

		float xmid = (width + 1) / 2.0f;
		for (int x = 0; x < width; x++)
		{
			float y = (xmid - x - 1) * yGridSize;
			Gizmos.DrawLine(new Vector3(xGridSize * width * -0.5f + xoffset - 100f, y + yoffset, 0), new Vector3(xGridSize * width * 0.5f + xoffset + 100f, y + yoffset, 0));
			Handles.Label(new Vector3(xGridSize * width * -0.5f + xoffset + 5f - 100f, y + yoffset + 70f, 0), (x+1).ToString());
		}

		float ymid = (height + 1) / 2.0f;
		for (int y = 0; y < height; y++)
		{
			for (int x = 0; x < width; x++) 
            {
				bool draw = true;
				float beginX = 0;
				float endX = 0;
				if(x % 2 == 0)
                {
					beginX = (y + 1 - ymid) * xGridSize + xoffset;
					endX = (y + 1 - ymid) * xGridSize + xoffset - xGridSize * 0.5f;
				}
				else
                {
					beginX = (y + 1 - ymid) * xGridSize + xoffset - xGridSize * 0.5f;
					endX = (y + 1 - ymid) * xGridSize + xoffset;
				}
				if (x == width - 1)
					draw = false;
				if (draw)
					Gizmos.DrawLine(new Vector3(beginX, (xmid - x - 1) * yGridSize + yoffset, 0), new Vector3(endX, (xmid - x - 2) * yGridSize + yoffset, 0));
			}
			Handles.Label(new Vector3((y + 1 - ymid) * xGridSize + xoffset, yGridSize * width * 0.5f + yoffset + 20f, 0), (y + 1).ToString());
		}
	}

	public void DeleteMapGrid()
	{
		while(operatePointRoot.childCount > 0)
		{
			GameObject.DestroyImmediate(operatePointRoot.GetChild(0).gameObject);
		}
		operatePointDict.Clear();
	}

	public void GenerateMapGrid()
    {
		if (operatePointRoot == null)
			operatePointRoot = this.transform.Find("operatePointRoot");
		if (operatePointTemplate == null)
			operatePointTemplate = this.transform.Find("operatePoint");

		DeleteMapGrid();

		float xmid = (width + 1) / 2.0f;
		float ymid = (height + 1) / 2.0f;
		for (int y = 0; y < height; y++)
		{
			for (int x = 0; x < width; x++)
			{
				float beginX = 0;
				if (x % 2 == 0)
				{
					beginX = (y + 1 - ymid) * xGridSize + xoffset;
				}
				else
				{
					beginX = (y + 1 - ymid) * xGridSize + xoffset - xGridSize * 0.5f;
				}

				int number = ((x + 1) * 1000 + (y + 1));
				if (!operatePointDict.ContainsKey(number))
				{
					GameObject operatePoint = GameObject.Instantiate(operatePointTemplate.gameObject, operatePointRoot);
					operatePoint.SetActive(true);
					operatePoint.name = number.ToString();
					operatePoint.transform.position = new Vector3(beginX, (xmid - x - 1) * yGridSize + yoffset, 0);
					operatePointDict.Add(number, operatePoint.transform);

					SpaceExplorationMapChildEditHelper helper = operatePoint.AddComponent<SpaceExplorationMapChildEditHelper>();
					helper.helper = this;
					helper.number = number;
				}
			}
		}
	}

	public List<int> GetWormhole(int wormholeID)
    {
		List<int> list = new List<int>();
		foreach (var pairs in idMap)
		{
			int id = pairs.Value;
			Dictionary<string, string> config = SpaceExplorationCsvHelper.GetConfig(id);
			if (config != null)
			{
				if (config.ContainsKey("Type") && config.ContainsKey("WormholeID"))
				{
					int type = int.Parse(config["Type"]);
					if (config["WormholeID"] != "")
					{
						int WormholeID = int.Parse(config["WormholeID"]);
						if (type == 6 && wormholeID == WormholeID && !list.Contains(pairs.Key))
						{
							list.Add(pairs.Key);
						}
					}
				}
			}
		}
		return list;
    }

	public void ModifyPointByConfig()
	{
		string path = "SpaceExplorationMapConfig/" + prefabName + mapID.ToString() + ".csv";
		if(!File.Exists(path))
        {
			EditorUtility.DisplayDialog("提示", path + "文件不存在", "确定");
			return;
		}
		string[] lines = File.ReadAllLines(path);
		string[] content = lines[1].Split(',');
		string[] position = content[1].Split('#');
		string[] starID = content[2].Split('#');
		for (int i = 0; i < position.Length; i++) 
        {
			ModifyPoint(int.Parse(position[i]), int.Parse(starID[i]));
        }
	}
}
#endif
