#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

public static class SpaceExplorationCsvHelper {

    private static Dictionary<int, Dictionary<string, string>> record = new Dictionary<int, Dictionary<string, string>>();
	public static void LoadConfig()
    {
        record.Clear();

        string path = "../../Tools/csv_script/StarExploreStars.csv";
        if (File.Exists(path))
        {
            string[] lines = File.ReadAllLines(path);
            List<string> parameters = new List<string>();
            for (int i = 0; i < lines.Length; i++) 
            {
                string line = lines[i];
                string[] split = line.Split(',');
                if (i == 0)
                { 
                    for(int j = 1; j < split.Length; j++)
                    {
                        parameters.Add(split[j]);
                    }
                }
                else if(i >= 7)
                {
                    int id = int.Parse(split[0]);
                    if (!record.ContainsKey(id))
                    {
                        record[id] = new Dictionary<string, string>();
                        for (int j = 1; j < split.Length; j++)
                        {
                            record[id].Add(parameters[j-1], split[j]);
                        }
                    }
                }
            }
        }
    }

    public static Dictionary<string, string> GetConfig(int id)
    {
        if (record.ContainsKey(id))
            return record[id];
        else
            return null;
    }
}
#endif
