#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(SpaceExplorationMapLineEditHelper))]
public class SpaceExplorationMapLineEditHelperEditor : Editor {
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        SpaceExplorationMapLineEditHelper t = target as SpaceExplorationMapLineEditHelper;

        if (GUILayout.Button("Delete-删除"))
        {
            t.helper.DeleteLine(t.gameObject.name, t.lineInfo);
        }
    }
}
#endif
