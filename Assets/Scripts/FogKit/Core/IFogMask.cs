/*******************************************************
 * Description:   #请描述功能
 * Author:        袁楠
 * Created Date:  2024年8月9日
 ********************************************************/

using System;

namespace Fog
{
    public interface IFogMask
    {
        /// <summary>
        /// 创建迷雾Map
        /// </summary>
        /// <param name="width"> 宽 </param>
        /// <param name="height"> 高 </param>
        void CreateFogMap(int width, int height);
        /// <summary>
        /// 更新迷雾Map
        /// </summary>
        /// <param name="baseData"> 更新的数据 </param>
        void UpdateFogMap(int[] data);
        /// <summary>
        /// 立刻刷新迷雾地图（立刻完成颜色缓冲区的色彩渐变）
        /// </summary>
        void RefreshFogMap();
        /// <summary>
        /// 清除迷雾地图
        /// </summary>
        void ClearFogMap();
    }
}