using UnityEngine;
using Unity.Collections;
using XLua;

namespace Fog
{
    /// <summary>
    /// 迷雾地图纹理, 因为要适配多边形网格,所以先进行全数组遍历处理,以后如果Mask网格比较细,考虑使用四分法,细化需要遍历的网格范围
    /// 通过透明度 0-1 的渐变实现迷糊的缓动效果
    /// </summary>
    internal sealed class FogMaskTexture
    {
        private readonly Color baseColor = new Color(0, 0, 0, 0f);
        private readonly Color targetColor = new Color(0, 0, 0, 1f);
        private readonly int _width;
        private readonly int _height;

        private Texture2D _maskTexture;
        private NativeArray<Color32> _maskColors;
        //private byte[] _maskColorCaches;
        //private float[] _maskColorElapsed;

        //private bool _hasCache = false;

        [System.NonSerialized]
        public bool allmask = false;

        float _FadeTime;

        // private List<FogMaskColorCache> _maskColorCaches;

        public FogMaskTexture(int width, int height)
        {
            _width = width;
            _height = height;
            //_maskColors = new NativeArray<Color>(width * height, Allocator.Persistent);
            //_maskColorCaches = new byte[width * height];
            //_maskColorElapsed = new float[width * height];
            _maskTexture = new Texture2D(width, height, TextureFormat.RGBA32, false, true)
            {
                wrapMode = TextureWrapMode.Clamp
            };
            _maskColors = _maskTexture.GetPixelData<Color32>(0);
            for (int i = 0; i < _maskColors.Length; i++)
                _maskColors[i] = baseColor;

            _maskTexture.Apply(false, false);
        }

        public Texture2D GetTexture()
        {
            return _maskTexture;
        }

        //public void Update(float deltaTime)
        //{
        //    if (_hasCache)
        //    {
        //        _hasCache = false;
        //        for (int i = 0; i < _maskColorElapsed.Length; i++)
        //        {
        //            if (_maskColorElapsed[i] <= 0f) continue;
        //            _maskColorElapsed[i] = Mathf.Clamp01(_maskColorElapsed[i] - deltaTime);
        //            float t = 1 - _maskColorElapsed[i];
        //            if (_maskColorCaches[i] == 0)
        //            {
        //                Color color = Color.Lerp(targetColor, baseColor, t);
        //                _maskColors[i] = color;
        //            }
        //            else
        //            {
        //                Color color = Color.Lerp(baseColor, targetColor, t);
        //                _maskColors[i] = color;
        //            }

        //            _hasCache = true;
        //        }

        //        //_maskTexture.SetPixelData(_maskColors, 0);
        //        _maskTexture.Apply(false, false);
        //    }
        //}

        public void UpdateColorCache(LuaArrAccess mapData)
        {
            // todo , 上次没完成的 fade 动画
            if(Time.time - _FadeTime < 1.0)
            {

            }
            _FadeTime = Time.time;
            allmask = true;
            for (int i = 0; i < _maskColors.Length; i++)
            {
                byte v = (byte)(mapData.GetInt(i + 1) * 255);

                Color32 mask = _maskColors[i];
                mask.b = mask.a;
                mask.a = v;
                allmask &= v >= 255;
                _maskColors[i] = mask;
            }

            _maskTexture.Apply(false, false);
            // _hasCache = true;
        }

        public void RefreshColorCache()
        {
           // Update(1);
            //_maskTexture.Apply(false, false);
            //_hasCache = false;
        }

        public void Release()
        {
            if (_maskTexture != null)
                UnityEngine.Object.Destroy(_maskTexture);
            _maskTexture = null;
            //_hasCache = false;
        }
    }
}