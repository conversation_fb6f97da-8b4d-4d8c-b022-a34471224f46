using System;
using UnityEngine;

namespace Fog
{
    public sealed class FogMaskData
    {
        public int Width => _mapData.GetLength(0);
        public int Height => _mapData.GetLength(1);

        private readonly byte[] _mapData;

        public FogMaskData(int width, int height)
        {
            _mapData = new byte[width * height];
        }
        
        public byte[] GetMapData()
        {
            return _mapData;
        }
        
        public int GetMapDataNode(int x, int y)
        {
            ValidateCoordinates(x, y);
            return _mapData[y * Width + x];
        }

        public void SetMapData(int[] data)
        {
            for (int i = 0; i < data.Length; i++)
            {
                _mapData[i] = (byte)data[i];
            }
        }

        public void SetMapDataNode(int x, int y, int state)
        {
            ValidateCoordinates(x, y);
            _mapData[y * Width + x] = (byte)state;
        }

        public void ClearMapData()
        {
            Array.Clear(_mapData, 0, _mapData.Length);
        }

        private void ValidateCoordinates(int x, int y)
        {
            if (x < 0 || x >= _mapData.GetLength(0) || y < 0 || y >= _mapData.GetLength(1))
            {
                FogDebug.LogError($"Coordinates ({x}, {y}) are out of bounds.");
            }
        }
    }
}