using UnityEngine;

namespace Fog
{
    /// <summary>
    /// 迷雾使用debug工具类
    /// </summary>
    public class FogDebug
    {
        [System.Diagnostics.Conditional ("UNITY_EDITOR")]
        public static void Log (string obj)
        {
            Debug.Log ("[Fog]" + obj);
        }
        
        [System.Diagnostics.Conditional ("UNITY_EDITOR")]
        public static void LogError (string obj)
        {
            Debug.Log ("[Fog]" + obj);
        }
        
        [System.Diagnostics.Conditional ("UNITY_EDITOR")]
        public static void LogAllocSize (string before, object baseSize, int num)
        {
            Log ("[Alloc][Size]" + before + GetSize (baseSize, num));
        }

        private static string GetSize (object baseSize, int num)
        {
            double res = System.Runtime.InteropServices.Marshal.SizeOf (baseSize) * num;
            if (res > 1024) {
                res /= 1024;
            } else {
                return $"{res:####.###} B";
            }
            if (res > 1024) {
                res /= 1024;
            } else {
                return $"{res:####.###} KB";
            }
            if (res > 1024) {
                res /= 1024;
            } else {
                return $"{res:####.###} MB";
            }
            if (res > 1024) {
                res /= 1024;
            } else {
                return $"{res:####.###} GB";
            }
            return $"{res:F3} TB";
        }
    }
}