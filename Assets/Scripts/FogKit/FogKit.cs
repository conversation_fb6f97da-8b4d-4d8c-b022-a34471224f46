using System;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.Assertions;
using War.Base;
using War.Render;
using XLua;


namespace Fog
{
    /// <summary>
    /// 迷雾地图Fog Kit
    /// Map 原点为左下角（0，0）
    /// </summary>
    public class FogKit : MonoBehaviour
    {
        public MeshRenderer meshRenderer;
        public string propName = "_AlphaTex";

        public GameObject planeRoot;
        private Vector3 m_PreTerrainPos;

        private int _FadeTime = Shader.PropertyToID("_FadeTime");
        private int _BlurOffset = Shader.PropertyToID("_BlurOffset");

        private Vector4 _FadeStartTime;
        public Material m_BlurMaterial = null;
        public float blurOffset = 1f;
        private bool m_UseCreateMat;

        //private FogMaskData _fogMaskData;
        private FogMaskTexture _fogMaskTexture;

        RenderTexture m_Fowtexture;
        public void CreateFogMap(int width, int height)
        {
            //FogDebug.Log("CreateFogMap: " + width + ", " + height);
            //_fogMaskData = new FogMaskData(width, height);
            _fogMaskTexture = new FogMaskTexture(width, height);

            m_Fowtexture = new RenderTexture(width, height, 0, RenderTextureFormat.R8);
            m_Fowtexture.wrapMode = TextureWrapMode.Clamp;
            m_Fowtexture.filterMode = FilterMode.Bilinear;
            m_Fowtexture.Create();

            meshRenderer.sharedMaterial.SetTexture(propName, m_Fowtexture);
            SyncPlanesShaderData();
            m_PreTerrainPos = this.transform.position;
        }

      

        public void UpdateFogMap(LuaArrAccess data)
        {
            // "".PrintLuaError("===fortest=== UpdateFogMap");
            //FogDebug.Log("UpdateFogMap: " + data.);
            //_fogMaskData.SetMapData(data);
            _FadeStartTime.x = Time.time;
            _FadeStartTime.y = Time.time;
            meshRenderer.sharedMaterial.SetVector(_FadeTime, _FadeStartTime);
            _fogMaskTexture.UpdateColorCache(data);
        }


        public void RefreshFogMap()
        {
            _fogMaskTexture.RefreshColorCache();
        }

        /// <summary>
        /// 清除迷雾地图
        /// </summary>
        public void ClearFogMap()
        {
            //_fogMaskData.ClearMapData();
            _fogMaskTexture.Release();
            meshRenderer.sharedMaterial.SetTexture(propName, null);

            if (planeRoot)
            {
                var planes = planeRoot.GetComponentsInChildren<MeshRenderer>();
                foreach (var plane in planes)
                {
                    plane?.sharedMaterial.SetTexture("_FogMaskMap", null);
                }
            }
        }

        private void Awake()
        {
            Assert.IsNotNull(meshRenderer);
        }
        
        #if UNITY_EDITOR
        private void OnValidate()
        {
            _FadeStartTime.x = Time.time;
            
            Debug.Log("===fortest=== UpdateFogMap 2222");
        }
        #endif

        private void Update()
        {
            if (m_Fowtexture == null || _fogMaskTexture == null || _fogMaskTexture.allmask)
            {
                if (meshRenderer)
                {
                    meshRenderer.enabled = false;
                }
                return;
            }

            if (meshRenderer && !meshRenderer.enabled)
            {
                meshRenderer.enabled = true;
            }

            if (Vector3.Distance(m_PreTerrainPos, this.transform.position) > 0.01f)
            {
                SyncPlanesShaderData();
                m_PreTerrainPos = this.transform.position;
                //Debug.Log("===fortest=== 更新matrix");
            }
            
            if (m_BlurMaterial == null)
            {
                var shader = ShaderCollectionWarmUp.Find("FogMap/GaussianBlur");
                if (shader != null)
                {
                    m_BlurMaterial = new Material(shader);
                    m_UseCreateMat = true;
                }
            }

            if (m_BlurMaterial == null)
                return;

            if (Time.time - _FadeStartTime.x > 2.0f)
                return;

            _FadeStartTime.y = Time.time;

            var rt = RenderTexture.GetTemporary(m_Fowtexture.width, m_Fowtexture.height, 0, RenderTextureFormat.R8);


            m_BlurMaterial.SetFloat(_BlurOffset, blurOffset);
            m_BlurMaterial.SetVector(_FadeTime, _FadeStartTime);
            Graphics.Blit(_fogMaskTexture.GetTexture(), rt, m_BlurMaterial, 0);
            m_BlurMaterial.SetTexture("_FogMaskTex",_fogMaskTexture.GetTexture());
            Graphics.Blit(rt, m_Fowtexture, m_BlurMaterial, 1);

            RenderTexture.ReleaseTemporary(rt);
            
            //_fogMaskTexture?.Update(Time.deltaTime);
        }

        private void SyncPlanesShaderData()
        {
            if (planeRoot == null)
                return;

            var planes = planeRoot.GetComponentsInChildren<MeshRenderer>(true);
            foreach (var plane in planes)
            {
                plane?.sharedMaterial.SetTexture("_FogMaskMap", m_Fowtexture);
                plane?.sharedMaterial.SetMatrix("_FogMapMatrix", this.transform.worldToLocalMatrix);
            }
        }

        private void OnDestroy()
        {
           // _fogMaskData?.ClearMapData();
            _fogMaskTexture?.Release();
            m_Fowtexture?.Release();
            if (m_BlurMaterial && m_UseCreateMat)
            {
                GameObject.DestroyImmediate(m_BlurMaterial);
                m_UseCreateMat = false;
            }
        }
        
        #if UNITY_EDITOR
        [ContextMenu("Save FogMask")]
        public void SaveFogMaskRT()
        {
            if (!EditorApplication.isPlaying)
            {
                Debug.LogError("运行时才可用");
                return;
            }
            
            if (m_Fowtexture == null)
                return;
            
            var tex2d = new Texture2D(m_Fowtexture.width, m_Fowtexture.height, TextureFormat.R8, false);
            var rect = Rect.MinMaxRect(0f, 0f, tex2d.width, tex2d.height);
            RenderTexture.active = m_Fowtexture;
            tex2d.ReadPixels(rect, 0, 0);
            RenderTexture.active = null;
            
            Graphics.CopyTexture(m_Fowtexture, tex2d);

            string outpath = "Assets/FogMaskTest(美术测试用 可删除).asset";
            UnityEditor.AssetDatabase.CreateAsset(tex2d,outpath);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
           
            Debug.Log($"保存成功,输出路径{outpath}");
        }
        #endif

    }
}