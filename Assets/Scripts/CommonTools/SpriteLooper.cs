namespace CommonTools
{
    using UnityEngine;

    public class SpriteLooper : MonoBehaviour
    {
        public float speed = 1.0f;

        private SpriteRenderer[] spriteRenderers;
        private float[] widths;

        void Start()
        {
            // 获取所有SpriteRenderer组件
            spriteRenderers = GetComponentsInChildren<SpriteRenderer>();
            widths = new float[spriteRenderers.Length];

            // 计算每个SpriteRenderer的宽度
            for (int i = 0; i < spriteRenderers.Length; i++)
            {
                widths[i] = spriteRenderers[i].sprite.bounds.size.x * spriteRenderers[i].transform.localScale.x;
            }
        }

        void LateUpdate()
        {
            for (int i = 0; i < spriteRenderers.Length; i++)
            {
                // 将每个SpriteRenderer向左移动
                spriteRenderers[i].transform.localPosition += Vector3.left * speed * Time.deltaTime;

                // 如果SpriteRenderer超出视图范围，则将其移到最右边
                if (spriteRenderers[i].transform.localPosition.x < -widths[i]*1.5)
                {
                    float farRightPosition = GetFarRightPosition();
                    spriteRenderers[i].transform.localPosition = new Vector3(4*widths[i] + spriteRenderers[i].transform.localPosition.x, 0, 0);
                }
            }
        }

        private float GetFarRightPosition()
        {
            float farRight = float.MinValue;

            // 找到当前所有SpriteRenderers中最右边的位置
            foreach (var spriteRenderer in spriteRenderers)
            {
                float edgePosition = spriteRenderer.transform.position.x + spriteRenderer.bounds.size.x/2;
                if (edgePosition > farRight)
                {
                    farRight = edgePosition;
                }
            }

            return farRight;
        }
    }


}