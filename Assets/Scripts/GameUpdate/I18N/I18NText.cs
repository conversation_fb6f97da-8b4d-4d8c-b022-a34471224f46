using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;

[RequireComponent(typeof(Text))]
public class I18NText : MonoBehaviour {
    public string i18nKey {
        get
        {
            return _i18nKey;
        }
        set
        {
            _i18nKey = value;
            SetText(_i18nKey);
        }
    }

    [SerializeField]
    private string _i18nKey;

    private Text _text;
	// Use this for initialization
	void Awake () {
        _text = this.GetComponent<Text>();
	}

    private void Start()
    {
         SetText(_i18nKey);
    }

    void SetText(string key)
    {
        if (key != null || key != string.Empty)
        {
            _text.text = I18NManager.GetText(key);
        }
        else
        {
            _text.text = string.Empty;
        }
    }
}
