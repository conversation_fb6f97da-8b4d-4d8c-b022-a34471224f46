using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public static class I18NConfig {
    private static Dictionary<SystemLanguage, string> SupportedLanguages = new Dictionary<SystemLanguage, string>();
    private static bool _init = false;

    public static string GetLanguageResPath(SystemLanguage language)
    {
        if (!_init)
        {
            _init = true;
            Load();
        }
        string path;
        SupportedLanguages.TryGetValue(language, out path);
        return path;
    }
    private static void Load()
    {
        // 默认英文, 需要加其他语言支持就往后加
        /*
         * public enum SystemLanguage
    {
        Afrikaans = 0,
        Arabic = 1,
        Basque = 2,
        Belarusian = 3,
        Bulgarian = 4,
        Catalan = 5,
        Chinese = 6,
        Czech = 7,
        Danish = 8,
        Dutch = 9,
        English = 10,
        Estonian = 11,
        Faroese = 12,
        Finnish = 13,
        French = 14,
        German = 15,
        Greek = 16,
        Hebrew = 17,
        Hugarian = 18,
        Hungarian = 18,
        Icelandic = 19,
        Indonesian = 20,
        Italian = 21,
        Japanese = 22,
        Korean = 23,
        Latvian = 24,
        Lithuanian = 25,
        Norwegian = 26,
        Polish = 27,
        Portuguese = 28,
        Romanian = 29,
        Russian = 30,
        SerboCroatian = 31,
        Slovak = 32,
        Slovenian = 33,
        Spanish = 34,
        Swedish = 35,
        Thai = 36,
        Turkish = 37,
        Ukrainian = 38,
        Vietnamese = 39,
        ChineseSimplified = 40,
        ChineseTraditional = 41,
        Unknown = 42
    }
         * */
        SupportedLanguages.Add(SystemLanguage.ChineseSimplified, "i18n/zh_CN");  // 简体中文
        SupportedLanguages.Add(SystemLanguage.Chinese, "i18n/zh_CN");  // 简体中文
		SupportedLanguages.Add(SystemLanguage.ChineseTraditional, "i18n/zh_HK ");  // 繁体中文
		SupportedLanguages.Add(SystemLanguage.English, "i18n/en_US");  // 英语
		SupportedLanguages.Add(SystemLanguage.Indonesian, "i18n/id_ID");  // 印尼语
		SupportedLanguages.Add(SystemLanguage.Vietnamese, "i18n/vn_VN");  // 越南语
		SupportedLanguages.Add(SystemLanguage.Thai, "i18n/th_TH");  // 泰语
		SupportedLanguages.Add(SystemLanguage.Korean, "i18n/th_KR");  // 韩语
		SupportedLanguages.Add(SystemLanguage.Japanese, "i18n/th_JA");  // 日语
        SupportedLanguages.Add(SystemLanguage.Dutch, "i18n/nl_NL");  // 荷兰语

        //..
    }

}
