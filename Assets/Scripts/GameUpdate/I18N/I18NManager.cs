using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class I18NManager : MonoBehaviour {

    private Dictionary<string, object> _default;
    private Dictionary<string, Dictionary<string, object>> _languages = new Dictionary<string, Dictionary<string, object>>();
    public static I18NManager Instance;
	// Use this for initialization
	void Awake () {
        Instance = this;
        var textAsset = Resources.Load<TextAsset>("i18n/default");
        _default = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
        if (_default == null)
        {
            Debug.LogError("解析国际化文本文件失败 i18n/default");
            _default = new Dictionary<string, object>();
        }
	}

    // 根据当前系统语言获得字符串集
    private Dictionary<string, object> GetCurrStrings()
    {
        string resPath = I18NConfig.GetLanguageResPath(Application.systemLanguage);
        if (resPath != null)
        {
            Dictionary<string, object> lang;
            if (_languages.TryGetValue(resPath, out lang))
            {
                return lang;
            }

            var textAsset = Resources.Load<TextAsset>(resPath);
            if (textAsset != null)
            {
                lang = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
                if (lang != null)
                {
                    _languages.Add(resPath, lang);
                    return lang;
                }
            }
        }
        return _default;
    }
    private string _GetText(string key)
    {
        Dictionary<string, object> lang = GetCurrStrings();
        object text;
        if (lang.TryGetValue(key, out text))
        {
            return System.Convert.ToString(text);
        }

        if (!System.Object.ReferenceEquals(lang, _default))
        {
            if (_default.TryGetValue(key, out text))
            {
                return System.Convert.ToString(text);
            }
        }
        return key;
    }

    public static string GetText(string key)
    {
        try
        {
            if (Instance == null)
            {
                Debug.LogWarningFormat("国际化化异常:"+ key);
                return key;
            }
            return Instance._GetText(key);
        }
        catch(System.Exception e)
        {
            Debug.LogWarningFormat("国际化化异常 {0} - {1}", key, e.Message);
            return key;
        }
    }
}
