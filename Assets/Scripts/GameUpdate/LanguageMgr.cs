using System;
using System.Collections.Generic;
using UnityEngine.UI;
using TMPro;

namespace bc.Lang
{
    public class LanguageMgr
    {
        public string currentArea = "";
        public string Lang(string key)
        {
            string str = "";
            switch (currentArea)
            {
                case "English":
                return LangEnglishDic[key];
                case "Portuguese":
                return LangPortugueseDic[key];
                case "Japanese":
                return LangJapaneseDic[key];
                case "Russian":
                return LangRussianDic[key];
                case "Finnish":
                return LangFinnishDic[key];
                case "German":
                return LangGermanDic[key];
                case "French":
                return LangFrenchDic[key];
                case "Thai":
                return LangThaiDic[key];
                case "Indonesian":
                return LangIndonesianDic[key];
                case "Spanish":
                return LangSpanishDic[key];
                case "Korean":
                return LangKoreanDic[key];
            }
            return "";
        }

        private Dictionary<string,string> LangEnglishDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangPortugueseDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangJapaneseDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangRussianDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangFinnishDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangGermanDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangFrenchDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangThaiDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangIndonesianDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangSpanishDic = new Dictionary<string,string>();
        private Dictionary<string,string> LangKoreanDic = new Dictionary<string,string>();
        private bool IsDone = false;
        
        public void Init(){
            LangText.regist_action = (lt)=>{
                LanguageMgr.Instance.RegisterCB(()=>{SetText(lt as LangText);});
            };
        }
        public void SetText(LangText lt){
            var key = lt.key;
            string ks = LanguageMgr.Instance.Lang(key);
            var txt = lt.GetComponent<Text>();
            if(txt == null)lt.GetComponent<TextMeshProUGUI>().text  = ks;
            else txt.text = ks;
        }

        List<System.Action> cbList = new List<Action>();
        public void RegisterCB(System.Action cb){
            if (IsDone)
            { 
                cb();
                return;
            }
            cbList.Add(cb);
        }
        
        public void Parsing(string s , string t)
        {
            currentArea = t;
            string[] line  = s.Split(new string[] { "\r\n" },System.StringSplitOptions.RemoveEmptyEntries);
            string[] Firstline  = line[0].Split(',');
            for (int i = 0; i < line.Length-1; i++)
            {
                if(i == 0) continue;
                string[] sline = line[i].Split(',');
                for (int j = 0; j < sline.Length-1; j++)
                {
                    if(j == 0) continue;
                    if(Firstline[j] == "English")
                        if (!LangEnglishDic.ContainsKey(sline[0]))
                            LangEnglishDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Portuguese")
                        if (!LangPortugueseDic.ContainsKey(sline[0]))
                            LangPortugueseDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Japanese")
                        if (!LangJapaneseDic.ContainsKey(sline[0]))
                            LangJapaneseDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Russian")
                        if (!LangRussianDic.ContainsKey(sline[0]))
                            LangRussianDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Finnish")
                        if (!LangFinnishDic.ContainsKey(sline[0]))
                            LangFinnishDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "German")
                        if (!LangGermanDic.ContainsKey(sline[0]))
                            LangGermanDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "French")
                        if (!LangFrenchDic.ContainsKey(sline[0]))
                            LangFrenchDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Thai")
                        if (!LangThaiDic.ContainsKey(sline[0]))
                            LangThaiDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Indonesian")
                        if (!LangIndonesianDic.ContainsKey(sline[0]))
                            LangIndonesianDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Spanish")
                        if (!LangSpanishDic.ContainsKey(sline[0]))
                            LangSpanishDic.Add(sline[0],sline[j]);
                    if(Firstline[j] == "Korean")
                        if (!LangKoreanDic.ContainsKey(sline[0]))
                            LangKoreanDic.Add(sline[0],sline[j]);
                }
            }
            IsDone = true;
            if(cbList.Count > 0 )
            for (int i = 0; i < cbList.Count; i++)
            {
                cbList[i]();
            }
        }

        private static LanguageMgr _instance = null;
        public static LanguageMgr Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new LanguageMgr();
                return _instance;
            }
        }
    }
}