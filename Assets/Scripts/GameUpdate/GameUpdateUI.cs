//#define ENABLE_TEST

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using com.adjust.sdk;

namespace War.GameUp
{
    public class GameUpdateUI : MonoBehaviour
    {
        public Slider progressBar;
        public RectTransform progressBg;
        public Text info;
        public GameObject reinstallMsgBox;
        public Button btnDownload;
        public Text tips;
        public GameObject content;
        public GameObject dynamicPic;
        public RawImage rawImage;
        public GameObject forceUpdateAppStore;
        public RectTransform TipsProgressBg;
        public RectTransform startUpCanvas;

        //private bool isForMajia = false;
        Dictionary<string, object> gameConfig = null;

        //private AssetsUpdator.Status currStatus;
        private bool startUpdate = false;
        private string newestVersion = "1.0.1";
        private bool needRebootApp = false;
        private float lastTime = 0;
        private int lastBytes = 0;

        //是否国内包
        private bool isDomestic = false;
        int channelID;
        private void Awake()
        {
            Debug.LogWarning("cost time,app start:" + Time.realtimeSinceStartup);
            // TextAsset textAsset = Resources.Load<TextAsset>("game_config");
            // gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);

            channelID = System.Convert.ToInt32(GameConfig.Instance().TryGetString("CHANNEL_ID"));
            isDomestic = GameConfig.Instance().TryGetBool("Q1SDK_DOMESTIC");
            
            InitProgressUI();
            RegisterEvent();

            reinstallMsgBox.SetActive(false);
            tips.gameObject.SetActive(false);
            forceUpdateAppStore.SetActive(false);

            progressBg.gameObject.SetActive(false);
            if (IsIOS())
                info.gameObject.SetActive(false);
            
            btnDownload.onClick.AddListener(downloadNewVersion);

           
            
            // 兼容已经发布出去的马甲包
            if (channelID == 2118002 || channelID == 2118003)
            {
                rawImage.enabled = true;
                //transform.GetComponent<Image>().enabled = true;
                //dynamicPic.SetActive(false);
            }
        }
        bool IsIOS()
        {
#if UNITY_EDITOR
            return true;
#endif
            //海外IOS位面2不隐藏进度条
            if (channelID == 20090 || channelID == 20093 || channelID == 21247702)
                return false;
            return Application.platform == RuntimePlatform.IPhonePlayer;
        }

        void OnSetReviewFinishCsharp()
        {
            if (!IsIOS())
                return;
            progressBg.gameObject.SetActive(!AssetsUpdator.s_IsReviewing);
            if (!AssetsUpdator.s_IsReviewing)
            {
                info.gameObject.SetActive(true);
            }
        }
        private void Start()
        {
            ModifyTabletBg();
            InitProgressUI();
        }

        public void RegisterEvent()
        {
#if !ENABLE_TEST
            AssetsUpdator.OnCompleted += OnUpdateComplete;
            AssetsUpdator.OnError += OnUpdateError;
            AssetsUpdator.OnProgress += OnUpdateProgress;
            AssetsUpdator.OnStatusChanged += OnUpdatorStatusChanged;
            AssetsUpdator.IsUpdateDll += (val) => { needRebootApp = val; };
            AssetsUpdator.onSetReviewFinishCsharp += OnSetReviewFinishCsharp;
#if UNITY_ANDROID
            AssetsUpdator.StartDownloadApk += (string ver) => { newestVersion = ver; };
            AssetsUpdator.OnDownload += OnDownloadApkProgress;
#endif

#endif
        }

        /// <summary>
        /// 界面销毁时，反注册掉委托，防止 OnUpdatorStatusChanged 等委托在界面销毁后通知界面更新
        /// </summary>
        public void UnregisterEvent()
        {
#if !ENABLE_TEST
            AssetsUpdator.OnCompleted -= OnUpdateComplete;
            AssetsUpdator.OnError -= OnUpdateError;
            AssetsUpdator.OnProgress -= OnUpdateProgress;
            AssetsUpdator.OnStatusChanged -= OnUpdatorStatusChanged;
            AssetsUpdator.IsUpdateDll -= (val) => { needRebootApp = val; };
            AssetsUpdator.onSetReviewFinishCsharp -= OnSetReviewFinishCsharp;
#if UNITY_ANDROID
            AssetsUpdator.StartDownloadApk -= (string ver) => { newestVersion = ver; };
            AssetsUpdator.OnDownload -= OnDownloadApkProgress;
#endif

#endif
        }

        public void OnDestroy()
        {
            UnregisterEvent();
        }

        private void InitProgressUI(bool isInitTipsUI = false)
        {
            var screen_width = startUpCanvas.sizeDelta.x;

            float default_scale = (704.0f / 720.0f);
            float default_scale2 = (685.0f / 720.0f);
            progressBg.sizeDelta = new Vector2(default_scale * screen_width, progressBg.sizeDelta.y);
            var progress_rect = progressBar.gameObject.transform.GetComponent<RectTransform>();
            progress_rect.sizeDelta = new Vector2(default_scale2 * screen_width, progress_rect.sizeDelta.y);
            TipsProgressBg.sizeDelta = new Vector2(default_scale * screen_width, progressBg.sizeDelta.y);
            if (!isInitTipsUI)
            {
                 if (AssetsUpdator.isSetReview && !AssetsUpdator.s_IsReviewing || !IsIOS())
                    progressBg.gameObject.SetActive(true);
            }
            else
            {
                if (AssetsUpdator.isSetReview && !AssetsUpdator.s_IsReviewing || !IsIOS())
                    TipsProgressBg.gameObject.SetActive(true);
            }
        }

        public void ModifyTabletBg()
        {
            var loginBgNode = GameObject.Find("StartupCanvas/Bg/Model/UILoginBg");
            if(loginBgNode == null)
            {
                Debug.Log("Tablet, can not find UILoginBg");
                return;
            }

            var imgLoginBgTabletTrans = loginBgNode.transform.Find("imgLoginBgTablet");
            if (imgLoginBgTabletTrans == null)
            {
                Debug.Log("Tablet, can not find imgLoginBgTablet");
                return;
            }
            if (IsAdaptiveTablet())
            {
                imgLoginBgTabletTrans.gameObject.SetActive(true);

                var imgLoginBgTrans = loginBgNode.transform.Find("imgLoginBg");
                if (imgLoginBgTrans == null)
                {
                    Debug.Log("Tablet, can not find imgLoginBg for tablet");
                    return;
                }
                else
                {
                    imgLoginBgTrans.gameObject.SetActive(false);
                }
            }
        }

        public bool IsIOSTablet()
        {
            if(Application.platform != RuntimePlatform.IPhonePlayer)
            {
                return false;
            }

            var deviceModel = SystemInfo.deviceModel;
            deviceModel = deviceModel.ToLower().Trim();
            Debug.Log("Tablet, deviceModel:" + deviceModel);

            if(deviceModel.Contains("ipad"))
            {
                return true;
            }

            return false;
        }

        public bool IsAndroidTablet()
        {
            var physicScreen = Mathf.Sqrt(Screen.width * Screen.width + Screen.height * Screen.height) / Screen.dpi;
            Debug.Log("Tablet, physicScreen:" + physicScreen);

            if(physicScreen >= 7.0f)
            {
                return true;
            }
            return false;
        }
        public bool IsAdaptiveTablet()
        {
            if (Application.platform == RuntimePlatform.IPhonePlayer && IsIOSTablet())
            {
                Debug.LogWarning("iOS Tablet");
                return true;
            }
            else if(Application.platform == RuntimePlatform.Android && IsAndroidTablet())
            {
                Debug.LogWarning("Android Tablet");
                // Android pad 不做 UI 特殊适配
                return false;
                //return true;
            }
            if (Application.isEditor)
            {
                var aspectRate = 0.5625f;
                var curRatio = (float)Screen.width / Screen.height;
                if (curRatio <= aspectRate)
                {
                    //宽变小，高变大，不处理
                    return false;
                }
                else
                {
                    return true;
                }
            }

            return false;
        }

#if ENABLE_TEST
        IEnumerator Start()
        {
            OnUpdatorStatusChanged(AssetsUpdator.Status.LoadingLocalVersionInfo);
            yield return new WaitForSeconds(2);
            OnUpdatorStatusChanged(AssetsUpdator.Status.RequestingRemoteVersionInfo);
            yield return new WaitForSeconds(2);
            OnUpdatorStatusChanged(AssetsUpdator.Status.RequestingPatchInfo);
            yield return new WaitForSeconds(2);
            OnUpdatorStatusChanged(AssetsUpdator.Status.DownloadingPatch);
            yield return new WaitForSeconds(2);
            OnUpdatorStatusChanged(AssetsUpdator.Status.WaitingForExtraction);
            yield return new WaitForSeconds(2);
            OnUpdatorStatusChanged(AssetsUpdator.Status.UpdateServerInfo);
            yield return new WaitForSeconds(2);

            OnUpdateError(AssetsUpdator.ErrCode.EReinstall);
        }
#endif

        void OnUpdateComplete()
        {
            if (!AssetsUpdator.s_IsReviewing)
            {
                InitProgressUI(true);
               if (AssetsUpdator.isSetReview && !AssetsUpdator.s_IsReviewing || !IsIOS())
                    tips.gameObject.SetActive(true);
                tips.text = I18NManager.GetText("load_configuration");
            }
            else
            {
                InitProgressUI(true);
                tips.gameObject.SetActive(false);
            }
                        
            if (Launch.LaunchMgr.Ins != null)
            {
                Launch.LaunchMgr.Ins.SceneMgr.LoadScene(Launch.SceneType.Start1);
            }
            else
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene(1);
            }
        }

        void OnUpdateError(AssetsUpdator.ErrCode code, string message = null)
        {
            if (code == AssetsUpdator.ErrCode.EReinstall)
            {
                reinstallMsgBox.SetActive(true);
                Debug.LogWarningFormat("The version has expired : {0} - {1}", code, message);
                //info.text = string.Format("The version has expired : {0} - {1}", code, message);
            }
            else if(code == AssetsUpdator.ErrCode.EShowAppStoreUI)
            {
                forceUpdateAppStore.SetActive(true);

                var update_config = AssetsUpdator.UpdateConfig;
                if(update_config!=null)
                {
                    // update_config["skip_install_version"] = "1.0.20";

                    object ob = null;
                    if (update_config.TryGetValue("skip_install_version", out ob))
                    {
                        var skip_install_version = ob.ToString();
                        var bSkip = War.Script.Utility.VersionIsHigher(Application.version, skip_install_version);

                        var skipUI = forceUpdateAppStore.transform.Find("btns/skip");
                        if(skipUI)
                        {
                            skipUI.gameObject.SetActive(bSkip);
                        }
                    }
                }
                Debug.LogWarningFormat("The force version has expired : {0} - {1}", code, message);
            }
            else
            {
                string text = I18NManager.GetText("update_error");
                info.text = string.Format(text, code);
                Debug.LogErrorFormat("OnUpdateError {0} - {1}", code, message);
                AdjustLoginFailed(code, info.text);
            }

            if (Q1.Q1SDK.Instance != null)
            {
                Q1.Q1SDK.Instance.StartEvent("updateError", "error=" + code);
                ReportUpdateEvent("update_error");
                if (isDomestic)
                {
                    Q1.Q1SDK.Instance.TrackUpdateError(string.Format("OnUpdateError {0} - {1}", code, message));
                }
            }
        }

        void OnUpdateProgress(int current, int total)
        {
            progressBar.value = (float)current / (float)total;
            string text = I18NManager.GetText("download_patch");
            // 兼容以前代码。total为100 表示百分比
            if (total == 100)
            {
                info.text = string.Format(text, newestVersion, progressBar.value * 100f) + (needRebootApp ? ("\n" + I18NManager.GetText("will_reboot")) : "");
            }
            else
            {
                var downloaded = current - lastBytes;
                downloaded = downloaded > 0 ? downloaded : 0;
                var speed = downloaded / (Time.realtimeSinceStartup - lastTime) / 1024;
                lastTime = Time.realtimeSinceStartup;
				lastBytes = current;
                info.text = string.Format(text, newestVersion + string.Format("({0:F2}M {1:F1}KB/s)", total / 1024 / 1024, speed), progressBar.value * 100f) + (needRebootApp ? ("\n" + I18NManager.GetText("will_reboot")) : "");
            }

            if (startUpdate == false)
            {
                startUpdate = true;
                if (Q1.Q1SDK.Instance != null)
                {
                    Q1.Q1SDK.Instance.StartEvent("updateBegin", "ver=" + System.DateTime.Now.ToString("yyyy-MM-dd.HH-mm-ss-ff", System.Globalization.DateTimeFormatInfo.InvariantInfo));
                    if (isDomestic)
                    {
                        Q1.Q1SDK.Instance.TrackUpdateBegin();
                    }
                    ReportUpdateEvent("update_begin");
                }
            }

            if (startUpdate == true && current == total)
            {
                if (Q1.Q1SDK.Instance != null)
                {
                    System.Text.StringBuilder str = new System.Text.StringBuilder();
                    str.Append("ver=");
                    str.Append(System.DateTime.Now.ToString("yyyy-MM-dd.HH-mm-ss-ff", System.Globalization.DateTimeFormatInfo.InvariantInfo));
                    str.Append("update = 100%");
                    Q1.Q1SDK.Instance.StartEvent("updateEnd", str.ToString());
                    ReportUpdateEvent("update_end");
                    if (isDomestic)
                    {
                        Q1.Q1SDK.Instance.TrackUpdateEnd();
                    }
                }
            }

        }

#if UNITY_ANDROID
        void OnDownloadApkProgress(int current, int total)
        {
            progressBar.value = (float)current / (float)total;
            string text = I18NManager.GetText("download_apk");
            info.text = string.Format(text, newestVersion, progressBar.value * 100f);
        }
#endif
        void OnUpdatorStatusChanged(AssetsUpdator.Status status)
        {
            switch(status)
            {
                case AssetsUpdator.Status.LoadingLocalVersionInfo :
                    {
                        info.text = I18NManager.GetText("load_local_version"); // "Loading local version info...";
                    }
                    break;
                case AssetsUpdator.Status.RequestingPatchInfo:
                    {
                        info.text = I18NManager.GetText("req_patch_info") + (needRebootApp ? I18NManager.GetText("will_reboot") : ""); // "Requesting patch info...";
                    }
                    break;
                case AssetsUpdator.Status.RequestingRemoteVersionInfo:
                    {
                        info.text = I18NManager.GetText("req_remote_ver"); // "Requesting remote version info...";
                    }
                    break;
                case AssetsUpdator.Status.WaitingForExtraction:
                    {
                        info.text = I18NManager.GetText("wait_extraction") + (needRebootApp ? I18NManager.GetText("will_reboot") : ""); //"Waiting for resource extraction...";
                    }
                    break;
                case AssetsUpdator.Status.UpdateServerInfo:
                    {
                        info.text = I18NManager.GetText("update_server_info"); //info.text = "Update server info...";
                    }
                    break;
                default:// DownloadingPatch
                    {

                    }
                    break;
            }
        }

        private void downloadNewVersion()
        {
            if (gameConfig == null)
            {
                Application.OpenURL("https://play.google.com/store/apps/details?id=com.q1.survival");
                Debug.LogError("解析game_config.json失败");
                return;
            }

            if (AssetsUpdator.UpdateConfig == null)
            {
                Application.OpenURL("https://play.google.com/store/apps/details?id=com.q1.survival");
                Debug.LogError("解析update.json失败");
                return;
            }

            string url = System.Convert.ToString(AssetsUpdator.UpdateConfig["downdload_url"]);
            int type = System.Convert.ToInt32(gameConfig["VERSION_TYPE"]);
            if (type == 0)
            {
                // 国内版
                url = System.Convert.ToString(AssetsUpdator.UpdateConfig["downdload_url_china"]);
            }
            Application.OpenURL(url);
        }

        //上报登录失败原因
        private void AdjustLoginFailed(AssetsUpdator.ErrCode code, string msg)
        {
            string fmt = "";
            System.Text.StringBuilder str = new System.Text.StringBuilder();
            str.Append("{");
            switch (code)
            {
                case AssetsUpdator.ErrCode.ELoadStreamAssetsVersion:
                    fmt = "\"LoadStreamAssetsVersion\":{0}";
                    str.Append(string.Format(fmt, msg));
                    break;
                case AssetsUpdator.ErrCode.ERequestRemoteAssetVersion:
                    fmt = "\"RequestRemoteAssetVersion\":{0}";
                    str.Append(string.Format(fmt, msg));
                    break;
                case AssetsUpdator.ErrCode.ERequestPatchList:
                    fmt = "\"RequestPatchList\":{0}";
                    str.Append(string.Format(fmt, msg));
                    break;
                case AssetsUpdator.ErrCode.EDownloadPatch:
                    fmt = "\"DownloadPatch\":{0}";
                    str.Append(string.Format(fmt, msg));
                    break;
                case AssetsUpdator.ErrCode.EUpdateServerInfo:
                    fmt = "\"UpdateServerInfo\":{0}";
                    str.Append(string.Format(fmt, msg));
                    break;
                default:
                    break;
            }
            str.Append("}");
            string paramJson = str.ToString();
            Adjust.trackEvent("hxamib", -1, "", "", "", paramJson, "", "", "", "");
        }

        // 冰川上传更新相关
        private void ReportUpdateEvent(string eventName)
        {
            var pid = System.Convert.ToString(gameConfig["CHANNEL_ID"]);
            string uuid = Q1.Q1SDK.Instance.GetUUID();
            string imei_idfa = Q1.Q1SDK.Instance.GetImeiMD5();
            string radid = Q1.Q1SDK.Instance.GetRadid();
            string rsid = Q1.Q1SDK.Instance.GetRsid();
            string fmt = "\"pid\":{0},\"uuid\":\"{1}\",\"imei_idfa\":\"{2}\",\"radid\":\"{3}\",\"rsid\":\"{4}\"";
            System.Text.StringBuilder str = new System.Text.StringBuilder();
            str.Append("{");
            str.Append(string.Format(fmt, pid, uuid, imei_idfa, radid, rsid));
            str.Append("}");
            string paramJson = str.ToString();

            //Debug.LogWarning("ReportUpdateEvent~~~~~~~~"+eventName+"~~~"+ paramJson);
            Q1.Q1SDK.Instance.TrackEvent(eventName, paramJson);
        }
    }
}

