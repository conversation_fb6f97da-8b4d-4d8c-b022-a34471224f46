using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class CardDissolveClip : PlayableAsset, ITimelineClipAsset
{
    public CardDissolveBehaviour template = new CardDissolveBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<CardDissolveBehaviour>.Create(graph, template);
        CardDissolveBehaviour behaviour = playable.GetBehaviour();
        behaviour.node = node;
        return playable;
    }
}