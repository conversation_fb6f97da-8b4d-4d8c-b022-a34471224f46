using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class GraynessClip : PlayableAsset, ITimelineClipAsset
{
    public GraynessBehaviour template = new GraynessBehaviour();

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<GraynessBehaviour>.Create(graph, template);
        return playable;
    }
}

