using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class ParticleAttackBehaviour : PlayableBehaviour
{
    public enum SkillHitType
    {
        Normal,
        LightChainSkill,
    }
    public GameObject prefab;
    public string particleSystemAbName = "";

    [Header("场外大招")]
    public GameObject outsideSkillPrefab;

    public string outsideSkillPrefabAbName = "";
    public float AlphaOffset;
    [NonSerialized]
    public Transform parent = null;
    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dest = null;
    [NonSerialized]
    public List<ParticleSystemDriver> drivers = null;
    [NonSerialized]
    public List<BattleActorNode> rootSources = null;

    [NonSerialized]
    public BattleOutsideSkillManager manager;

    [Header("飞行特效交替反转")] 
    public bool IsFlyEffectNeedReverse = false;
    public int needReverseMultiple = 2;//每逢几个反转
    [Header("依次击打")]
    public SkillHitType skillType = SkillHitType.Normal; 
    public int hitIndexRound = 0;//每逢几个反转

    //private bool bValid = true;

    //public void SetValid(bool bValid)
    //{
    //    //Debug.LogError("SetValid :" + bValid);
    //    if (this.bValid == bValid)
    //    {
    //        return;
    //    }
    //    this.bValid = bValid;
    //}

    public override void OnGraphStart(Playable playable)
    {
        //if (!bValid)
        //{
            //Debug.Log("ParticleAttackBehaviour OnGraphStart not valid");
            //return;
        //}
        //Debug.Log("PAB_OnGraphStart----||"+GetHashCode()+" || " + (prefab?prefab.name:"null") + " || " + ((float)playable.GetDuration()));
        if (dest != null && dest.Count != 0)
        {
            foreach (BattleActorNode node in dest)
            {
                ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
                if (Application.isPlaying)
                {
                    driver.PrepareParticleSystem((float)playable.GetDuration());
                }
                drivers.Add(driver);
            }
        }
        else if (source != null)
        {
            ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
            if (Application.isPlaying)
            {
                driver.PrepareParticleSystem((float)playable.GetDuration());
            }
            drivers.Add(driver);
        }
        else if (parent != null)
        {
            // 兼容编辑器预览，但是没法校对位置缩放
            ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
            driver.Attach(parent, Vector3.zero);
            if (Application.isPlaying)
            {
                driver.PrepareParticleSystem((float)playable.GetDuration());
            }
            drivers.Add(driver);
        }


        // if (manager == null)
        // {
        //     manager = GameObject.Find("CanvasOutsideSkill").GetComponent<BattleOutsideSkillManager>();
        //     Debug.LogError("manager == null!!!" + manager == null);
        // }
        
        // ParticleSystemDriver driver1 = new ParticleSystemDriver(prefab);
        // driver1.Attach(parent, Vector3.zero);
        // drivers.Add(driver1);

        // driver1 = new ParticleSystemDriver(outsideSkillPrefab);
        // driver1.Attach(parent, Vector3.zero);
        // drivers.Add(driver1);
    }

    public override void OnGraphStop(Playable playable)
    {
        //Debug.Log("PAB_OnGraphStop-----||" + GetHashCode()+" || " + (prefab?prefab.name:"null") + " || " + drivers.Count);
        foreach (ParticleSystemDriver driver in drivers)
            driver.Destroy();

        drivers.Clear();
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        //Debug.Log("PAB_OnBehaviourPause----||" + GetHashCode()+" || " + (prefab?prefab.name:"null") + " || " + ((float)playable.GetDuration()));
        foreach (ParticleSystemDriver driver in drivers)
            driver.Clear();
        //if (manager != null && outsideSkillPrefab != null)
        //{
        //    manager.StopPlay();
        //}


        //if (Application.isMobilePlatform)
        //{
        //    playable.SetPlayState(PlayState.Playing);
        //    //OnBehaviourPlay(playable, default(FrameData));
        //}

        //if ( manager != null && outsideSkillPrefab != null)
        //{
        //    manager.StopPlay();
        //}
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        //if (!bValid)
        //{
        //    //Debug.Log("ParticleAttackBehaviour ProcessFrame track not valid");
        //    return;
        //}

        //Debug.Log("PAB_ProcessFrame----||" + GetHashCode()+" || " + (prefab?prefab.name:"null") + " || " + drivers.Count);
        float time = (float)playable.GetTime();
        float duration = (float)playable.GetDuration();

        foreach (ParticleSystemDriver driver in drivers)
            driver.Simulate(time, duration);
    }

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        //if (!bValid)
        //{
        //    Debug.Log("ParticleAttackBehaviour OnBehaviourPlay track not valid");
        //    return;
        //}
        //Debug.Log("ParticleAttackBehaviour OnBehaviourPlay track " + (prefab ? prefab.name : "null"));
        //Debug.Log("PAB_OnBehaviourPlay-||" + GetHashCode()+" || " + (prefab?prefab.name:"null"));
        if (dest != null && dest.Count != 0)
        {
            for (int i = 0; i < drivers.Count; ++i)
            {
                ParticleSystemDriver driver = drivers[i];
                if (IsFlyEffectNeedReverse)
                {
                    SetFlyEffectReverse(driver, i);
                }
                if (skillType != SkillHitType.Normal)
                {
                    if (hitIndexRound > 0 && rootSources != null && rootSources.Count > hitIndexRound)
                    {
                        driver.Attach(rootSources[hitIndexRound - 1], rootSources[hitIndexRound], i);
                        driver.Play();
                    }
                    else
                    {
                        driver.Attach(source, dest[i], i);
                        driver.Play();
                    }
                }
                else
                {
                    driver.Attach(source, dest[i], i);
                    driver.Play();
                }
            }
        }
        else if (source != null)
        {
            for (int i = 0; i < drivers.Count; ++i)
            {
                ParticleSystemDriver driver = drivers[i];
                if (IsFlyEffectNeedReverse)
                {
                    SetFlyEffectReverse(driver, i);
                }
                driver.Attach(source, source, i);
                driver.Play();
            }
        }
        else
        {
            for (int i = 0; i < drivers.Count; ++i)
            {
                ParticleSystemDriver driver = drivers[i];
                driver.Play();
            }
        }

        if (manager != null && ( outsideSkillPrefab != null || outsideSkillPrefabAbName != "" ))
        {
            //Debug.Log("加载大招");
            //manager.maxTime = drivers.playable.time
            BattleOutsideSkillManager.ResLoadedCallback callBack = (PlayableAsset _assets) => {
                //Debug.Log("成功加载，开始播放");
                manager.BegainPlay();
            };
            manager.pretemp = AlphaOffset;
            if (outsideSkillPrefabAbName != "")
            {
                manager.TryLoadSkill(outsideSkillPrefabAbName, callBack);
                //Debug.Log("通过abname加载大招：" + outsideSkillPrefabAbName);
            }
            else if (outsideSkillPrefab != null)
            {
                manager.TryLoadSkill(outsideSkillPrefab, callBack);
                //Debug.Log("通过outsideSkillPrefab加载大招：" + outsideSkillPrefab.name);
            }
        }
    }

    private void SetFlyEffectReverse(ParticleSystemDriver driver, int i)
    {
        bool needReverse = (i + 1) % needReverseMultiple == 0;
        if (driver.config_new)
        {
            driver.config_new.flyEffectNeedReverse = needReverse;
        }
    }
}
