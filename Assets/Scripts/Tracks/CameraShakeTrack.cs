using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.857f, 0.658f, 0.244f)]
[TrackClipType(typeof(CameraShakeClip))]
[TrackBindingType(typeof(Animator))]

public class CameraShakeTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        Animator animator = director.GetGenericBinding(this) as Animator;

        foreach (TimelineClip clip in GetClips())
        {
            CameraShakeClip playableAsset = clip.asset as CameraShakeClip;

            if (playableAsset)
            {
                playableAsset.animator = animator;
            }
        }
        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        var comp = director.GetGenericBinding(this) as Animator;
        if (comp == null)
            return;
        var so = new UnityEditor.SerializedObject(comp);
        var iter = so.GetIterator();
        while (iter.NextVisible(true))
        {
            if (iter.hasVisibleChildren)
                continue;
            driver.AddFromName<Animator>(comp.gameObject, iter.propertyPath);
        }
#endif
        base.GatherProperties(director, driver);
    }
}
