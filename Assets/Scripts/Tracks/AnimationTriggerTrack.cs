using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using System.Collections.Generic;
using System;
using War.Battle;

[TrackColor(0.1f, 0f, 1f)]
[TrackClipType(typeof(AnimationTriggerClip))]
[TrackBindingType(typeof(Animator))]
public class AnimationTriggerTrack : TrackAsset
{
    public int segment;

    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dests;

    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        Animator animator = director.GetGenericBinding(this) as Animator;

        foreach (TimelineClip clip in GetClips())
        {
            AnimationTriggerClip playableAsset = clip.asset as AnimationTriggerClip;

            if (playableAsset)
            {
                if (animator)
                {
                    playableAsset.animator = animator;

                    playableAsset.source = source;
                    playableAsset.dests = dests;
                }
            }
        }

        var scriptPlayable = ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
        return scriptPlayable;
    }
}
