using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

public class ParticleAttackClip : PlayableAsset, ITimelineClipAsset
{
    public ParticleAttackBehaviour template = new ParticleAttackBehaviour();

    [NonSerialized]
    public Transform parent;
    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dest;
    [NonSerialized]
    public BattleOutsideSkillManager manager;
    [NonSerialized]
    public BattlePlayer player;
    //private bool bValid = true;
    //private ParticleAttackBehaviour behaviour = null;
    [NonSerialized]
    public List<BattleActorNode> rootSources = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<ParticleAttackBehaviour>.Create(graph, template);
        ParticleAttackBehaviour clone = playable.GetBehaviour();
        clone.parent = parent;
        clone.source = source;
        clone.dest = dest;
        clone.drivers = new List<ParticleSystemDriver>();

        clone.rootSources = rootSources;
        clone.manager = manager;

        //behaviour = clone;
        //behaviour.SetValid(bValid);
        return playable;
    }

    //public void SetValid(bool bValid)
    //{
    //    if (this.bValid == bValid)
    //    {
    //        return;
    //    }
    //    this.bValid = bValid;
    //
    //    if(behaviour != null)
    //    {
    //        behaviour.SetValid(bValid);
    //    }
    //}
}
