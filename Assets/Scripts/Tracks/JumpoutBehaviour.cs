using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;
using War.Base;

[Serializable]
public class JumpoutBehaviour : PlayableBehaviour
{
    [Serializable]
    public struct Param
    {
        public AnimationCurve alphaCurve;
        public LayerMask alphaLayer;
        public AnimationCurve scaleCurve;
        public float scaleFactor;
        public string animatorTrigger;
        public float animationDelay;
    }

    public Param inner;
    public Param outter;
    public LayerMask jumpoutLayer;

    [NonSerialized]
    public BattleActorNode node;
    [NonSerialized]
    public Card jumperCard;

    private bool innerAnimated = false;
    private bool outterAnimated = false;
    private Vector3 pos;
    private Transform jumpout;

    private bool bDisposed = false;

    private bool bUseCache = false;

    public override void OnGraphStart(Playable playable)
    {
        if (!Config.EnableJumpOut)
            return;
        bDisposed = false;
        bUseCache = (!Application.isEditor || Application.isPlaying) && node.role != null;

        GameObject jumpActor = null;
        if (node && node.card && node.manager.hub.jumpoutManager)
        {
            BattleJumpoutManager manager = node.manager.hub.jumpoutManager;
            jumpout = manager.AllocJumpoutNode().transform;
            jumpout.transform.position = node.transform.position;
            pos = jumpout.transform.position;

            if (!bUseCache)
            {
                jumpActor = GameObject.Instantiate(node.card.gameObject);
                InitJumperCard(jumpActor, manager);
            }
            else
            {
                GameObjectPool.GetAsyncWithCache(CachedGameObjectPool.Cached_Type_Character, node.role.res, null, (obj, bCached) =>
                {
                    if(obj == null)
                    {
                        return;
                    }
                    if(bDisposed)
                    {
                        GameObjectPool.ReleaseWithCache(CachedGameObjectPool.Cached_Type_Character, obj);
                        return;
                    }
                    jumpActor = obj as GameObject;
                    InitJumperCard(jumpActor, manager);
                });
            }
        }
    }

    void InitJumperCard(GameObject jumpActor, BattleJumpoutManager manager)
    {
        if(null == jumpActor)
        {
            return;
        }
        jumperCard = jumpActor.GetComponent<Card>();

        if (jumperCard)
        {
            if (Application.isPlaying == false)
            {
                jumperCard.Initialize();
            }

            jumperCard.SetupTransform(manager.GetJumpBounds(node), jumpout);

            //BattleJumpoutManager jumpoutManager = jumpout.GetComponentInParent<BattleJumpoutManager>();
            //if (jumpoutManager)
            //{
            //}
            //else
            //{
            //    jumpActor.transform.SetParent(node.jumpout);
            //    jumpActor.transform.localScale = node.card.transform.localScale;
            //    jumpActor.transform.localPosition = node.card.transform.localPosition;
            //}

            node.card.avatar = jumperCard;

            jumperCard.SetupLayer(jumpoutLayer);
            jumperCard.SetupStencil(1, 0);
            jumperCard.SetupSortingLayerName("Jumpout");
            jumperCard.SetSpriteAlpha(0, outter.alphaLayer);
        }
    }

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
 
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (jumperCard)
        {
            float time = (float)(playable.GetTime() / playable.GetDuration());
            jumperCard.transform.parent.position = pos;
            if (node)
                ProcessCard(node.card, ref inner, time);

            ProcessCard(jumperCard, ref outter, time);

            if (node && node.manager)
                AdjustCard(jumperCard, node.manager.sceneBounds);

            if (outter.animatorTrigger != null && outter.animatorTrigger != "" && jumperCard && time >= outter.animationDelay && !outterAnimated)
            {
                jumperCard.animator.SetTrigger(outter.animatorTrigger);
                outterAnimated = true;
            }
            if (inner.animatorTrigger != null && inner.animatorTrigger != "" && node && time >= inner.animationDelay && !innerAnimated)
            {
                node.card.animator.SetTrigger(inner.animatorTrigger);
                innerAnimated = true;
            }
        }
    }

    public void ProcessCard(Card card, ref Param param, float normalizedTime)
    {
        float alpha = param.alphaCurve.Evaluate(normalizedTime);
        float scale = param.scaleCurve.Evaluate(normalizedTime) * param.scaleFactor;

        card.SetSpriteAlpha(alpha, param.alphaLayer);
        card.transform.parent.localScale = new Vector3(scale, scale, scale);
    }

    public void AdjustCard(Card card, Bounds scene)
    {
        Transform node = card.transform.parent;
        Matrix4x4 cardToNode = Matrix4x4.TRS(card.transform.localPosition, card.transform.localRotation, card.transform.localScale);
        Matrix4x4 nodeToParent = Matrix4x4.TRS(node.localPosition, node.localRotation, node.localScale);
        Matrix4x4 cardToParent = nodeToParent * cardToNode;

        Vector3 cardMin = cardToParent.MultiplyPoint(card.config.center - Vector3.one*card.config.size * 0.5f);
        Vector3 cardMax = cardToParent.MultiplyPoint(card.config.center + Vector3.one * card.config.size * 0.5f);
        Vector3 sceneMin = card.transform.parent.parent.worldToLocalMatrix.MultiplyPoint(scene.min);
        Vector3 sceneMax = card.transform.parent.parent.worldToLocalMatrix.MultiplyPoint(scene.max);

        Vector3 offset = Vector3.zero;
        if (cardMin.x < sceneMin.x)
        {
            offset.x = sceneMin.x - cardMin.x;
        }
        if (cardMax.x > sceneMax.x)
        {
            offset.x = sceneMax.x - cardMax.x;
        }
        if (cardMin.y < sceneMin.y)
        {
            offset.y = sceneMin.y - cardMin.y;
        }
        if (cardMax.y > sceneMax.y)
        {
            offset.y = sceneMax.y - cardMax.y;
        }
        node.transform.localPosition = node.transform.localPosition + offset;
        return;
    }

    public override void OnGraphStop(Playable playable)
    {
        bDisposed = true;

        if (jumperCard)
        {
            if (bUseCache)
            {
                ReleaseCardWithCache(jumperCard);
            }
            else
            {
                GameObject.DestroyImmediate(jumperCard.gameObject);
            }

            if (node && node.card)
                node.card.avatar = null;
        }
        if (node && jumpout)
        {
            BattleJumpoutManager manager = node.manager.hub.jumpoutManager;
            manager.ReleaseJumpoutNode(jumpout);
            jumpout = null;
        }
    }

    void ReleaseCardWithCache(Card card)
    {
        card.MarkResetting(CardResetting.CardColor);
        card.MarkResetting(CardResetting.SortingLayer);
        card.Reset();
        GameObjectPool.ReleaseWithCache(CachedGameObjectPool.Cached_Type_Character, card.gameObject);
    }
}
