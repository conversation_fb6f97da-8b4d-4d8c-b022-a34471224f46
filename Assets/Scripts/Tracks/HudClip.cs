using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class HudClip : PlayableAsset, ITimelineClipAsset
{
    public HudBehaviour template = new HudBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<HudBehaviour>.Create(graph, template);
        HudBehaviour behaviour = playable.GetBehaviour();
        behaviour.node = node;
        return playable;
    }
}
