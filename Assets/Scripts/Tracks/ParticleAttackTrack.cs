using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.25f, 0.88f, 0.40f)]
[TrackClipType(typeof(ParticleAttackClip))]
[TrackBindingType(typeof(Transform))]
public class ParticleAttackTrack : TrackAsset
{
    public int segment = 0;
    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dest;

    [NonSerialized]
    public BattlePlayer player;
    [NonSerialized]
    public BattleOutsideSkillManager manager;
    [NonSerialized]
    public List<BattleActorNode> rootSources = new List<BattleActorNode>();

    //private bool bValid = true;

    //public void SetValid(bool bValid)
    //{
    //    if(this.bValid == bValid)
    //    {
    //        return;
    //    }
    //    this.bValid = bValid;
    //
    //    foreach (TimelineClip clip in GetClips())
    //    {
    //        ParticleAttackClip playableAsset = clip.asset as ParticleAttackClip;
    //        if (playableAsset)
    //        {
    //            playableAsset.SetValid(bValid);
    //        }
    //    }
    //}

    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        Transform parent = director.GetGenericBinding(this) as Transform;

        foreach (TimelineClip clip in GetClips())
        {
            ParticleAttackClip playableAsset = clip.asset as ParticleAttackClip;

            if (playableAsset && parent)
            {
                playableAsset.parent = parent;
                playableAsset.source = source;
                playableAsset.dest = dest;
                playableAsset.manager = manager;
                playableAsset.player = player;
                playableAsset.rootSources = rootSources;
            }
        }

        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        var comp = director.GetGenericBinding(this) as Transform;
        if (comp == null)
            return;
        var so = new UnityEditor.SerializedObject(comp);
        var iter = so.GetIterator();
        while (iter.NextVisible(true))
        {
            if (iter.hasVisibleChildren)
                continue;
            driver.AddFromName<Transform>(comp.gameObject, iter.propertyPath);
        }
#endif
        base.GatherProperties(director, driver);
    }
}
