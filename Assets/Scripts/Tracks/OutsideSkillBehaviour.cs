using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;

public class OutsideSkillBehaviour : PlayableBehaviour
{
    [NonSerialized]
    public BattleOutsideSkillManager manager;
    [NonSerialized]
    public GameObject skillObject;

    //public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    //{
    //    if (manager == null || skillObject == null)
    //    {
    //        return;
    //    }
    //}

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (manager == null || skillObject == null)
        {
            //Debug.LogError($"3manager=>{manager}��skillObject=>{skillObject}");
            return;
        }
        manager.TryLoadSkill(skillObject, (PlayableAsset _assets) =>
        {
            manager.BegainPlay();
        });
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        if (manager == null || skillObject == null)
        {
            //Debug.LogError($"4manager=>{manager}��skillObject=>{skillObject}");
            return;
        }
        manager.StopPlay();
    }

    public double GetEndTiming()
    {
        double timing = 0f;
        PlayableDirector director = skillObject.GetComponent<PlayableDirector>();
        if (director)
        {
            timing = director.duration;
        }
        return timing;
    }
}
