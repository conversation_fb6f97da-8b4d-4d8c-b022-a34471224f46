using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class JumpoutClip : PlayableAsset, ITimelineClipAsset
{
    public JumpoutBehaviour template = new JumpoutBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<JumpoutBehaviour>.Create(graph, template);
        JumpoutBehaviour behaviour = playable.GetBehaviour();
        behaviour.node = node;
        return playable;
    }
}
