using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class CrazingBehaviour : PlayableBehaviour
{
    public GameObject prefab;

    [NonSerialized]
    public Transform parent = null;
    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<ParticleSystemDriver> drivers = null;

    public override void OnGraphStart(Playable playable)
    {
        if (source != null && War.Base.Config.EnableCrazingBehaviour)
        {
            var driver = new ParticleSystemStencilDriver(prefab);
            if (Application.isPlaying)
            {
                driver.PrepareParticleSystem((float)playable.GetDuration());
                driver.InitStencilData();
                //driver.SetStencil(1 << source.MaskID, UnityEngine.Rendering.CompareFunction.Equal, UnityEngine.Rendering.StencilOp.Keep);
            }
            drivers.Add(driver);
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        if(drivers == null)
        {
            return;
        }

        foreach (ParticleSystemDriver driver in drivers)
        {
            driver.Destroy();
        }

        drivers.Clear();
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (drivers == null)
        {
            return;
        }

        float time = (float)playable.GetTime();
        float duration = (float)playable.GetDuration();

        foreach (ParticleSystemDriver driver in drivers)
        {
            driver.Simulate(time, duration);
        }
    }

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (source != null && War.Base.Config.EnableCrazingBehaviour && drivers != null)
        {
            for (int i = 0; i < drivers.Count; ++i)
            {
                ParticleSystemDriver driver = drivers[i];
                driver.Attach(source, source, i);
                driver.Play();
            }
        }
    }

}
