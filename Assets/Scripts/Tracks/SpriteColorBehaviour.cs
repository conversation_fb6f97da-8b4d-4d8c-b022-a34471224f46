using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class SpriteColorBehaviour : PlayableBehaviour
{
    public Color sourceColor = Color.white;
    public Color destColor = new Color(1, 1, 1, 0);
    public AnimationCurve curve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

    [NonSerialized]
    public BattleActorNode node = null;
    [NonSerialized]
    public LayerMask mask = -1;

    //public override void OnBehaviourPlay(Playable playable, FrameData info)
    //{
    //    if (node && node.card)
    //        node.card.MarkResetting(CardResetting.CardColor);
    //}

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node && node.card)
        {
            float time = (float)playable.GetTime() / (float)playable.GetDuration();
            Color color = Color.Lerp(sourceColor, destColor, curve.Evaluate(time));
            node.card.SetSpriteColor(color, mask);
        }
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        switch (info.evaluationType)
        {
            case FrameData.EvaluationType.Evaluate:
                if (info.weight == 0)
                {

                }
                break;
            case FrameData.EvaluationType.Playback:
                if (info.deltaTime != 0)
                {
                    Cleanup();
                }
                break;
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        Cleanup();
    }

    public void Cleanup()
    {
        if (node && node.card)
        {
            Color color = Color.Lerp(sourceColor, destColor, curve.Evaluate(1));
            node.card.SetSpriteColor(color, mask);
        }
    }
}

