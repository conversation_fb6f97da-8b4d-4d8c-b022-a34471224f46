using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;
using System.Collections.Generic;

[Serializable]
public class AnimationTriggerBehaviour : PlayableBehaviour
{
    //public enum e_TriggerType
    //{
    //    Set,
    //    Reset,
    //}
    //public enum e_AnimStr
    //{
    //    Attack = 1,
    //    Go = 2,
    //    Skill = 3,
    //    Back = 4,
    //    Stone = 5,
    //    Wound = 6,
    //    Death = 7
    //}
    [Header("主要动画Trigger")]

    public string trigger = "Ability";
    [Header("附加动画字段")]
    public AttachBoolConfig[] boolConfig = { new AttachBoolConfig() };
    public AttachIntConfig[] IntConfig = { new AttachIntConfig() };
    //public string[] boolStr = { "CanMove", "Skill" };
    //public bool[] SetBool = { false, false };
    public e_MoveTo m_moveTo;
    [Tooltip("如果状态机中存在对应bool校验通过，则不执行该Trigger")]
    [Header("冲突")]
    public AttachBoolConfig[] ConflitBool = null;
    [Header("需要旋转到目标位置")]
    public bool needRote = true;
    //[Header("是连击后续攻击")]
    //public bool isSeriailzed = false;

    [NonSerialized]
    public BattleActorNode SourceCard;
    [NonSerialized]
    public List<BattleActorNode> TargetCards;

    //string[] m_Str = {
    //"","Attack","Go","Skill","Back","Stone","Wound","Death",
    //};
    //public e_TriggerType _TriggerType = e_TriggerType.Set;
    //public e_AnimStr _AnimStr = e_AnimStr.Attack;
    [Tooltip("如果有特殊的Trigger在这里填写")]
    //public string trigger = "Ability";

    [HideInInspector]
    public Animator animator;

    private bool excuted = false;

    private Vector3 mainTargetPos = Vector3.zero;
    private Quaternion tempFacing = Quaternion.identity;
    public override void OnGraphStart(Playable playable)
    {
        if(TargetCards != null && TargetCards.Count > 0)
        {
            foreach (BattleActorNode item in TargetCards)
            {
                mainTargetPos = item.card.transform.position;
                break;
            }
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        if (excuted == false)
            ExeEvent();
    }
    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (excuted == false)
        {
            ExeEvent();
            excuted = true;
        }
        else
        {
            Debug.LogError("excuted animator >>>> " + animator);
        }
        if (needRote && !mainTargetPos.Equals(Vector3.zero) && SourceCard != null)
        {
            //Debug.Log($"mainTargetPos >>> {mainTargetPos}，source={source}，{source.gameObject.transform.position}");
            tempFacing = ParticleUtility.GetRotation(SourceCard.gameObject.transform.position, mainTargetPos);
            SourceCard.card.transform.localRotation = tempFacing;
        }
    }
    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        //if (Application.isMobilePlatform)
        //{
        //    playable.SetPlayState(PlayState.Playing);
        //}

        if (needRote && SourceCard != null)
        {
            SourceCard.card.transform.localRotation = Quaternion.identity;
        }
    }

    public float roteBackTime = 0.5f;
    private float percent = 0;
    private float disTime = 0;
    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (needRote && SourceCard != null)
        {
            float passTime = (float)playable.GetTime();
            float duration = (float)playable.GetDuration();
            disTime = duration - passTime;

            if (disTime <= roteBackTime)
            {
                percent = 1- disTime / roteBackTime;
                SourceCard.card.transform.localRotation = Quaternion.Lerp(tempFacing, Quaternion.identity,percent);
            }
        }
    }

    void ExeEvent()
    { 
        if (animator)
        {
            bool isMoveAction  =false;

            bool isEnable = true;

            if (ConflitBool != null && ConflitBool.Length > 0)
            {
                foreach (AttachBoolConfig attach in ConflitBool)
                {
                    if(animator.GetBool(attach.animStr) == attach.animBool)
                    {
                        isEnable = false;
                        break;
                    }
                }
            }

            if (isEnable)
            {
                if (boolConfig != null && boolConfig.Length > 0)
                {
                    foreach (AttachBoolConfig attach in boolConfig)
                    {
                        if (attach.animStr == "CanMove" && attach.animBool)
                        {
                            isMoveAction = true;
                            needRote = false;
                        }
                        else
                        {
                            animator.SetBool(attach.animStr, attach.animBool);
                        }
                    }
                }

                if (IntConfig != null && IntConfig.Length > 0)
                {
                    foreach (AttachIntConfig attach in IntConfig)
                    {
                        //Debug.Log("attach.animStr, attach.value >>"+ attach.animStr+"/"+ attach.value);
                        animator.SetInteger(attach.animStr, attach.value);
                    }
                }

                if (SourceCard != null && TargetCards != null && trigger == "Ability")
                {
                    if (isMoveAction)
                    {
                        if (SourceCard.card.actConfig == null)
                        {
                            SourceCard.card.actConfig = new ActingConfig();
                        }
                        SourceCard.card.actConfig.ActingNode = SourceCard;
                        SourceCard.card.actConfig.TargetNodes = TargetCards;
                        SourceCard.card.actConfig.moveTo = m_moveTo;
                        //SourceCard.card.actConfig.moveFromOrigin = true;

                    }
                    //else if (isSeriailzed)
                    //{
                    //    SourceCard.card.actConfig.AttachBool = new AttachBoolConfig();
                    //    SourceCard.card.actConfig.AttachBool.animStr = "CanMove";
                    //    SourceCard.card.actConfig.AttachBool.animBool = true;
                    //    Debug.LogError($"动画 >>>>> {SourceCard.card.actConfig.AttachBool.animStr}，bool={SourceCard.card.actConfig.AttachBool.animBool}");
                    //}
                }
                else if (SourceCard != null && TargetCards != null && trigger == "SpecialAbility")
                {
                    if (isMoveAction)
                    {
                        if (SourceCard.card.actConfig == null)
                        {
                            SourceCard.card.actConfig = new ActingConfig();
                        }

                        SourceCard.card.actConfig.ActingNode = SourceCard;
                        SourceCard.card.actConfig.TargetNodes = TargetCards;
                        SourceCard.card.actConfig.moveTo = m_moveTo;
                    }
                }

                animator.SetBool("CanMove", isMoveAction);
                if (trigger!= null && !trigger.Equals(""))
                {
                    animator.SetTrigger(trigger);
                }
            }
        }
        else
        {
            Debug.LogError("Behaviour animator >>>> " + animator);
        }
    }
}
