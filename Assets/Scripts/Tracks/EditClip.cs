
using System;
using UnityEngine;
using UnityEngine.Playables;

using Sirenix.OdinInspector;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System.Linq;
using System.IO;

public class ConfigUti
{
    public static void CheckDir(string path)
    {

        var dir = Path.GetDirectoryName(path);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
    }
}
public class TimelineConfig<T> : ScriptableObject
{
    public T config;
}


[Serializable]
public class EditClip<T,ConfigAsset> : PlayableAsset  where T:PlayableBehaviour,iEditClip,new() where ConfigAsset:TimelineConfig<T>
{
//	public class ConfigAsset :TimelineConfig<T>
//	{ 
//
//	}
	public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
	{ 
		return default(Playable);
	} 

//	public T template = new T();
	protected virtual T Template {
		get ;
		set ;
	}

 

	[Title("下拉选择配置类型")]
	[InlineButton("CreateTemplate","Create Template")]
	[OnValueChanged("ApplyConfig")]
	[ValueDropdown("GetListOfMonoBehaviours")]
	[ShowInInspector]
	public string ConfigId {
		get {
			return Template.configId;
		}
		set {
			Template.configId = value;
		}
	} 

	protected virtual string GroupId (){
		return "default"; 
	}
#if UNITY_EDITOR 
	string config_path;

	public string AssetPath {
		get {
			config_path = string.Format ("Assets/EditorConfig/{0}/{1}.asset", GroupId(), ConfigId);
			return config_path;
		}
	}

	private System.Collections.Generic.IEnumerable<string> GetListOfMonoBehaviours()
	{  
		var objs = AssetDatabase.FindAssets (string.Format ("t:{0}", typeof(ConfigAsset).Name))
			.Select (x => UnityEditor.AssetDatabase.GUIDToAssetPath (x))
			.Select (x => Path.GetFileNameWithoutExtension (x));

		return objs; 
	}
	void ApplyConfig()
	{
		if(File.Exists (AssetPath)){
			var obj = UnityEditor.AssetDatabase.LoadAssetAtPath<ConfigAsset>(AssetPath);
			if(obj)
			{
				this.Template = (T)obj.config.Clone ();
			}
		}
	}
	protected void CreateTemplate()
	{
		var obj = ScriptableObject.CreateInstance <ConfigAsset> ();
		obj.config = (T)Template.Clone (); 
		ConfigUti.CheckDir (AssetPath);
		UnityEditor.AssetDatabase.CreateAsset (obj,AssetPath);
		UnityEditor.AssetDatabase.Refresh ();
		obj = UnityEditor.AssetDatabase.LoadAssetAtPath<ConfigAsset>( AssetPath);
		UnityEditor.Selection.activeObject = obj;
	}
	#endif
}



public interface iEditClip{
	string configId{ get; set;}
}