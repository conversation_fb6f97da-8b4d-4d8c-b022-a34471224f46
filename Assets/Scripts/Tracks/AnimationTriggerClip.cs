using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;
using System.Collections.Generic;

[Serializable]

public class AnimationTriggerClip : PlayableAsset, ITimelineClipAsset
{
    public AnimationTriggerBehaviour template = new AnimationTriggerBehaviour();

    public Animator animator { get; set; }

    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dests;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }
    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<AnimationTriggerBehaviour>.Create(graph, template);
        AnimationTriggerBehaviour clone = playable.GetBehaviour();
        clone.animator = animator;

        clone.SourceCard = source;
        if (dests != null )
        {
            clone.TargetCards = dests;
        }
        //Debug.LogError("Clip animator >>>> " + animator);
        return playable;
    }
}
