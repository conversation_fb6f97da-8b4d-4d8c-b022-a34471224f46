using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

public class ParticleSystemStencilDriver : ParticleSystemDriver
{
    public ParticleSystemDriver particleSystemDriver;
    public readonly Dictionary<Renderer, StencilRenderer> rendererStencilSet = new Dictionary<Renderer, StencilRenderer>();

    public ParticleSystemStencilDriver(GameObject prefab) : base(prefab)
    {
    }

    public bool IsMaterialSupportStencil(Material material)
    {
        if (!material)
        {
            return false;
        }
        if (!material.HasProperty(StencilParameter.StencilID_Name)
            || !material.HasProperty(StencilParameter.StencilID_Name)
            || !material.HasProperty(StencilParameter.StencilOp_Name))
        {
            return false;
        }
        return true;
    }

    public void InitStencilData()
    {
        ClearStencilData();
        if (instance == null)
        {
            return;
        }

        var rendererSet = instance.GetComponentsInChildren<Renderer>();
        foreach (var renderer in rendererSet)
        {
            var stencilData = new StencilRenderer()
            {
                renderer = renderer,
                stencilParameters = new List<StencilParameter>()
            };
            var sharedMaterials = renderer.sharedMaterials;
            foreach (var sharedMaterial in sharedMaterials)
            {
                var stencilParameter = new StencilParameter()
                {
                    isSupportStencil = IsMaterialSupportStencil(sharedMaterial)
                };
                if (stencilParameter.isSupportStencil)
                {
                    stencilParameter.orgStencilID = sharedMaterial.GetInt(StencilParameter.StencilID_Name);
                    stencilParameter.orgCompareFunction = (CompareFunction)sharedMaterial.GetInt(StencilParameter.CompareFunction_Name);
                    stencilParameter.orgStencilOp = (StencilOp)sharedMaterial.GetInt(StencilParameter.StencilOp_Name);
                }
                stencilData.stencilParameters.Add(stencilParameter);
            }

            rendererStencilSet[renderer] = stencilData;
        }
    }

    public void SetStencil(int stencilID, CompareFunction compareFunction, StencilOp stencilOp = StencilOp.Keep)
    {
        StencilRenderer stencilData;
        foreach (var rendererStencil in rendererStencilSet)
        {
            stencilData = rendererStencil.Value;
            stencilData.SetStencil(stencilID, compareFunction, stencilOp);
        }
    }

    /// <summary>
    /// 还原修改过的遮罩设置
    /// </summary>
    public void ClearStencilData()
    {
        if (rendererStencilSet.Count == 0)
        {
            return;
        }

        StencilRenderer stencilData;
        foreach (var rendererStencil in rendererStencilSet)
        {
            stencilData = rendererStencil.Value;
            stencilData.Clear();
        }
        rendererStencilSet.Clear();
    }

    public override void Destroy()
    {
        ClearStencilData();
        base.Destroy();
    }

    public override void Clear()
    {
        ClearStencilData();
        base.Clear();
    }
}
