using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

public class CrazingClip : PlayableAsset, ITimelineClipAsset
{
    public CrazingBehaviour template = new CrazingBehaviour();

    [NonSerialized]
    public BattleActorNode source;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<CrazingBehaviour>.Create(graph, template);
        var clone = playable.GetBehaviour();
        clone.source = source;
        clone.drivers = new List<ParticleSystemDriver>();

        return playable;
    }
}
