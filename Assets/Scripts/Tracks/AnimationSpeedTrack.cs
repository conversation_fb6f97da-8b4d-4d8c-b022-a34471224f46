using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.368f, 0.458f, 0.024f)]
[TrackClipType(typeof(AnimationSpeedClip))]
[TrackBindingType(typeof(BattleActorNode))]
public class AnimationSpeedTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        BattleActorNode node = director.GetGenericBinding(this) as BattleActorNode;
        if (node == null || node.card == null)
            return;

        var so = new UnityEditor.SerializedObject(node.card.animator);
        var iter = so.GetIterator();
        while (iter.NextVisible(true))
        {
            if (iter.hasVisibleChildren)
                continue;
            driver.AddFromName<Animator>(node.card.animator.gameObject, iter.propertyPath);
        }
#endif
        base.GatherProperties(director, driver);
    }
}
