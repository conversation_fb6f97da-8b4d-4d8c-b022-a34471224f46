using System;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;
using System.Collections.Generic;


[Serializable]
public class EventBehaviour : PlayableBehaviour, iEditClip
{
	#region iEditClip implementation
	public string configId
	{
		get
		{
			return eventName;
		}
		set
		{
			eventName = value;
		}
	}
	#endregion

	public string eventName;
	public string param;

	public UnityEngine.Object bindObject;

	public Episode bindEvent = null;

	private bool excuted = false;

	public override void OnGraphStop(Playable playable)
	{
		if (excuted == false)
			ExeEvent();
	}
	public override void OnBehaviourPlay(Playable playable, FrameData info)
	{
		if (excuted == false)
		{
			ExeEvent();
			excuted = true;
		}
	}

	void ExeEvent()
	{
;		SkillEditorSceneTest();
		if (bindEvent != null && bindEvent.player)
		{
			bindEvent.player.OnEventEpisode(eventName, param, bindEvent, bindObject);
        }
        else
        {
			BattlePlayer.Instance?.OnEventEpisode(eventName, param, bindEvent, bindObject);
        }
	}

	/// <summary>
	/// ֻ��Editor��Ӧ�ã�������Ч------wl
	/// </summary>
	void SkillEditorSceneTest()
	{
#if UNITY_EDITOR
		if (eventName == "OnLuaCallback" && param == "OnCinemaChine")
		{
			ShakeScreenCfgAsset assets = bindObject as ShakeScreenCfgAsset;
			if (assets == null || assets.shakeScreenCfg == null || assets.shakeScreenCfg.signalSource == null)
			{
				Debug.LogError("����" + assets.name + "��Դ�Ƿ�Ϊ��");
				return;
			}

			GameObject go = GameObject.Find("CinemachineSourceObj");
			if (go == null)
			{
				go = new GameObject("CinemachineSourceObj");
				go.AddComponent<Cinemachine.CinemachineImpulseSource>();
			}
			Cinemachine.CinemachineImpulseSource ImpulseSource = go.GetComponent<Cinemachine.CinemachineImpulseSource>();
			ImpulseSource.m_ImpulseDefinition.m_AmplitudeGain = assets.shakeScreenCfg.amplitudeGain;
			ImpulseSource.m_ImpulseDefinition.m_FrequencyGain = assets.shakeScreenCfg.frequencyGain;
			ImpulseSource.m_ImpulseDefinition.m_TimeEnvelope.m_AttackTime = assets.shakeScreenCfg.duration;
			ImpulseSource.m_ImpulseDefinition.m_RawSignal = assets.shakeScreenCfg.signalSource as Cinemachine.SignalSourceAsset;
			ImpulseSource.GenerateImpulse();
		}
#endif
	}
}