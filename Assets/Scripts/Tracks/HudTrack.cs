using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.365f, 0.789f, 0.144f)]
[TrackClipType(typeof(HudClip))]
[TrackBindingType(typeof(BattleActorNode))]

public class HudTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        BattleActorNode node = director.GetGenericBinding(this) as BattleActorNode;

        foreach (TimelineClip clip in GetClips())
        {
            HudClip playableAsset = clip.asset as HudClip;

            if (playableAsset)
            {
                playableAsset.node = node;
            }
        }
        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        var comp = director.GetGenericBinding(this) as Animator;
        if (comp == null)
            return;
        var so = new UnityEditor.SerializedObject(comp);
        var iter = so.GetIterator();
        while (iter.NextVisible(true))
        {
            if (iter.hasVisibleChildren)
                continue;
            driver.AddFromName<Animator>(comp.gameObject, iter.propertyPath);
        }
#endif
        base.GatherProperties(director, driver);
    }
}
