using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class FlashBehaviour : PlayableBehaviour
{
    public GameObject prefab;
    private bool flashed = false;

    [NonSerialized]
    public Transform parent = null;
    [NonSerialized]
    public List<BattleActorNode> nodes = new List<BattleActorNode>();
    [NonSerialized]
    public List<ParticleSystemDriver> drivers = null;

    public override void OnGraphStart(Playable playable)
    {
        if (nodes != null && nodes.Count != 0)
        {
            foreach (BattleActorNode node in nodes)
            {
                ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
                if (Application.isPlaying)
                {
                    driver.PrepareParticleSystem((float)playable.GetDuration());
                }
                drivers.Add(driver);
            }
        }
        else if (parent != null)
        {
            // 兼容编辑器预览，但是没法校对位置缩放
            ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
            driver.Attach(parent, Vector3.zero);
            if (Application.isPlaying)
            {
                driver.PrepareParticleSystem((float)playable.GetDuration());
            }
            drivers.Add(driver);
        }
    }
    public override void OnGraphStop(Playable playable)
    {
        foreach (ParticleSystemDriver driver in drivers)
            driver.Destroy();

        drivers.Clear();
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        foreach (ParticleSystemDriver driver in drivers)
            driver.Clear();
    }

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (nodes != null && nodes.Count != 0)
        {
            for (int i = 0; i < drivers.Count; ++i)
            {
                ParticleSystemDriver driver = drivers[i];
                driver.Attach(nodes[i]);
                driver.Play();
            }
        }
        else
        {
            for (int i = 0; i < drivers.Count; ++i)
            {
                ParticleSystemDriver driver = drivers[i];
                driver.Play();
            }
        }
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        float time = (float)playable.GetTime();
        float duration = (float)playable.GetDuration();

        foreach (ParticleSystemDriver driver in drivers)
            driver.Simulate(time, duration);
    }
}
