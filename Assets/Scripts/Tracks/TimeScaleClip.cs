using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class TimeScaleClip : PlayableAsset, ITimelineClipAsset
{
    public TimeScaleBehaviour template = new TimeScaleBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<TimeScaleBehaviour>.Create(graph, template);
        TimeScaleBehaviour behaviour = playable.GetBehaviour();
        return playable;
    }
}
