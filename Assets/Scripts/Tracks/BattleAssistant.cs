using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using UnityEngine.UI;

namespace War.Battle
{
    public class BattleAssistant : MonoBehaviour
    {
        BattlePlayer player = null;

        private void Awake()
        {
            player = GetComponent<BattlePlayer>();
        }

        private void OnEnable()
        {
            BattleEvent.OnRoundActionBind -= OnActionBind;
            BattleEvent.OnRoundActionBind += OnActionBind;
        }
      
        public static BattleTracksBinding trackbinding = new BattleTracksBinding();
        void OnActionBind(UnityEngine.Playables.PlayableDirector director, UnityEngine.Timeline.TimelineAsset timeline, EpisodeAction e)
        {
            foreach (PlayableBinding binding in timeline.outputs)
            {
                trackbinding.Bind(director, binding, e);
            }
        }
    }
}