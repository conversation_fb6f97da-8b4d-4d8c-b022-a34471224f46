using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class FlashClip : PlayableAsset, ITimelineClipAsset
{
    public FlashBehaviour template = new FlashBehaviour();

    [NonSerialized]
    public Transform parent;
    [NonSerialized]
    public List<BattleActorNode> nodes = new List<BattleActorNode>();

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<FlashBehaviour>.Create(graph, template);
        FlashBehaviour behaviour = playable.GetBehaviour();
        behaviour.parent = parent;
        behaviour.nodes = nodes;
        behaviour.drivers = new List<ParticleSystemDriver>();
        return playable;
    }
}
