using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class SpriteSelfLightBehaviour : PlayableBehaviour
{
    public float lightness = 0;
    public AnimationCurve curve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

    [NonSerialized]
    public BattleActorNode node = null;

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node && node.card)
        {
            float time = (float)playable.GetTime() / (float)playable.GetDuration();
            float value = curve.Evaluate(time);
            node.card.SetSpriteSelfLight(value);
        }
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        switch (info.evaluationType)
        {
            case FrameData.EvaluationType.Evaluate:
                if (info.weight == 0)
                {

                }
                break;
            case FrameData.EvaluationType.Playback:
                if (info.deltaTime != 0)
                {
                    Cleanup();
                }
                break;
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        Cleanup();
    }

    public void Cleanup()
    {
        if (node && node.card)
        {
            node.card.SetSpriteSelfLight(curve.Evaluate(1));
        }
    }
}
