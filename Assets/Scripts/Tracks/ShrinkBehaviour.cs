using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class ShrinkBehaviour : PlayableBehaviour
{
    public enum ShrinkType
    {
        custom,
        attack,
        suffer,
    }
    public ShrinkType shrink = ShrinkType.custom;
    public AnimationCurve animation;
    private BattleActorNode node; 

    // 预置两种缩放类型
    public static AnimationCurve attackCurve = new AnimationCurve
    (
        new Keyframe(0f, 1f, 0f, 0f),
        new Keyframe(5f / 64f, 0.8925696f, 0f, 0f),
        new Keyframe(10f / 64f, 1f, 0f, 0f),
        new Keyframe(15f / 64f, 0.8925696f, 0f, 0f),
        new Keyframe(20f / 64f, 1f, 0f, 0f),
        new Keyframe(44f / 64f, 1f, 0f, 0f),
        new Keyframe(47f / 64f, 0.862918f, 0f, 0f),
        new Keyframe(1f, 1f, 0f, 0f)
    );

    public static AnimationCurve sufferCurve = new AnimationCurve
    (
        new Keyframe(0f, 1f, 0f, 0f),
        new Keyframe(5f / 39f, 0.77472f, 0f, 0f),
        new Keyframe(13f / 39f, 1.08878f, 0, 0),
        new Keyframe(22f / 39f, 0.85357f, 0, 0),
        new Keyframe(1f, 1, 0, 0)
    );

    public override void OnGraphStart(Playable playable)
    {
        double duration = playable.GetDuration();
        if (Mathf.Approximately((float)duration, 0f))
            throw new UnityException("A TransformTween cannot have a duration of zero.");

        switch (shrink)
        {
            default:
            case ShrinkType.custom:
                break;
            case ShrinkType.attack:
                animation = attackCurve;
                break;
            case ShrinkType.suffer:
                animation = sufferCurve;
                break;
        }
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        switch (info.evaluationType)
        {
            case FrameData.EvaluationType.Evaluate:
                if (info.weight == 0)
                {

                }
                break;
            case FrameData.EvaluationType.Playback:
                if (info.deltaTime != 0)
                {
                    if (node)
                        node.SetScale(1);
                }
                break;
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        // unity bug OnBehaviourPause for info.deltaTime != 0 有时候没有调用，在这里再补调一次。
        if (node)
            node.SetScale(1);
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node == null)
            node = playerData as BattleActorNode;

        if (node != null)
        {
            float time = (float)(playable.GetTime()/playable.GetDuration());
            float scale = animation.Evaluate(time);
            node.SetScale(scale);
        }
    }
}
