using UnityEngine;
using UnityEngine.Rendering;

public class StencilParameter
{
    public static string StencilID_Name = "_Stencil";
    public static string CompareFunction_Name = "_StencilComp";
    public static string StencilOp_Name = "_StencilOp";

    public bool isSupportStencil = false;

    public int orgStencilID = 0;
    public CompareFunction orgCompareFunction = CompareFunction.Always;
    public StencilOp orgStencilOp = StencilOp.Keep;

    public int stencilID = 0;
    public CompareFunction compareFunction = CompareFunction.Always;
    public StencilOp stencilOp = StencilOp.Keep;

    public void SetMaterialStencil(Material material, int stencilID, CompareFunction compareFunction, StencilOp stencilOp = StencilOp.Keep)
    {
        if (material == null || !isSupportStencil)
        {
            return;
        }

        if (this.stencilID != stencilID)
        {
            this.stencilID = stencilID;
            material.SetInt(StencilID_Name, stencilID);
        }
        if (this.compareFunction != compareFunction)
        {
            this.compareFunction = compareFunction;
            material.SetInt(CompareFunction_Name, (int)compareFunction);
        }
        if (this.stencilOp != stencilOp)
        {
            this.stencilOp = stencilOp;
            material.SetInt(StencilOp_Name, (int)stencilOp);
        }
    }
}
