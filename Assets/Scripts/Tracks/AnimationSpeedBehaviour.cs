using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class AnimationSpeedBehaviour : PlayableBehaviour
{
    public AnimationCurve speedCurve;
    public float speed = 1.0f;
    public bool revertWhenEnd = true;
    private float oldSpeed = 1.0f;
    private Animator animator = null;
    private float inverseDuration;

    public override void OnGraphStart(Playable playable)
    {
        inverseDuration = 1.0f / (float)playable.GetDuration();
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (animator == null)
        {
            BattleActorNode node = playerData as BattleActorNode;
            if (node && node.card)
            {
                animator = node.card.animator;
                oldSpeed = animator.speed;
                node.card.MarkResetting(CardResetting.AnimationSpeed);
            }
        }

        if (animator)
        {
            if (speedCurve.length != 0)
            {
                float time = (float)playable.GetTime() * inverseDuration;
                float speed = speedCurve.Evaluate(time);
                animator.speed = speed;
            }
            else
            {
                animator.speed = speed;
            }
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        if (revertWhenEnd && animator)
        {
            animator.speed = oldSpeed;
        }
    }


}
