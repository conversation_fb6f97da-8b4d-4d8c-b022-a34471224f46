using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;

[TrackColor(0.25f, 0.86f, 0.61f)]
[TrackClipType(typeof(MoveAttackClip))]
[TrackBindingType(typeof(Transform))]
[Serializable]
public class MoveAttackTrack: TrackAsset
{
    public int segment = 0;

    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dest;
    [NonSerialized]
    public List<int> targetPosSet;
    [NonSerialized]
    public CinemachineController cineControl;
    [NonSerialized]
    public Animator animator;
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        Transform parent = director.GetGenericBinding(this) as Transform;

        foreach (TimelineClip clip in GetClips())
        {
            MoveAttackClip playableAsset = clip.asset as MoveAttackClip;

            if (playableAsset && parent)
            {
                playableAsset.parent = parent;
                playableAsset.source = source;
                playableAsset.dest = dest;
                playableAsset.targetPosSet = targetPosSet;
                playableAsset.cineControl = cineControl;
                playableAsset.animator = animator;
            }
        }

        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }
}
