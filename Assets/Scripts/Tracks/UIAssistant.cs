using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;


public class UIAssistant : MonoBehaviour
{
    public PlayableDirector director;
    public BattleActorNode node;
    public AudioSource audioSource;

    public delegate void EventHandler(string eventName);
    public EventHandler onEvent;

    private Coroutine routine;

    public void Play(TimelineAsset timeline)
    {
        director.playableAsset = timeline;
        Bind(director, timeline);
        routine = StartCoroutine(PlayWork());
    }

    public void Stop()
    {
        if (routine != null)
        {
            StopCoroutine(routine);
            routine = null;
        }

        if (IsPlaying())
        {
            director.Stop();
            director.playableAsset = null;
        }
    }

    public void RegisterHandler(EventHandler handler)
    {
        onEvent += handler;
    }

    IEnumerator PlayWork()
    {
        if (onEvent != null)
            onEvent("PlayBegin");

        director.Play();
        while (IsPlaying())
            yield return null;

        if (onEvent != null)
            onEvent("PlayEnd");
    }

    public bool IsPlaying()
    {
        return director.state == PlayState.Playing;
    }

    public void Bind(PlayableDirector director, TimelineAsset timeline)
    {
        foreach (PlayableBinding binding in timeline.outputs)
        {
            Type type = binding.sourceObject.GetType();
            BindingHandler handler = null;
            if (bindings.TryGetValue(type, out handler))
            {
                handler(binding, this);
            }
        }
    }

    #region track binding
    static UIAssistant()
    {
        bindings.Add(typeof(AnimationSpeedTrack), BindingToAnimator);
        bindings.Add(typeof(AnimationTrack), BindingToAnimator);
        bindings.Add(typeof(AnimationTriggerTrack), BindingToAnimator);
        bindings.Add(typeof(AudioTrack), BindingAudioTrack);
        bindings.Add(typeof(CardDissolveTrack), BindingToNode);
        bindings.Add(typeof(CardSkillTrack), BindingToNode);
        //bindings.Add(typeof(CameraShakeTrack), BindingCameraShakeTrack);
        //bindings.Add(typeof(EventTrack), BindingEventTrack);
        bindings.Add(typeof(FlashTrack), BindingFlashTrack);
        bindings.Add(typeof(GraynessTrack), BindingToNode);
        //bindings.Add(typeof(HudTrack), BindingHudTrack);
        bindings.Add(typeof(JumpoutTrack), BindingToNode);
        bindings.Add(typeof(ParticleSpawnTrack), BindingParticleSpawnTrack);
        bindings.Add(typeof(ParticleAttackTrack), BindingParticleAttackTrack);
        bindings.Add(typeof(ShrinkTrack), BindingToNode);
        //bindings.Add(typeof(SpriteColorTrack), BindingToNode);
        bindings.Add(typeof(TransformAnimationTrack), BindingToNode);
    }

    private delegate void BindingHandler(PlayableBinding binding, UIAssistant assistant);
    static Dictionary<Type, BindingHandler> bindings = new Dictionary<Type, BindingHandler>();

    private static void BindingToAnimator(PlayableBinding binding, UIAssistant assistant)
    {
        assistant.director.SetGenericBinding(binding.sourceObject, assistant.node.card.animator);
    }

    private static void BindingToNode(PlayableBinding binding, UIAssistant assistant)
    {
        assistant.director.SetGenericBinding(binding.sourceObject, assistant.node);
    }
    
    private static void BindingToSkillConfig(PlayableBinding binding, UIAssistant assistant)
    {
        SkillEffectConfig config = assistant.gameObject.GetComponent<SkillEffectConfig>();
        assistant.director.SetGenericBinding(binding.sourceObject, config);
    }

    private static void BindingAudioTrack(PlayableBinding binding, UIAssistant assistant)
    {
        assistant.director.SetGenericBinding(binding.sourceObject, assistant.audioSource);
    }

    private static void BindingAnimationSpeedTrack(PlayableBinding binding, UIAssistant assistant)
    {
        assistant.director.SetGenericBinding(binding.sourceObject, assistant.node.card.animator);
    }

    private static void BindingFlashTrack(PlayableBinding binding, UIAssistant assistant)
    {
        FlashTrack track = (FlashTrack)binding.sourceObject;
        switch (track.bindingStamp)
        {
            case TrackBindingStamp.Binding_SrcArea:
            case TrackBindingStamp.Binding_Src:
                assistant.director.SetGenericBinding(binding.sourceObject, assistant.node);
                break;
        }
    }

    private static void BindingParticleSpawnTrack(PlayableBinding binding, UIAssistant assistant)
    {
        ParticleSpawnTrack track = (ParticleSpawnTrack)binding.sourceObject;
        switch (track.bindingStamp)
        {
            case TrackBindingStamp.Binding_Default:
            case TrackBindingStamp.Binding_Src:
                assistant.director.SetGenericBinding(binding.sourceObject, assistant.node.transform);
                break;
            case TrackBindingStamp.Binding_Dest:
                break;
        }
    }

    private static void BindingParticleAttackTrack(PlayableBinding binding, UIAssistant assistant)
    {
        ParticleAttackTrack track = (ParticleAttackTrack)binding.sourceObject;
        track.source = assistant.node;
        track.dest = null;
        assistant.director.SetGenericBinding(binding.sourceObject, assistant.director.transform);
    }

    #endregion
}
