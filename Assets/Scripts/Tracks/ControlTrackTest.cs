using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.857f, 0.658f, 0.244f)]
[TrackClipType(typeof(CameraShakeClip))]
[TrackBindingType(typeof(Animator))]

public class ControlTrackTest : ControlTrack
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
        base.GatherProperties(director, driver);
    }
}
