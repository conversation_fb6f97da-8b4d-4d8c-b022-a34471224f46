using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class HudBehaviour : PlayableBehaviour
{
    public AnimationCurve alpha = new AnimationCurve(new Keyframe(0, 1), new Keyframe(1, 0));

    [NonSerialized]
    public BattleActorNode node;

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (node && node.Hud)
            node.Hud.Clear();
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node && node.Hud)
        {
            float time = (float)(playable.GetTime() / playable.GetDuration());
            node.Hud.SetAlpha(alpha.Evaluate(time));
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        if (node && node.Hud)
            node.Hud.SetAlpha(1);
    }
}

