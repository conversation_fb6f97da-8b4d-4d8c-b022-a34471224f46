using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;
[TrackColor(0.25f, 0.86f, 0.61f)]
[TrackClipType(typeof(OutsideSkillClip))]
[TrackBindingType(typeof(BattleOutsideSkillManager))]
[Serializable]
public class OutsideSkillTrack : TrackAsset
{
    [NonSerialized]
    public BattlePlayer player;
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        BattleOutsideSkillManager manager = director.GetGenericBinding(this) as BattleOutsideSkillManager;
        foreach (TimelineClip clip in GetClips())
        {
            OutsideSkillClip playableAsset = clip.asset as OutsideSkillClip;
            if (playableAsset)
            {
                playableAsset.manager = manager;
                playableAsset.player = player;
            }
        }

        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }
}
