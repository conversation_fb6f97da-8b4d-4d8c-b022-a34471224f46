using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;

[Serializable]
public class MoveAttackClip : PlayableAsset, ITimelineClipAsset
{
    public MoveAttackBehaviour template = new MoveAttackBehaviour();

    [NonSerialized]
    public Transform parent;
    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public List<BattleActorNode> dest;
    [NonSerialized]
    public List<int> targetPosSet;
    [NonSerialized]
    public CinemachineController cineControl;
    [NonSerialized]
    public Animator animator;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<MoveAttackBehaviour>.Create(graph, template);
        MoveAttackBehaviour clone = playable.GetBehaviour();
        clone.parent = parent;
        clone.source = source;
        if (dest != null && dest.Count > 0)
        {
            clone.dest = dest[0];
            clone.destPosition = targetPosSet[0];
        }
        else
        {
            clone.dest = null;
            clone.destPosition = -1;
        }
        clone.animator = animator;
        clone.cineControl = cineControl;
        return playable;
    }
}
