using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;

[TrackColor(0.4448276f, 0f, 1f)]
[TrackClipType(typeof(EventClip))]
//[TrackBindingType(typeof(Episode))]
public class EventTrack : TrackAsset
{
    public int segment = 0;
    public Episode bindEpisode;
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        var director = go.GetComponent<PlayableDirector>();
//		var trackTargetObject = director.GetGenericBinding(this) as Episode;

        foreach (var clip in GetClips())
        {
            var playableAsset = clip.asset as EventClip;

            if (playableAsset)
            {
				if (bindEpisode!=null)
                {
					playableAsset.bindEvent = bindEpisode;
                }
				//else{
				//	Debug.LogError ("bindEpisode err null");
				//}
            }
        }

        var scriptPlayable = ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
        return scriptPlayable;
    }
}
