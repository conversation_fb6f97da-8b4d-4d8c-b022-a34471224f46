using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;

#if UNITY_EDITOR
using Sirenix.OdinInspector;
using UnityEditor;
using System.Linq;
using System.IO;
#endif

[Serializable]
public class EventClip :
#if UNITY_EDITOR
 EditClip<EventBehaviour,EventAsset>
#else
PlayableAsset
#endif
,ITimelineClipAsset
{
	#if UNITY_EDITOR 
	protected override string GroupId (){
		return "Event"; 
	}

	protected override EventBehaviour Template {
		get {
			return template;
		}
		set { 
			template = value;
		}
	}  
	#endif
	public EventBehaviour template = new EventBehaviour();


	public Episode bindEvent { get; set; }
  
    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    } 

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<EventBehaviour>.Create(graph, template);
        EventBehaviour clone = playable.GetBehaviour();
        clone.bindEvent = bindEvent;
        return playable;
    }  

}
