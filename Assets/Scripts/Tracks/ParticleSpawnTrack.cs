using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.38f, 0f, 0.13f)]
[TrackClipType(typeof(ParticleSpawnClip))]
[TrackBindingType(typeof(Transform))]
public class ParticleSpawnTrack : TrackAsset
{
    public TrackBindingStamp bindingStamp = TrackBindingStamp.Binding_Default;
    public int segment = 0;

    [NonSerialized]
    public List<Transform> parents = new List<Transform>();

    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        Transform parent = director.GetGenericBinding(this) as Transform;

        foreach (TimelineClip clip in GetClips())
        {
            ParticleSpawnClip playableAsset = clip.asset as ParticleSpawnClip;

            if (playableAsset)
            {
                if (parent)
                {
                    playableAsset.parent = parent;
                    playableAsset.parents = parents;
                }
            }
        }

        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        var comp = director.GetGenericBinding(this) as Transform;
        if (comp == null)
            return;
        var so = new UnityEditor.SerializedObject(comp);
        var iter = so.GetIterator();
        while (iter.NextVisible(true))
        {
            if (iter.hasVisibleChildren)
                continue;
            driver.AddFromName<Transform>(comp.gameObject, iter.propertyPath);
        }
#endif
        base.GatherProperties(director, driver);
    }
}
