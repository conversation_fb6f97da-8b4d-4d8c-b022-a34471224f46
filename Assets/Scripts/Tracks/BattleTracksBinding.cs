//#define DEBUG_ACTION

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using System.Text;
using War.Base;
using Cinemachine;

namespace War.Battle
{
    public class BattleTracksBinding
    {
        public delegate void BindingHandler(PlayableDirector director, PlayableBinding binding, EpisodeAction action);
        Dictionary<Type, BindingHandler> bindings = new Dictionary<Type, BindingHandler>();
        public BattleTracksBinding()
        {
            bindings.Add(typeof(AnimationSpeedTrack), BindingAnimationSpeedTrack);
            bindings.Add(typeof(AnimationTrack), BindingAnimationTrack);
            bindings.Add(typeof(AnimationTriggerTrack), BindingAnimationTriggerTrack);
            bindings.Add(typeof(AudioTrack), BindingAudioTrack);
            bindings.Add(typeof(AudioExTrack), BindingAudioExTrack);
            bindings.Add(typeof(CardDissolveTrack), BindingCardDissolveTrack);
            bindings.Add(typeof(CameraShakeTrack), BindingCameraShakeTrack);
            bindings.Add(typeof(EventTrack), BindingEventTrack);
            bindings.Add(typeof(FlashTrack), BindingFlashTrack);
            bindings.Add(typeof(GraynessTrack), BindingGraynessTrack);
            bindings.Add(typeof(HudTrack), BindingHudTrack);
            bindings.Add(typeof(JumpoutTrack), BindingJumpoutTrack);
            bindings.Add(typeof(ParticleSpawnTrack), BindingParticleSpawnTrack);
            bindings.Add(typeof(ParticleAttackTrack), BindingParticleAttackTrack);
            bindings.Add(typeof(ShrinkTrack), BindingShrinkTrack);
            //bindings.Add(typeof(SpriteColorTrack), BindingSpriteColorTrack);
            bindings.Add(typeof(TransformAnimationTrack), BindingTransformAnimationTrack);
            bindings.Add(typeof(MoveAttackTrack), BindingMoveAttackTrack);
            bindings.Add(typeof(CrazingTrack), BindCrazingTrack);
            //bindings.Add(typeof(SpriteSelfLightTrack), BindingSpriteSelfLightTrack);
            bindings.Add(typeof(CinemachineTrack), BindingChinemachine);
            bindings.Add(typeof(OutsideSkillTrack), BindingOutsideSkillTrack);
            
        }

        public void Bind(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            if (string.Equals(binding.streamName, ("Markers")))
            {
                return;
            }
            if(binding.sourceObject==null)
            {
                "".PrintError("BattleTracksBinding bind error binding.sourceObject==null", director.gameObject.name);
                
                return;
            }
            Type type = binding.sourceObject.GetType();
            BindingHandler handler = null;
            if (bindings.TryGetValue(type, out handler))
            {
                handler(director, binding, action);
            }
        }

        private void BindingAnimationTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            director.SetGenericBinding(binding.sourceObject, action.role.node.card.animator);
        }
        private void BindingAudioTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            AudioTrack track = (AudioTrack)binding.sourceObject;
            foreach (TimelineClip c in track.GetClips())
                c.timeScale = Time.timeScale;

            director.SetGenericBinding(binding.sourceObject, action.role.audio);
        }

        private void BindingAudioExTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            BindingAudioTrack(director, binding, action);

            AudioExTrack track = binding.sourceObject as AudioExTrack;
            bool bValid = action.IsTrackValid(track.segment);
            track.muted = !bValid;
        }

        private void BindingAnimationSpeedTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingEventTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            EventTrack track = binding.sourceObject as EventTrack;
            track.bindEpisode = action;
            bool bValid = action.IsTrackValid(track.segment);
            track.muted = !bValid;
        }

        private void BindingAnimationTriggerTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            if (action.role.node.card == null) return;
            director.SetGenericBinding(binding.sourceObject, action.role.node.card.animator);

            AnimationTriggerTrack track = binding.sourceObject as AnimationTriggerTrack;
            bool bValid = action.IsTrackValid(track.segment);
            track.muted = !bValid;

            Role role = action.role;
            BattlePlayer player = role.player;
            if (player && action.targets.Count > 0 && action.targets.Count > track.segment)
            {
                List<uint> targets = action.targets[track.segment];
                List<BattleActorNode> dests = new List<BattleActorNode>();
                Role targetRole;
                foreach (uint target in targets)
                {
                    targetRole = player.GetRole(target);
                    if (targetRole != null)
                    {
                        dests.Add(targetRole.node);
                    }
                    else
                    {
                        Debug.LogError($"Get target Error >>> {target}");
                    }
                }
                track.dests = dests;
                track.source = action.role.node;
            }
        }

        private void BindingParticleSpawnTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            ParticleSpawnTrack track = (ParticleSpawnTrack)binding.sourceObject;

            Role role = action.role;
            BattlePlayer player = role.player;
            List<uint> targets = action.targets[track.segment];

            Transform bindingTransform = null;
            switch (track.bindingStamp)
            {
                case TrackBindingStamp.Binding_Default:
                case TrackBindingStamp.Binding_Src:
                    bindingTransform = role.roleGO.transform;
                    break;
                case TrackBindingStamp.Binding_Dest:
                    if (targets.Count > 1)
                    {
                        track.parents = new List<Transform>();
                        foreach (uint i in targets)
                        {
                            Role targetRole = player.GetRole(i);
                            if (targetRole != null)
                            {
                                track.parents.Add(targetRole.roleGO.transform);
                            }
                        }
                    }
                    else if (targets.Count == 1)
                    {
                        Role targetRole = player.GetRole(targets[0]);
                        if (targetRole != null)
                        {
                            bindingTransform = (targetRole.roleGO.transform);
                        }
                    }
                    break;
                //case TrackBindingStamp.Binding_SrcArea:
                //    bindingTransform = player.layout.GetSrcArea(role.position);
                //    break;
                //case TrackBindingStamp.Binding_DestArea:
                //    bindingTransform = player.layout.GetDestArea(role.position);
                //    break;
            }

            director.SetGenericBinding(binding.sourceObject, bindingTransform);
        }

        //private void BindingSpriteColorTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        //{
        //    SpriteColorTrack track = binding.sourceObject as SpriteColorTrack;
        //    BattleActorNode node = action.role.node;

        //    if (track.segment < action.targets.Count)
        //    {
        //        List<uint> targets = action.targets[track.segment];
        //        BattlePlayer player = action.player;

        //        switch (track.bindingStamp)
        //        {
        //            case TrackBindingStamp.Binding_Default:
        //            case TrackBindingStamp.Binding_Src:
        //                break;
        //            case TrackBindingStamp.Binding_Dest:
        //                node = player.GetRole(targets[0]).node;
        //                break;
        //        }
        //    }
        //    director.SetGenericBinding(binding.sourceObject, node);
        //}

        private void BindCrazingTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            CrazingTrack track = (CrazingTrack)binding.sourceObject;
            Role role = action.role;
            BattlePlayer player = role.player;

            track.source = action.role.node;

            Transform roleTrans = role.GetRoleTransform();
            if (roleTrans)
            {
                director.SetGenericBinding(binding.sourceObject, roleTrans);
            }
            else
            {
                Debug.LogError("BindCrazingTrack can not find role's root transform");
                director.SetGenericBinding(binding.sourceObject, director.transform);
            }
        }

        private void BindingParticleAttackTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            ParticleAttackTrack track = (ParticleAttackTrack)binding.sourceObject;
            Role role = action.role;
            BattlePlayer player = role.player;

            if (player)
            {
                if (track.segment < action.targets.Count)
                {
                    List<uint> targets = action.targets[track.segment];
                    List<BattleActorNode> dest = new List<BattleActorNode>();
                    foreach (uint target in targets)
                    {
#if DEBUG_ACTION
                        Debug.LogError("caster " + action.caster + " target dest:" + target);
#endif
                        dest.Add(player.GetRole(target).node);
                    }

                    track.source = action.role.node;
                    track.dest = dest;
                }
                else
                {
                    track.source = action.role.node;
                    track.dest = new List<BattleActorNode>();
                }
                track.rootSources.Clear();
                foreach (uint pos in action.rootSourceIds)
                {
                    track.rootSources.Add(player.GetRole(pos).node);
                }
                
                bool bValid = action.IsTrackValid(track.segment);
                track.muted = !bValid;
                //track.SetValid(bValid);
                track.player = player;
                if (player.hub)
                {
                    BattleOutsideSkillManager manager = player.hub.outsideManager;
                    if (manager != null)
                        track.manager = manager;
                }
            }

            Transform roleTrans = role.GetRoleTransform();
            if(roleTrans)
            {
                director.SetGenericBinding(binding.sourceObject, roleTrans);
            }
            else
            {
                Debug.LogError("BindingParticleAttackTrack can not find role's root transform");
                director.SetGenericBinding(binding.sourceObject, director.transform);
            }
        }

        private void BindingMoveAttackTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            MoveAttackTrack track = (MoveAttackTrack)binding.sourceObject;
            Role role = action.role;
            BattlePlayer player = role.player;

            if (player)
            {
                if (track.segment < action.targets.Count)
                {
                    List<uint> targets = action.targets[track.segment];
                    List<BattleActorNode> dest = new List<BattleActorNode>();
                    List<int> targetPos = new List<int>();
                    Role targetRole;
                    foreach (uint target in targets)
                    {
#if DEBUG_ACTION
                        Debug.LogError("caster " + action.caster + " target dest:" + target);
#endif
                        targetRole = player.GetRole(target);
                        dest.Add(targetRole.node);
                        targetPos.Add(targetRole.position);
                    }
                    track.source = action.role.node;
                    track.dest = dest;
                    track.targetPosSet = targetPos;
                    track.cineControl = player.cinemachineController;
                }
                else
                {
                    track.source = action.role.node;
                    track.dest = null;
                    track.targetPosSet = null;
                    track.cineControl = null;
                }
                if (action.role.node.card != null)
                    track.animator = action.role.node.card.animator;
            }

            Transform roleTrans = role.GetRoleTransform();
            if (roleTrans)
            {
                director.SetGenericBinding(binding.sourceObject, roleTrans);
            }
            else
            {
                Debug.LogError("BindingMoveAttackTrack can not find role's root transform");
                director.SetGenericBinding(binding.sourceObject, director.transform);
            }
        }

        private void BindingShrinkTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            ShrinkTrack track = (ShrinkTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingFlashTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            FlashTrack track = (FlashTrack)binding.sourceObject;
            Role role = action.role;
            BattlePlayer player = role.player;

            if (player && track.segment < action.targets.Count)
            {
                List<uint> targets = action.targets[track.segment];
                List<BattleActorNode> nodes = new List<BattleActorNode>();

                switch (track.bindingStamp)
                {
                    default:
                    case TrackBindingStamp.Binding_Default:
                    case TrackBindingStamp.Binding_DestArea:
                    case TrackBindingStamp.Binding_Dest:
                        foreach (uint target in targets)
                        {
                            nodes.Add(player.GetRole(target).node);
                        }
                        break;
                    case TrackBindingStamp.Binding_SrcArea:
                    case TrackBindingStamp.Binding_Src:
                        nodes.Add(role.node);
                        break;
                }

                foreach (TimelineClip c in track.GetClips())
                {
                    FlashClip clip = c.asset as FlashClip;
                    clip.nodes = nodes;
                }
            }
            
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingGraynessTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            GraynessTrack track = (GraynessTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingJumpoutTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            JumpoutTrack track = (JumpoutTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingCardDissolveTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            CardDissolveTrack track = (CardDissolveTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingCameraShakeTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            CameraShakeTrack track = (CameraShakeTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.player.hub.GetComponent<Animator>());
        }

        private void BindingTransformAnimationTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            TransformAnimationTrack track = (TransformAnimationTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        private void BindingHudTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            HudTrack track = (HudTrack)binding.sourceObject;
            director.SetGenericBinding(binding.sourceObject, action.role.node);
        }

        //private void BindingSpriteSelfLightTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        //{
        //    SpriteSelfLightTrack track = (SpriteSelfLightTrack)binding.sourceObject;
        //    BattleActorNode node = action.role.node;

        //    if (track.segment < action.targets.Count)
        //    {
        //        List<uint> targets = action.targets[track.segment];
        //        BattlePlayer player = action.player;

        //        switch (track.bindingStamp)
        //        {
        //            case TrackBindingStamp.Binding_Default:
        //            case TrackBindingStamp.Binding_Src:
        //                break;
        //            case TrackBindingStamp.Binding_Dest:
        //                node = player.GetRole(targets[0]).node;
        //                break;
        //        }
        //    }
        //    director.SetGenericBinding(binding.sourceObject, node);
        //}

        private void BindingChinemachine(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            //CinemachineTrack track = binding.sourceObject as CinemachineTrack;
            Role role = action.role;
            BattlePlayer player = role.player;
            if (player && player.hub)
            {
                CinemachineBrain beain = player.hub.battleCamera.GetComponent<CinemachineBrain>();
                if(beain != null)
                    director.SetGenericBinding(binding.sourceObject, beain);
            }
        }

        private void BindingOutsideSkillTrack(PlayableDirector director, PlayableBinding binding, EpisodeAction action)
        {
            OutsideSkillTrack track = binding.sourceObject as OutsideSkillTrack;
            Role role = action.role;
            BattlePlayer player = role.player;
            if (player && player.hub)
            {
                BattleOutsideSkillManager manager = player.hub.outsideManager;
                if (manager != null)
                    director.SetGenericBinding(binding.sourceObject, manager);
                track.player = player;
            }
        }
    }
}