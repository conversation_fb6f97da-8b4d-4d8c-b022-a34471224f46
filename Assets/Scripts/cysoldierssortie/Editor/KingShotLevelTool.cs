using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.Experimental.SceneManagement;
using cysoldierssortie.PathTool;
using DebuggingEssentials;

namespace cysoldierssortie.Editor
{
    public class KingShotLevelTool : EditorWindow
    {
        private const string LEVEL_PREFAB_PATH = "Assets/cysoldierssortie/KingShot/Editor/Level";
        private const string PATH_PREFAB_PATH = "Assets/cysoldierssortie/KingShot/Prefab/Path";
        
        private GameObject selectedLevelPrefab;
        private string newLevelName = "";
        private string oldPrefix = "102_";
        private string newPrefix = "203_";
        private Vector2 scrollPosition;

        // 节点类型配置
        private bool replaceBuildings = true;
        private bool replaceEnemyBirth = true;
        private bool replaceEnemyPath = true;
        private bool replaceSoldierBirth = true;

        // 新功能：批量复制EnemyBirth节点
        private GameObject selectedEnemyBirthPrefab;
        private Transform selectedEnemyBirthNode;
        private int copyCount = 10;
        private int timeInterval = 1;
        
        [MenuItem("Tools/KingShot关卡工具")]
        public static void ShowWindow()
        {
            GetWindow<KingShotLevelTool>("KingShot关卡工具");
        }
        
        private void OnGUI()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // 标题和帮助按钮
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("KingShot关卡配置工具", EditorStyles.boldLabel);
            if (GUILayout.Button("帮助", GUILayout.Width(50)))
            {
                ShowHelpDialog();
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();

            // 功能0：一键刷新关卡json
            DrawRefreshAllLevelsSection();
            EditorGUILayout.Space();

            // 功能1-3：关卡复制和修改
            DrawLevelCopySection();
            EditorGUILayout.Space();

            // 新功能：批量复制EnemyBirth节点
            DrawEnemyBirthCopySection();
            EditorGUILayout.Space();

            // 新功能：批量BAKE路径
            DrawBatchBakePathSection();

            EditorGUILayout.EndScrollView();
        }

        private void ShowHelpDialog()
        {
            string helpText = "KingShot关卡工具使用说明:\n\n" +
                              "【一键刷新关卡JSON】\n" +
                              "• 批量刷新所有关卡的JSON数据\n" +
                              "• 适用于修改解析方法后重新生成数据\n\n" +
                              "【关卡复制工具】\n" +
                              "• 选择源关卡prefab作为模板\n" +
                              "• 输入新关卡名称\n" +
                              "• 配置节点前缀替换规则\n" +
                              "• 自动复制路径预制体\n\n" +
                              "【批量复制EnemyBirth节点】\n" +
                              "• 选择关卡中的EnemyBirth子节点\n" +
                              "• 设置复制数量和时间间隔（不能为0秒）\n" +
                              "• 自动递增时间间隔生成新节点\n" +
                              "• 节点格式：怪物ID_Timer_间隔时间_路线\n\n" +
                              "【批量BAKE路径工具】\n" +
                              "• 遍历所有关卡的EnemyPath节点\n" +
                              "• 对包含PathScript组件的路径执行BAKE操作\n" +
                              "• 提供路径统计和扫描功能\n\n" +
                              "【支持的节点类型】\n" +
                              "• Buildings - 建筑节点\n" +
                              "• EnemyBirth - 敌人出生节点\n" +
                              "• EnemyPath - 敌人路径节点（特殊处理）\n" +
                              "• SoldierBirth - 士兵出生节点\n\n" +
                              "详细说明请查看README文档。";

            EditorUtility.DisplayDialog("使用帮助", helpText, "确定");
        }
        
        private void DrawRefreshAllLevelsSection()
        {
            EditorGUILayout.LabelField("0. 一键刷新关卡JSON", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("刷新所有关卡的JSON数据，当修改了EditorLevelUtils.cs中的解析方法后使用", MessageType.Info);
            
            if (GUILayout.Button("刷新所有关卡JSON", GUILayout.Height(30)))
            {
                RefreshAllLevelJsons();
            }
        }
        
        private void DrawLevelCopySection()
        {
            EditorGUILayout.LabelField("1. 关卡复制工具", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("选择一个关卡prefab作为模板，复制并修改节点前缀来创建新关卡", MessageType.Info);

            // 选择源关卡
            EditorGUILayout.LabelField("选择源关卡Prefab:");
            selectedLevelPrefab = (GameObject)EditorGUILayout.ObjectField(selectedLevelPrefab, typeof(GameObject), false);

            if (selectedLevelPrefab != null)
            {
                string assetPath = AssetDatabase.GetAssetPath(selectedLevelPrefab);
                if (!assetPath.StartsWith(LEVEL_PREFAB_PATH))
                {
                    EditorGUILayout.HelpBox("请选择正确的关卡Prefab（位于" + LEVEL_PREFAB_PATH + "目录下）", MessageType.Warning);
                    selectedLevelPrefab = null;
                }
                else
                {
                    EditorGUILayout.HelpBox($"已选择关卡: {selectedLevelPrefab.name}", MessageType.Info);
                }
            }

            EditorGUILayout.Space();
            
            // 新关卡名称
            EditorGUILayout.LabelField("新关卡名称:");
            newLevelName = EditorGUILayout.TextField(newLevelName);
            
            EditorGUILayout.Space();
            
            // 前缀替换配置
            EditorGUILayout.LabelField("2. 节点前缀替换配置", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("将节点名称中的前缀进行替换:");
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("原前缀:", GUILayout.Width(60));
            oldPrefix = EditorGUILayout.TextField(oldPrefix);
            EditorGUILayout.LabelField("→", GUILayout.Width(20));
            EditorGUILayout.LabelField("新前缀:", GUILayout.Width(60));
            newPrefix = EditorGUILayout.TextField(newPrefix);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 节点类型选择
            EditorGUILayout.LabelField("选择要处理的节点类型:");
            replaceBuildings = EditorGUILayout.Toggle("Buildings节点", replaceBuildings);
            replaceEnemyBirth = EditorGUILayout.Toggle("EnemyBirth节点", replaceEnemyBirth);
            replaceEnemyPath = EditorGUILayout.Toggle("EnemyPath节点", replaceEnemyPath);
            replaceSoldierBirth = EditorGUILayout.Toggle("SoldierBirth节点", replaceSoldierBirth);
            
            EditorGUILayout.Space();
            
            // 执行复制按钮
            GUI.enabled = selectedLevelPrefab != null && !string.IsNullOrEmpty(newLevelName);
            if (GUILayout.Button("复制关卡", GUILayout.Height(40)))
            {
                CopyLevel();
            }
            GUI.enabled = true;

            if (selectedLevelPrefab != null && string.IsNullOrEmpty(newLevelName))
            {
                EditorGUILayout.HelpBox("请输入新关卡名称", MessageType.Warning);
            }

            EditorGUILayout.Space();

            // 显示现有关卡列表
            if (GUILayout.Button("显示现有关卡列表"))
            {
                ShowExistingLevels();
            }
        }

        private void ShowExistingLevels()
        {
            if (!Directory.Exists(LEVEL_PREFAB_PATH))
            {
                EditorUtility.DisplayDialog("错误", "关卡目录不存在: " + LEVEL_PREFAB_PATH, "确定");
                return;
            }

            string[] prefabFiles = Directory.GetFiles(LEVEL_PREFAB_PATH, "*.prefab", SearchOption.TopDirectoryOnly);
            string levelList = "现有关卡列表:\n\n";

            foreach (string file in prefabFiles)
            {
                string fileName = Path.GetFileNameWithoutExtension(file);
                levelList += "• " + fileName + "\n";
            }

            if (prefabFiles.Length == 0)
            {
                levelList += "未找到任何关卡prefab";
            }

            EditorUtility.DisplayDialog("关卡列表", levelList, "确定");
        }

        private void DrawEnemyBirthCopySection()
        {
            EditorGUILayout.LabelField("4. 批量复制EnemyBirth节点", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("选择关卡中的EnemyBirth子节点，批量复制并自动递增时间间隔", MessageType.Info);

            // 选择关卡prefab
            EditorGUILayout.LabelField("选择关卡Prefab:");
            selectedEnemyBirthPrefab = (GameObject)EditorGUILayout.ObjectField(selectedEnemyBirthPrefab, typeof(GameObject), false);

            if (selectedEnemyBirthPrefab != null)
            {
                string assetPath = AssetDatabase.GetAssetPath(selectedEnemyBirthPrefab);
                if (!assetPath.StartsWith(LEVEL_PREFAB_PATH))
                {
                    EditorGUILayout.HelpBox("请选择正确的关卡Prefab", MessageType.Warning);
                    selectedEnemyBirthPrefab = null;
                    selectedEnemyBirthNode = null;
                }
                else
                {
                    // 显示EnemyBirth节点选择
                    DrawEnemyBirthNodeSelection();
                }
            }

            if (selectedEnemyBirthNode != null)
            {
                EditorGUILayout.Space();

                // 复制参数设置
                EditorGUILayout.LabelField("复制参数设置:");
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("复制数量:", GUILayout.Width(80));
                copyCount = EditorGUILayout.IntField(copyCount, GUILayout.Width(60));
                copyCount = Mathf.Max(1, copyCount); // 确保至少为1
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("时间间隔:", GUILayout.Width(80));
                timeInterval = EditorGUILayout.IntField(timeInterval, GUILayout.Width(60));
                timeInterval = Mathf.Max(1, timeInterval); // 确保至少为1，不能为0
                EditorGUILayout.LabelField("秒", GUILayout.Width(20));
                EditorGUILayout.EndHorizontal();

                if (timeInterval == 0)
                {
                    EditorGUILayout.HelpBox("时间间隔不能为0秒", MessageType.Warning);
                }

                EditorGUILayout.Space();

                // 预览信息
                string nodeName = selectedEnemyBirthNode.name;
                string previewText = GenerateCopyPreview(nodeName, copyCount, timeInterval);
                EditorGUILayout.LabelField("预览结果:");
                EditorGUILayout.TextArea(previewText, GUILayout.Height(60));

                EditorGUILayout.Space();

                // 执行复制按钮
                if (GUILayout.Button("批量复制节点", GUILayout.Height(30)))
                {
                    CopyEnemyBirthNodes();
                }
            }
        }

        private void DrawEnemyBirthNodeSelection()
        {
            // 获取EnemyBirth节点
            Transform enemyBirthNode = selectedEnemyBirthPrefab.transform.Find("EnemyBirth");
            if (enemyBirthNode == null)
            {
                EditorGUILayout.HelpBox("该关卡prefab中未找到EnemyBirth节点", MessageType.Warning);
                return;
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("选择EnemyBirth子节点:");

            // 获取所有子节点
            List<Transform> childNodes = new List<Transform>();
            for (int i = 0; i < enemyBirthNode.childCount; i++)
            {
                childNodes.Add(enemyBirthNode.GetChild(i));
            }

            if (childNodes.Count == 0)
            {
                EditorGUILayout.HelpBox("EnemyBirth节点下没有子节点", MessageType.Info);
                return;
            }

            // 创建节点名称数组用于下拉选择
            string[] nodeNames = childNodes.Select(t => t.name).ToArray();
            int currentIndex = selectedEnemyBirthNode != null ?
                Array.IndexOf(nodeNames, selectedEnemyBirthNode.name) : -1;

            if (currentIndex == -1) currentIndex = 0;

            int newIndex = EditorGUILayout.Popup("子节点:", currentIndex, nodeNames);

            if (newIndex >= 0 && newIndex < childNodes.Count)
            {
                selectedEnemyBirthNode = childNodes[newIndex];

                // 显示选中节点的信息
                EditorGUILayout.HelpBox($"已选择节点: {selectedEnemyBirthNode.name}", MessageType.Info);

                // 解析节点名称格式
                ParseNodeNameInfo(selectedEnemyBirthNode.name);
            }
        }

        private void ParseNodeNameInfo(string nodeName)
        {
            // 解析节点名称格式：怪物ID_Timer_间隔时间_路线
            string[] parts = nodeName.Split('_');
            if (parts.Length >= 4)
            {
                string monsterID = parts[0];
                string timerPart = parts[1];
                string intervalPart = parts[2];
                string routePart = parts[3];

                EditorGUILayout.LabelField("节点信息解析:");
                EditorGUILayout.LabelField($"  怪物ID: {monsterID}");
                EditorGUILayout.LabelField($"  Timer: {timerPart}");
                EditorGUILayout.LabelField($"  当前间隔: {intervalPart}");
                EditorGUILayout.LabelField($"  路线: {routePart}");
            }
            else
            {
                EditorGUILayout.HelpBox("节点名称格式不符合预期（应为：怪物ID_Timer_间隔时间_路线）", MessageType.Warning);
            }
        }

        private string GenerateCopyPreview(string originalName, int count, int interval)
        {
            string[] parts = originalName.Split('_');
            if (parts.Length < 4)
            {
                return "节点名称格式不正确，无法生成预览";
            }

            string monsterID = parts[0];
            string timerPart = parts[1];
            string currentInterval = parts[2];
            string routePart = parts[3];

            // 获取当前间隔时间作为起始值
            int startInterval = 0;
            if (int.TryParse(currentInterval, out startInterval))
            {
                // 如果解析成功，从当前间隔开始
            }
            else
            {
                // 如果解析失败，从0开始
                startInterval = 0;
            }

            List<string> previewNames = new List<string>();
            for (int i = 0; i < count; i++)
            {
                int newInterval = startInterval + (i * interval);
                string newName = $"{monsterID}_{timerPart}_{newInterval}_{routePart}";
                previewNames.Add(newName);
            }

            return string.Join("\n", previewNames);
        }

        private void CopyEnemyBirthNodes()
        {
            if (selectedEnemyBirthNode == null || selectedEnemyBirthPrefab == null)
            {
                EditorUtility.DisplayDialog("错误", "请先选择关卡和节点", "确定");
                return;
            }

            try
            {
                EditorUtility.DisplayProgressBar("批量复制节点", "开始复制...", 0f);

                // 使用PrefabUtility进入prefab编辑模式
                string assetPath = AssetDatabase.GetAssetPath(selectedEnemyBirthPrefab);
                GameObject prefabContents = PrefabUtility.LoadPrefabContents(assetPath);

                try
                {
                    Transform enemyBirthNode = prefabContents.transform.Find("EnemyBirth");
                    if (enemyBirthNode == null)
                    {
                        EditorUtility.DisplayDialog("错误", "未找到EnemyBirth节点", "确定");
                        return;
                    }

                    // 找到对应的源节点
                    Transform sourceNode = null;
                    for (int i = 0; i < enemyBirthNode.childCount; i++)
                    {
                        if (enemyBirthNode.GetChild(i).name == selectedEnemyBirthNode.name)
                        {
                            sourceNode = enemyBirthNode.GetChild(i);
                            break;
                        }
                    }

                    if (sourceNode == null)
                    {
                        EditorUtility.DisplayDialog("错误", "未找到源节点", "确定");
                        return;
                    }

                    // 解析原始节点名称
                    string[] parts = sourceNode.name.Split('_');
                    if (parts.Length < 4)
                    {
                        EditorUtility.DisplayDialog("错误", "节点名称格式不正确", "确定");
                        return;
                    }

                    string monsterID = parts[0];
                    string timerPart = parts[1];
                    string currentInterval = parts[2];
                    string routePart = parts[3];

                    // 获取起始间隔时间
                    int startInterval = 0;
                    int.TryParse(currentInterval, out startInterval);

                    // 获取源节点在父节点中的索引，新节点将插入到其后面
                    int insertIndex = sourceNode.GetSiblingIndex() + 1;

                    // 批量复制节点
                    for (int i = 0; i < copyCount; i++)
                    {
                        EditorUtility.DisplayProgressBar("批量复制节点", $"复制节点 {i + 1}/{copyCount}", (float)i / copyCount);

                        // 计算新的间隔时间
                        int newInterval = startInterval + ((i + 1) * timeInterval);
                        string newName = $"{monsterID}_{timerPart}_{newInterval}_{routePart}";

                        // 复制节点
                        GameObject newNode = GameObject.Instantiate(sourceNode.gameObject);
                        newNode.name = newName;
                        newNode.transform.SetParent(enemyBirthNode);
                        newNode.transform.SetSiblingIndex(insertIndex + i);

                        Debug.Log($"✓ 创建新节点: {newName}");
                    }

                    // 保存修改
                    PrefabUtility.SaveAsPrefabAsset(prefabContents, assetPath);

                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("成功", $"成功复制了 {copyCount} 个节点", "确定");

                    // 刷新资源
                    AssetDatabase.Refresh();

                    // 刷新子节点列表 - 重新加载prefab以更新UI显示
                    selectedEnemyBirthPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);

                    // 重置选中的节点，让用户重新选择
                    selectedEnemyBirthNode = null;
                }
                finally
                {
                    PrefabUtility.UnloadPrefabContents(prefabContents);
                }
            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("错误", "复制节点时发生错误: " + e.Message, "确定");
                Debug.LogError("批量复制节点错误: " + e);
            }
        }

        private void DrawBatchBakePathSection()
        {
            EditorGUILayout.LabelField("5. 批量BAKE路径工具", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("遍历所有关卡的EnemyPath节点，对包含PathScript组件的路径执行BAKE操作", MessageType.Info);

            EditorGUILayout.Space();

            if (GUILayout.Button("批量BAKE所有关卡路径", GUILayout.Height(40)))
            {
                BatchBakeAllLevelPaths();
            }

            EditorGUILayout.Space();

            if (GUILayout.Button("扫描并显示路径统计", GUILayout.Height(30)))
            {
                ScanAndShowPathStatistics();
            }
        }

        private void BatchBakeAllLevelPaths()
        {
            if (!Directory.Exists(LEVEL_PREFAB_PATH))
            {
                EditorUtility.DisplayDialog("错误", "关卡目录不存在: " + LEVEL_PREFAB_PATH, "确定");
                return;
            }

            string[] prefabFiles = Directory.GetFiles(LEVEL_PREFAB_PATH, "*.prefab", SearchOption.TopDirectoryOnly);
            int totalLevels = prefabFiles.Length;
            int processedLevels = 0;
            int totalPathsBaked = 0;

            try
            {
                for (int i = 0; i < prefabFiles.Length; i++)
                {
                    string prefabPath = prefabFiles[i].Replace('\\', '/');
                    string fileName = Path.GetFileNameWithoutExtension(prefabPath);

                    EditorUtility.DisplayProgressBar("批量BAKE路径", $"处理关卡: {fileName}", (float)i / totalLevels);

                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        int pathsBaked = BakeLevelPaths(prefab, fileName);
                        totalPathsBaked += pathsBaked;
                        processedLevels++;
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("完成",
                $"批量BAKE完成！\n" +
                $"处理关卡: {processedLevels}/{totalLevels}\n" +
                $"BAKE路径总数: {totalPathsBaked}", "确定");
        }

        private int BakeLevelPaths(GameObject levelPrefab, string levelName)
        {
            int bakedCount = 0;

            try
            {
                // 查找EnemyPath节点
                Transform enemyPathNode = levelPrefab.transform.Find("EnemyPath");
                if (enemyPathNode == null)
                {
                    Debug.Log($"关卡 {levelName} 没有EnemyPath节点");
                    return 0;
                }

                // 遍历EnemyPath的所有子节点
                for (int i = 0; i < enemyPathNode.childCount; i++)
                {
                    Transform child = enemyPathNode.GetChild(i);
                    PathScript pathScript = child.GetComponent<PathScript>();

                    if (pathScript != null && pathScript.bakedPathResource != null)
                    {
                        // 执行BAKE操作
                        pathScript.bakedPathResource.Bake(pathScript);
                        bakedCount++;

                        Debug.Log($"✓ BAKE路径: {levelName} -> {child.name}");

                        // 标记为脏数据
                        EditorUtility.SetDirty(pathScript.bakedPathResource);
                        EditorUtility.SetDirty(pathScript);
                    }
                    else if (pathScript != null && pathScript.bakedPathResource == null)
                    {
                        Debug.LogWarning($"⚠ 路径节点 {levelName}->{child.name} 的PathScript没有bakedPathResource引用");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"BAKE关卡 {levelName} 路径时发生错误: {e.Message}");
            }

            return bakedCount;
        }

        private void ScanAndShowPathStatistics()
        {
            if (!Directory.Exists(LEVEL_PREFAB_PATH))
            {
                EditorUtility.DisplayDialog("错误", "关卡目录不存在: " + LEVEL_PREFAB_PATH, "确定");
                return;
            }

            string[] prefabFiles = Directory.GetFiles(LEVEL_PREFAB_PATH, "*.prefab", SearchOption.TopDirectoryOnly);
            int totalLevels = prefabFiles.Length;
            int levelsWithPaths = 0;
            int totalPaths = 0;
            int pathsWithBakedResource = 0;
            int pathsWithoutBakedResource = 0;

            List<string> statisticsDetails = new List<string>();

            try
            {
                for (int i = 0; i < prefabFiles.Length; i++)
                {
                    string prefabPath = prefabFiles[i].Replace('\\', '/');
                    string fileName = Path.GetFileNameWithoutExtension(prefabPath);

                    EditorUtility.DisplayProgressBar("扫描路径统计", $"扫描关卡: {fileName}", (float)i / totalLevels);

                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        var stats = ScanLevelPathStatistics(prefab, fileName);
                        if (stats.pathCount > 0)
                        {
                            levelsWithPaths++;
                            totalPaths += stats.pathCount;
                            pathsWithBakedResource += stats.pathsWithBaked;
                            pathsWithoutBakedResource += stats.pathsWithoutBaked;

                            statisticsDetails.Add($"{fileName}: {stats.pathCount}个路径 ({stats.pathsWithBaked}有引用, {stats.pathsWithoutBaked}无引用)");
                        }
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            string summary = $"路径统计报告:\n\n" +
                           $"总关卡数: {totalLevels}\n" +
                           $"包含路径的关卡: {levelsWithPaths}\n" +
                           $"路径总数: {totalPaths}\n" +
                           $"有BakedPath引用: {pathsWithBakedResource}\n" +
                           $"无BakedPath引用: {pathsWithoutBakedResource}\n\n" +
                           "详细信息:\n" + string.Join("\n", statisticsDetails);

            EditorUtility.DisplayDialog("路径统计", summary, "确定");
        }

        private (int pathCount, int pathsWithBaked, int pathsWithoutBaked) ScanLevelPathStatistics(GameObject levelPrefab, string levelName)
        {
            int pathCount = 0;
            int pathsWithBaked = 0;
            int pathsWithoutBaked = 0;

            Transform enemyPathNode = levelPrefab.transform.Find("EnemyPath");
            if (enemyPathNode != null)
            {
                for (int i = 0; i < enemyPathNode.childCount; i++)
                {
                    Transform child = enemyPathNode.GetChild(i);
                    PathScript pathScript = child.GetComponent<PathScript>();

                    if (pathScript != null)
                    {
                        pathCount++;
                        if (pathScript.bakedPathResource != null)
                        {
                            pathsWithBaked++;
                        }
                        else
                        {
                            pathsWithoutBaked++;
                        }
                    }
                }
            }

            return (pathCount, pathsWithBaked, pathsWithoutBaked);
        }

        private bool ValidateInputs()
        {
            if (selectedLevelPrefab == null)
            {
                EditorUtility.DisplayDialog("错误", "请选择源关卡prefab", "确定");
                return false;
            }

            if (string.IsNullOrEmpty(newLevelName))
            {
                EditorUtility.DisplayDialog("错误", "请输入新关卡名称", "确定");
                return false;
            }

            if (string.IsNullOrEmpty(oldPrefix) || string.IsNullOrEmpty(newPrefix))
            {
                EditorUtility.DisplayDialog("错误", "请输入有效的前缀替换规则", "确定");
                return false;
            }

            if (!replaceBuildings && !replaceEnemyBirth && !replaceEnemyPath && !replaceSoldierBirth)
            {
                EditorUtility.DisplayDialog("错误", "请至少选择一个要处理的节点类型", "确定");
                return false;
            }

            // 检查新关卡名称是否包含非法字符
            if (newLevelName.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
            {
                EditorUtility.DisplayDialog("错误", "关卡名称包含非法字符", "确定");
                return false;
            }

            return true;
        }
        
        private void RefreshAllLevelJsons()
        {
            if (!Directory.Exists(LEVEL_PREFAB_PATH))
            {
                EditorUtility.DisplayDialog("错误", "关卡目录不存在: " + LEVEL_PREFAB_PATH, "确定");
                return;
            }
            
            string[] prefabFiles = Directory.GetFiles(LEVEL_PREFAB_PATH, "*.prefab", SearchOption.TopDirectoryOnly);
            int processedCount = 0;
            int totalCount = prefabFiles.Length;
            
            try
            {
                for (int i = 0; i < prefabFiles.Length; i++)
                {
                    string prefabPath = prefabFiles[i].Replace('\\', '/');
                    string fileName = Path.GetFileNameWithoutExtension(prefabPath);
                    
                    EditorUtility.DisplayProgressBar("刷新关卡JSON", $"处理关卡: {fileName}", (float)i / totalCount);
                    Debug.LogWarning("刷新关卡JSON: " + $"处理关卡: {fileName}");
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        // 调用EditorLevelUtils中的方法生成JSON
                        EditorLevelUtils.CreateLevelData_KingShot(prefab,false);
                        processedCount++;
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
            
            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("完成", $"成功刷新了 {processedCount}/{totalCount} 个关卡的JSON数据", "确定");
        }
        
        private void CopyLevel()
        {
            // 验证输入
            if (!ValidateInputs())
            {
                return;
            }

            try
            {
                EditorUtility.DisplayProgressBar("复制关卡", "开始复制...", 0f);

                // 1. 复制关卡prefab
                string sourcePath = AssetDatabase.GetAssetPath(selectedLevelPrefab);
                string targetPath = Path.Combine(LEVEL_PREFAB_PATH, newLevelName + ".prefab").Replace('\\', '/');

                if (AssetDatabase.LoadAssetAtPath<GameObject>(targetPath) != null)
                {
                    if (!EditorUtility.DisplayDialog("文件已存在", $"关卡 {newLevelName} 已存在，是否覆盖？", "覆盖", "取消"))
                    {
                        EditorUtility.ClearProgressBar();
                        return;
                    }
                }

                bool copySuccess = AssetDatabase.CopyAsset(sourcePath, targetPath);
                if (!copySuccess)
                {
                    EditorUtility.ClearProgressBar();
                    EditorUtility.DisplayDialog("错误", "复制关卡prefab失败", "确定");
                    return;
                }

                AssetDatabase.Refresh();
                
                EditorUtility.DisplayProgressBar("复制关卡", "修改关卡内容...", 0.3f);
                
                // 2. 加载并修改新的prefab
                GameObject newPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(targetPath);
                if (newPrefab == null)
                {
                    EditorUtility.DisplayDialog("错误", "无法加载复制的关卡prefab", "确定");
                    return;
                }
                
                // 3. 修改prefab内容
                ModifyPrefabContent(newPrefab, targetPath);
                
                EditorUtility.DisplayProgressBar("复制关卡", "生成JSON数据...", 0.8f);
                
                // 4. 强制更新PathScript引用
                EditorUtility.DisplayProgressBar("复制关卡", "更新路径引用...", 0.9f);
                ForceUpdatePathScriptReferences(newPrefab);

                // 5. 生成新关卡的JSON数据
                EditorLevelUtils.CreateLevelData_KingShot(newPrefab);

                EditorUtility.ClearProgressBar();
                AssetDatabase.Refresh();

                EditorUtility.DisplayDialog("成功", $"关卡 {newLevelName} 复制完成！", "确定");

                // 选中新创建的prefab
                Selection.activeObject = newPrefab;
                EditorGUIUtility.PingObject(newPrefab);
            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("错误", "复制关卡时发生错误: " + e.Message, "确定");
                Debug.LogError("复制关卡错误: " + e);
            }
        }
        
        private void ModifyPrefabContent(GameObject prefab, string prefabPath)
        {
            // 使用PrefabUtility进入prefab编辑模式
            string assetPath = AssetDatabase.GetAssetPath(prefab);
            GameObject prefabContents = PrefabUtility.LoadPrefabContents(assetPath);

            try
            {
                // 修改prefab名称
                prefabContents.name = newLevelName;

                // 处理各个节点
                if (replaceBuildings)
                    ProcessNode(prefabContents, "Buildings");

                if (replaceEnemyBirth)
                    ProcessNode(prefabContents, "EnemyBirth");

                if (replaceSoldierBirth)
                    ProcessNode(prefabContents, "SoldierBirth");

                // 最后处理EnemyPath节点（因为需要复制文件和更新引用）
                if (replaceEnemyPath)
                    ProcessEnemyPathNode(prefabContents);

                // 保存修改
                PrefabUtility.SaveAsPrefabAsset(prefabContents, assetPath);

                Debug.Log($"✓ 成功保存prefab修改: {assetPath}");
            }
            finally
            {
                PrefabUtility.UnloadPrefabContents(prefabContents);
            }
        }
        
        private void ProcessNode(GameObject prefab, string nodeName)
        {
            Transform nodeTransform = prefab.transform.Find(nodeName);
            if (nodeTransform == null) return;
            
            // 遍历所有子节点，替换名称前缀
            for (int i = 0; i < nodeTransform.childCount; i++)
            {
                Transform child = nodeTransform.GetChild(i);
                if (child.name.StartsWith(oldPrefix))
                {
                    child.name = child.name.Replace(oldPrefix, newPrefix);
                }
            }
        }
        
        private void ProcessEnemyPathNode(GameObject prefab)
        {
            Transform pathNode = prefab.transform.Find("EnemyPath");
            if (pathNode == null) return;

            List<PathUpdateInfo> pathsToUpdate = new List<PathUpdateInfo>();

            // 从源关卡名称中提取关卡编号
            string sourceLevelName = selectedLevelPrefab.name;
            string sourceLevelNumber = ExtractLevelNumber(sourceLevelName);
            string targetLevelNumber = ExtractLevelNumber(newLevelName);

            // 遍历EnemyPath节点的子节点
            for (int i = 0; i < pathNode.childCount; i++)
            {
                Transform child = pathNode.GetChild(i);
                string oldName = child.name;

                // 检查是否是路径节点（格式如：108_a, 108_b等）
                if (oldName.StartsWith(sourceLevelNumber + "_"))
                {
                    // 替换关卡编号，保持后缀不变
                    string suffix = oldName.Substring(sourceLevelNumber.Length); // 获取"_a", "_b"等后缀
                    string newName = targetLevelNumber + suffix;
                    child.name = newName;

                    Debug.Log($"重命名路径节点: {oldName} -> {newName}");

                    // 检查PathScript组件
                    PathScript pathScript = child.GetComponent<PathScript>();
                    if (pathScript != null)
                    {
                        pathsToUpdate.Add(new PathUpdateInfo
                        {
                            pathScript = pathScript,
                            oldName = oldName,
                            newName = newName
                        });
                    }
                }
            }

            // 先复制所有路径预制体
            foreach (var pathInfo in pathsToUpdate)
            {
                CopyPathPrefab(pathInfo.oldName, pathInfo.newName);
            }

            // 等待资源刷新后再更新引用
            EditorApplication.delayCall += () =>
            {
                AssetDatabase.Refresh();
                EditorApplication.delayCall += () =>
                {
                    foreach (var pathInfo in pathsToUpdate)
                    {
                        UpdatePathScriptReference(pathInfo.pathScript, pathInfo.oldName, pathInfo.newName);
                    }
                };
            };
        }

        private class PathUpdateInfo
        {
            public PathScript pathScript;
            public string oldName;
            public string newName;
        }

        private string ExtractLevelNumber(string levelName)
        {
            // 从Level108或Level109这样的名称中提取数字部分
            if (levelName.StartsWith("Level"))
            {
                return levelName.Substring(5); // 返回"108"或"109"
            }

            // 如果不是标准格式，尝试提取数字
            string numbers = "";
            foreach (char c in levelName)
            {
                if (char.IsDigit(c))
                {
                    numbers += c;
                }
            }

            return numbers;
        }

        private void ForceUpdatePathScriptReferences(GameObject prefab)
        {
            try
            {
                // 使用PrefabUtility进入prefab编辑模式来强制更新引用
                string assetPath = AssetDatabase.GetAssetPath(prefab);
                GameObject prefabContents = PrefabUtility.LoadPrefabContents(assetPath);

                try
                {
                    Transform pathNode = prefabContents.transform.Find("EnemyPath");
                    if (pathNode != null)
                    {
                        string targetLevelNumber = ExtractLevelNumber(newLevelName);

                        for (int i = 0; i < pathNode.childCount; i++)
                        {
                            Transform child = pathNode.GetChild(i);
                            string nodeName = child.name;

                            // 检查是否是目标格式的节点（如109_a）
                            if (nodeName.StartsWith(targetLevelNumber + "_"))
                            {
                                PathScript pathScript = child.GetComponent<PathScript>();
                                if (pathScript != null)
                                {
                                    string pathPrefabPath = Path.Combine(PATH_PREFAB_PATH, $"bakepath{nodeName}.prefab").Replace('\\', '/');
                                    var pathPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(pathPrefabPath);

                                    if (pathPrefab != null)
                                    {
                                        var bakedPath = pathPrefab.GetComponent<BakedPath>();
                                        if (bakedPath != null)
                                        {
                                            pathScript.bakedPathResource = bakedPath;
                                            EditorUtility.SetDirty(pathScript);
                                            Debug.Log($"✓ 强制更新PathScript引用: {nodeName} -> {pathPrefabPath}");
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 保存修改
                    PrefabUtility.SaveAsPrefabAsset(prefabContents, assetPath);
                }
                finally
                {
                    PrefabUtility.UnloadPrefabContents(prefabContents);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"强制更新PathScript引用失败: {e.Message}");
            }
        }

        private void UpdatePathScriptReference(PathScript pathScript, string oldName, string newName)
        {
            try
            {
                // 构建新的路径预制体路径，格式：bakepath109_a.prefab
                string newPathPrefabPath = Path.Combine(PATH_PREFAB_PATH, $"bakepath{newName}.prefab").Replace('\\', '/');

                Debug.Log($"准备更新PathScript引用: {oldName} -> {newName}");
                Debug.Log($"目标路径预制体: {newPathPrefabPath}");

                // 直接尝试加载新的路径预制体
                var newPathPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(newPathPrefabPath);
                if (newPathPrefab != null)
                {
                    var bakedPath = newPathPrefab.GetComponent<BakedPath>();
                    if (bakedPath != null)
                    {
                        // 清除旧引用
                        pathScript.bakedPathResource = null;

                        // 设置新引用
                        pathScript.bakedPathResource = bakedPath;

                        // 标记为脏数据，确保保存
                        EditorUtility.SetDirty(pathScript);
                        EditorUtility.SetDirty(pathScript.gameObject);

                        Debug.Log($"✓ 成功更新PathScript引用: {oldName} -> {newName}");
                        Debug.Log($"✓ 新的BakedPath引用: {AssetDatabase.GetAssetPath(bakedPath)}");
                    }
                    else
                    {
                        Debug.LogWarning($"⚠ 新路径预制体中未找到BakedPath组件: {newPathPrefabPath}");
                    }
                }
                else
                {
                    Debug.LogWarning($"⚠ 无法加载新路径预制体: {newPathPrefabPath}");

                    // 列出目录中的所有文件，帮助调试
                    if (Directory.Exists(PATH_PREFAB_PATH))
                    {
                        var files = Directory.GetFiles(PATH_PREFAB_PATH, "*.prefab")
                            .Select(f => Path.GetFileName(f))
                            .Where(f => f.Contains(newName))
                            .ToArray();

                        if (files.Length > 0)
                        {
                            Debug.Log($"找到相关文件: {string.Join(", ", files)}");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"更新PathScript引用异常 {oldName} -> {newName}: {e.Message}");
            }
        }
        
        private void CopyPathPrefab(string oldPathName, string newPathName)
        {
            try
            {
                // 构建路径预制体文件名，格式：bakepath108_a.prefab
                string sourcePathFile = Path.Combine(PATH_PREFAB_PATH, $"bakepath{oldPathName}.prefab").Replace('\\', '/');
                string targetPathFile = Path.Combine(PATH_PREFAB_PATH, $"bakepath{newPathName}.prefab").Replace('\\', '/');

                Debug.Log($"尝试复制路径预制体: {sourcePathFile} -> {targetPathFile}");

                if (AssetDatabase.LoadAssetAtPath<GameObject>(sourcePathFile) != null)
                {
                    bool success = AssetDatabase.CopyAsset(sourcePathFile, targetPathFile);
                    if (success)
                    {
                        Debug.Log($"✓ 成功复制路径预制体: bakepath{oldPathName}.prefab -> bakepath{newPathName}.prefab");
                        AssetDatabase.ImportAsset(targetPathFile);
                    }
                    else
                    {
                        Debug.LogError($"✗ 复制路径预制体失败: {sourcePathFile} -> {targetPathFile}");
                    }
                }
                else
                {
                    Debug.LogWarning($"⚠ 源路径预制体不存在: {sourcePathFile}");

                    // 尝试查找可能的文件名变体
                    string[] possibleFiles = Directory.GetFiles(PATH_PREFAB_PATH, $"*{oldPathName}*.prefab", SearchOption.TopDirectoryOnly);
                    if (possibleFiles.Length > 0)
                    {
                        Debug.Log($"找到可能的路径预制体文件: {string.Join(", ", possibleFiles.Select(Path.GetFileName))}");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"复制路径预制体异常 {oldPathName} -> {newPathName}: {e.Message}");
            }
        }
    }
}
