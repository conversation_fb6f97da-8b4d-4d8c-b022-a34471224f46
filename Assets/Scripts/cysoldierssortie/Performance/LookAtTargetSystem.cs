using System.Collections.Generic;
using Unity.Jobs;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using Vegetation;
using System;
using UnityEngine;

namespace cysoldierssortie
{
    public class LookAtTargetSystem : MonoBehaviour
    {
        private static LookAtTargetSystem _instance;
        public static LookAtTargetSystem Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("LookAtTargetSystem");
                    _instance = go.AddComponent<LookAtTargetSystem>();
                }
                return _instance;
            }
        }
        private static int maxSize = 256;
        private List<Transform> _soldierTrans = new List<Transform>(maxSize);
        private List<Transform> _enemyTrans = new List<Transform>(maxSize);
        private List<float> _soldierLockDis = new List<float>(maxSize);
        private List<float> _enemyLockDis = new List<float>(maxSize);

        private Dictionary<Transform, Transform> _soldierTargetDict = new Dictionary<Transform, Transform>();
        private Dictionary<Transform, Transform> _enemyTargetDict = new Dictionary<Transform, Transform>();


        private NativeArray<float3> EnemyPositions;
        private NativeArray<float> EnemyLockDis;
        private NativeArray<float3> SoldierPositions;
        private NativeArray<float> SoldierLockDis;
        private NativeArray<int> NearestEnemyIndices;
        private NativeArray<int> NearestSoldierIndices;
        private NativeArray<bool> EnemyPositionValidFlag;
        private NativeArray<bool> SoldierPositionValidFlag;

        private int _soldierEmptyIndex = 0;
        private List<int> _soldierIndexPool = new List<int>();
        Dictionary<Transform, int> _soliderIndexCacheDic = new Dictionary<Transform, int>();

        private int _enemyEmptyIndex = 0;
        private List<int> _enemyIndexPool = new List<int>();
        Dictionary<Transform, int> _enemyIndexCacheDic = new Dictionary<Transform, int>();

        LookAtTargetJobProxy _lookAtTargetJobProxy;

        private bool _updateJobCommit = false;
        void Awake()
        {
            _instance = this;
            EnemyPositions = new NativeArray<float3>(
                maxSize,
                Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory
            );
            SoldierPositions = new NativeArray<float3>(maxSize,
                Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);

            NearestEnemyIndices = new NativeArray<int>(
                maxSize,
                Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory
            );
            NearestSoldierIndices = new NativeArray<int>(maxSize, Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);
            EnemyPositionValidFlag = new NativeArray<bool>(maxSize, Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);
            SoldierPositionValidFlag = new NativeArray<bool>(maxSize, Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);
           EnemyLockDis = new NativeArray<float>(maxSize, Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);
            SoldierLockDis = new NativeArray<float>(maxSize, Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);

            if (JobHandleManger.Instance==null)
            {
                JobHandleManger.Instance = new JobHandleManger();
            }

            for(int i=0;i< maxSize;i++)
            {
                _soldierTrans.Add(null);
                _enemyTrans.Add(null);
                NearestSoldierIndices[i] = -1;
                NearestEnemyIndices[i] = -1;
                SoldierPositions[i] = Vector3.zero;
                EnemyPositions[i] = Vector3.zero;
                EnemyPositionValidFlag[i] = false;
                SoldierPositionValidFlag[i] = false;
                _soldierLockDis.Add(100);
                _enemyLockDis.Add(100);
            }
            _lookAtTargetJobProxy = new LookAtTargetJobProxy();
        }

        private void CacheSoldierIndex(Transform transform, int index)
        {
            if (_soliderIndexCacheDic.ContainsKey(transform))
            {
                _soliderIndexCacheDic[transform] = index;
            }
            else
            {
                _soliderIndexCacheDic.Add(transform, index);
            }
        }

        private int GetSoldierIndex(Transform transform)
        {
            int index = -1;
            if (_soliderIndexCacheDic.ContainsKey(transform))
            {
                _soliderIndexCacheDic.TryGetValue(transform, out index);
            }
            return index;
        }

        private void CacheSoliderIndexClear(Transform transform)
        {
            if (_soliderIndexCacheDic.ContainsKey(transform))
            {
                _soliderIndexCacheDic[transform] = -1;
            }
        }

        private void CacheEnemyIndex(Transform transform, int index)
        {
            if (_enemyIndexCacheDic.ContainsKey(transform))
            {
                _enemyIndexCacheDic[transform] = index;
            }
            else
            {
                _enemyIndexCacheDic.Add(transform, index);
            }
        }

        private int GetEnemyIndex(Transform transform)
        {
            int index = -1;
            if (_enemyIndexCacheDic.ContainsKey(transform))
            {
                _enemyIndexCacheDic.TryGetValue(transform, out index);
            }
            return index;
        }

        private void CacheEnemyIndexClear(Transform transform)
        {
            if (_enemyIndexCacheDic.ContainsKey(transform))
            {
                _enemyIndexCacheDic[transform] = -1;
            }
        }
        private void OnEnable()
        {
            _lookAtTargetJobProxy.OnCompleteJobAction += OnJobCompleted;
        }

        private void OnDisable()
        {
            _lookAtTargetJobProxy.OnCompleteJobAction -= OnJobCompleted;
        }

        public void RegisterSoldier(Transform soldier, float lockTargetDis)
        {
            int index = 0;
            if (_soldierIndexPool.Count > 0)
            {
                index = _soldierIndexPool[0];
                _soldierIndexPool.RemoveAt(0);
            }
            else
            {
                index = _soldierEmptyIndex;
                _soldierEmptyIndex = _soldierEmptyIndex + 1;
            }
            if (index > (maxSize - 1))
            {
                return;
            }
            _soldierTrans[index] = soldier;
            _soldierLockDis[index] = lockTargetDis;
            CacheSoldierIndex(soldier, index);
        }

        public void UnregisterSoldier(Transform soldier)
        {
            int index = GetSoldierIndex(soldier);
            if (index >= 0)
            {
                CacheSoliderIndexClear(soldier);
                _soldierTrans[index] = null;
                _soldierIndexPool.Add(index);
            }
        }

        public void RegisterEnemy(Transform enemy,float lockTargetDis)
        {
            int index = 0;
            if (_enemyIndexPool.Count > 0)
            {
                index = _enemyIndexPool[0];
                _enemyIndexPool.RemoveAt(0);
            }
            else
            {
                index = _enemyEmptyIndex;
                _enemyEmptyIndex = _enemyEmptyIndex + 1;
            }
            if (index > (maxSize -1))
            {
                return;
            }
            _enemyTrans[index] = enemy;
            _enemyLockDis[index] = lockTargetDis;
            CacheEnemyIndex(enemy, index);
        }

        public void UnregisterEnemey(Transform enemy)
        {
            int index = GetEnemyIndex(enemy); //_enemyTrans.IndexOf(enemy);
            if (index >= 0)
            {
                CacheEnemyIndexClear(enemy);
                _enemyTrans[index] = null;
                _enemyIndexPool.Add(index);
            }
        }


        public void UpdateSoldierPosition()
        {
            for(int i=0;i<_soldierTrans.Count;i++)
            {
                if (_soldierTrans[i]!=null)
                {
                    SoldierPositions[i] =  _soldierTrans[i].position;
                    SoldierPositionValidFlag[i] = true;
                    SoldierLockDis[i] = _soldierLockDis[i];
                }
                else
                {
                    SoldierPositionValidFlag[i] = false;
                }
            }
        }

        public void UpdateEnemyPosition()
        {
            for (int i = 0; i < _enemyTrans.Count; i++)
            {
                if (_enemyTrans[i] != null)
                {
                    EnemyPositions[i] = _enemyTrans[i].position;
                    EnemyPositionValidFlag[i] = true;
                    EnemyLockDis[i] = _enemyLockDis[i];
                }
                else
                {
                    EnemyPositionValidFlag[i] = false;
                }
            }
        }

        void Update()
        {
            if(_updateJobCommit==false)
            {
                _updateJobCommit = true;
                UpdateEnemyPosition();
                UpdateSoldierPosition();
                _lookAtTargetJobProxy.InitNativeData(EnemyPositions, SoldierPositions, NearestEnemyIndices, NearestSoldierIndices,
                SoldierPositionValidFlag, EnemyPositionValidFlag, SoldierLockDis, EnemyLockDis);
                _lookAtTargetJobProxy.CreateJob();
            }
        }

        void OnJobCompleted(NativeArray<int> nearestEnemyIndices, NativeArray<int> nearestSoldierIndices)
        {
            _updateJobCommit = false;
            _soldierTargetDict.Clear();
            for (int i=0;i<nearestEnemyIndices.Length;i++)
            {
                int enemyIndex = nearestEnemyIndices[i];
                if(enemyIndex!=-1)
                {
                    var soldierTrans = _soldierTrans[i];
                    if(soldierTrans!=null)
                    {
                        var enemyTrans = _enemyTrans[enemyIndex];

                        _soldierTargetDict.Add(soldierTrans, enemyTrans);
                    }
                }
            }

            _enemyTargetDict.Clear();
            for (int i = 0; i < nearestSoldierIndices.Length; i++)
            {
                int soldierIndex = nearestSoldierIndices[i];
                if (soldierIndex != -1)
                {
                    var enemyTrans = _enemyTrans[i];
                    if(enemyTrans!=null)
                    {
                        var soldierTrans = _soldierTrans[soldierIndex];
                        _enemyTargetDict.Add(enemyTrans, soldierTrans);
                    }
                }
            }
        }

        public Transform GetSoldierTarget(Transform trans)
        {
            Transform target;
            _soldierTargetDict.TryGetValue(trans, out target);
            return target;
        }

        public Transform GetEnemyTarget(Transform trans)
        {
            Transform target;
            _enemyTargetDict.TryGetValue(trans, out target);
            return target;
        }

        void OnDestroy()
        {
            _lookAtTargetJobProxy.Dispose();
            EnemyPositions.Dispose();
            SoldierPositions.Dispose();
            NearestEnemyIndices.Dispose();
            NearestSoldierIndices.Dispose();
            EnemyPositionValidFlag.Dispose();
            SoldierPositionValidFlag.Dispose();
            SoldierLockDis.Dispose();
            EnemyLockDis.Dispose();
        }

        // Job定义（带Burst编译）
        [BurstCompile]
        struct LookAtTargetJob : IJobParallelFor
        {
            [ReadOnly][NativeDisableParallelForRestriction] public NativeArray<float3> EnemyPositions;
            [ReadOnly][NativeDisableParallelForRestriction] public NativeArray<float3> SoldierPositions;
            [ReadOnly][NativeDisableParallelForRestriction] public NativeArray<bool> _soldierPositionsValidFlag;
            [ReadOnly][NativeDisableParallelForRestriction] public NativeArray<bool> _enemyPositionValidFlag;
            [ReadOnly][NativeDisableParallelForRestriction] public NativeArray<float> SoldierLockDis;
            [ReadOnly][NativeDisableParallelForRestriction] public NativeArray<float> EnemyLockDis;
            public NativeArray<int> NearestEnemyIndices;
            public NativeArray<int> NearestSoldierIndices;

            public void Execute(int index)
            {
                int nearestIndex = -1;
                bool validSoldier = _soldierPositionsValidFlag[index];
                bool validEnemy = false;
                if (validSoldier)
                {
                    float3 soldierPos = SoldierPositions[index];
                    float minSoldierDistance = SoldierLockDis[index];
                    for (int i = 0; i < EnemyPositions.Length; i++)
                    {
                        validEnemy = _enemyPositionValidFlag[i];
                        if(validEnemy)
                        {
                            float3 targetEnemyPos = EnemyPositions[i];
                            float dist = Vector3.Distance(soldierPos,targetEnemyPos);
                            if (dist <= minSoldierDistance)
                            {
                                minSoldierDistance = dist;
                                nearestIndex = i;
                            }
                        }                      
                    }
                    NearestEnemyIndices[index] = nearestIndex;
                }

                nearestIndex = -1;
                validEnemy = _enemyPositionValidFlag[index];

                if(validEnemy)
                {
                    float3 enemyPos = EnemyPositions[index];
                    float minEnemyDistance = EnemyLockDis[index];

                    for (int i = 0; i < SoldierPositions.Length; i++)
                    {
                        validSoldier = _soldierPositionsValidFlag[i];
                        if(validSoldier)
                        {
                            float3 targetSoldierPos = SoldierPositions[i];
                            float dist = Vector3.Distance(enemyPos,targetSoldierPos);
                            if (dist <= minEnemyDistance)
                            {
                                minEnemyDistance = dist;
                                nearestIndex = i;
                            }
                        }
                
                    }
                    NearestSoldierIndices[index] = nearestIndex;
                }
            }
        }

        private class LookAtTargetJobProxy : JobHandleManger.IJobProxy
        {
            private NativeArray<float3> _enemyPositions;
            private NativeArray<float3> _soldierPositions;
            private NativeArray<int> _nearestEnemyIndices;
            private NativeArray<int> _nearestSoldierIndecies;
            private NativeArray<bool> _soldierPositionsValidFlag;
            private NativeArray<bool> _enemyPositionValidFlag;
            private NativeArray<float> _soldierLockDis;
            private NativeArray<float> _enemyLockDis;
            public event Action<NativeArray<int>, NativeArray<int>> OnCompleteJobAction;
            public void InitNativeData(NativeArray<float3> enemyPositions, NativeArray<float3> soldierPositions, 
                NativeArray<int> nearestEnemyIndices, NativeArray<int> nearestSoldierIndeciest,NativeArray<bool> soldierPositionsValidFlag,
                NativeArray<bool> enemyPositionsValidFlag,NativeArray<float> soldierLockDis,NativeArray<float> enemyLockDis)
            {
                _enemyPositions = enemyPositions;
                _soldierPositions = soldierPositions;
                _nearestEnemyIndices = nearestEnemyIndices;
                _nearestSoldierIndecies = nearestSoldierIndeciest;
                _soldierPositionsValidFlag = soldierPositionsValidFlag;
                _enemyPositionValidFlag = enemyPositionsValidFlag;
                _soldierLockDis = soldierLockDis;
                _enemyLockDis = enemyLockDis;
            }

            public void CreateJob()
            {
                JobHandleManger.Instance.ExecuteJob(this);
            }

            public JobHandle CreateJobHandle()
            {
                var job = new LookAtTargetJob
                {
                    EnemyPositions = _enemyPositions,
                    SoldierPositions = _soldierPositions,
                    _soldierPositionsValidFlag = _soldierPositionsValidFlag,
                    _enemyPositionValidFlag = _enemyPositionValidFlag,
                     SoldierLockDis = _soldierLockDis,
                     EnemyLockDis = _enemyLockDis,
                    NearestEnemyIndices = _nearestEnemyIndices, 
                    NearestSoldierIndices = _nearestSoldierIndecies
                };
                JobHandle handle = job.Schedule(_soldierPositions.Length, 64);
                return handle;
            }

            public void Dispose()
            {
                JobHandleManger.Instance.RemoveJob(this);
            }

            public void CompleteJob()
            {
                JobHandleManger.Instance.CompleteJob(this);
            }

            public void OnJobCompleted()
            {
                OnCompleteJobAction.Invoke(_nearestEnemyIndices, _nearestSoldierIndecies);
            }
        }
    }

}
