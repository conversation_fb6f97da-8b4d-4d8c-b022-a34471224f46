using UnityEngine;
// Updated for equal-arc-length baked paths and GPU instancing option

using System.Collections.Generic;
using cysoldierssortie.PathTool;


public class PathVisualizer : MonoBehaviour
{
    public BakedPath path; // 你的路径脚本
    public GameObject arrowPrefab; // 红色三角形预制体
    public float spacing = 0.5f;    // 箭头间距
    public float speed = 2.0f;      // 箭头移动速度

    private List<GameObject> arrows = new List<GameObject>();
    private float[] arrowDistances;
    private float totalDistance;
    private int arrowCount;
    public float hideDistance = 0.2f; // 距离路径起点/终点的隐藏距离
    public bool useInstancing = false; // 使用GPU实例化渲染
    public Vector3 prefabEulerOffset = new Vector3(90f, -90f, 0f); // 适配模型的朝向
    public float lookAheadDistance = 0.1f; // 朝前取样以确定朝向

    // 实例化模式需求
    private Mesh arrowMesh;
    private Material arrowMaterial;
    private readonly List<Matrix4x4> batch = new List<Matrix4x4>(1023);

    private float[] arrowTimes; // 改用时间而不是距离
    void Start()
    {
        if (path == null) path = GetComponent<BakedPath>();
        if (path == null || arrowPrefab == null) return;

        totalDistance = Mathf.Max(0f, path.PathDistance);

        // 根据路径长度和间距计算所需的箭头数量
        arrowCount = Mathf.Max(1, Mathf.FloorToInt(totalDistance / Mathf.Max(0.01f, spacing)));
        arrowTimes = new float[arrowCount]; // 改用时间数组

        // 为实例化准备 Mesh/Material
        if (useInstancing)
        {
            var mf = arrowPrefab.GetComponentInChildren<MeshFilter>();
            var mr = arrowPrefab.GetComponentInChildren<MeshRenderer>();
            if (mf != null && mf.sharedMesh != null && mr != null && mr.sharedMaterial != null)
            {
                arrowMesh = mf.sharedMesh;
                arrowMaterial = mr.sharedMaterial;
            }
            else
            {
                Debug.LogWarning("PathVisualizer: arrowPrefab 缺少 Mesh/Material，已回退到普通GameObject渲染。");
                useInstancing = false;
            }
        }

        // 计算循环周期
        float cycleDuration = (speed > 1e-5f) ? totalDistance / speed : 1f;

        // 初始化箭头时间
        for (int i = 0; i < arrowCount; i++)
        {
            // 初始时间沿周期均匀分布
            arrowTimes[i] = (cycleDuration / arrowCount) * i;

            if (!useInstancing)
            {
                GameObject arrow = Instantiate(arrowPrefab, transform);
                arrows.Add(arrow);
                UpdateArrowPosition(i);
            }
        }
    }


    void Update()
    {
        if (path == null) return;

        float cycleDuration = (speed > 1e-5f) ? totalDistance / speed : 1f;

        // 更新所有箭头位置
        for (int i = 0; i < arrowCount; i++)
        {
            // 基于时间更新
            arrowTimes[i] += Time.deltaTime;

            // 循环时间
            if (arrowTimes[i] > cycleDuration)
                arrowTimes[i] = arrowTimes[i] - cycleDuration;

            if (useInstancing)
            {
                TryAddInstanceMatrix(i, cycleDuration);
            }
            else
            {
                UpdateArrowPosition(i);
            }
        }

        if (useInstancing && batch.Count > 0)
        {
            Graphics.DrawMeshInstanced(arrowMesh, 0, arrowMaterial, batch);
            batch.Clear();
        }
    }

    void TryAddInstanceMatrix(int index, float cycleDuration)
    {
        if (arrowMesh == null || arrowMaterial == null) return;
        float normalizedTime = arrowTimes[index] / cycleDuration;
        float currentDist = normalizedTime * totalDistance;

        bool shouldHide = (currentDist > totalDistance - hideDistance) || (currentDist < hideDistance);
        if (shouldHide) return;

        Vector3 currentPos = path.GetPositionAtDistance(currentDist);
        float nextDist = Mathf.Min(totalDistance, currentDist + lookAheadDistance);
        Vector3 nextPos = path.GetPositionAtDistance(nextDist);
        Vector3 dir = (nextPos - currentPos);
        if (dir.sqrMagnitude < 1e-6f) return;

        Quaternion rot = Quaternion.LookRotation(dir.normalized) * Quaternion.Euler(prefabEulerOffset);
        Matrix4x4 m = Matrix4x4.TRS(currentPos, rot, Vector3.one);
        batch.Add(m);
    }


    void UpdateArrowPosition(int index)
    {
        float cycleDuration = (speed > 1e-5f) ? totalDistance / speed : 1f;
        float normalizedTime = arrowTimes[index] / cycleDuration;
        float currentDist = normalizedTime * totalDistance;

        // 检查是否需要隐藏箭头
        bool shouldHide = (currentDist > totalDistance - hideDistance) || (currentDist < hideDistance);
        arrows[index].SetActive(!shouldHide);

        if (shouldHide) return;

        Vector3 currentPos = path.GetPositionAtDistance(currentDist);
        float nextDist = Mathf.Min(totalDistance, currentDist + lookAheadDistance);
        Vector3 nextPos = path.GetPositionAtDistance(nextDist);

        arrows[index].transform.position = currentPos;

        Vector3 direction = (nextPos - currentPos).normalized;
        if (direction.magnitude > 1e-3f)
        {
            Quaternion rotation = Quaternion.LookRotation(direction);
            Vector3 eulerAngles = rotation.eulerAngles;
            eulerAngles.x = 90.0f;
            eulerAngles.y -= 90.0f;

            arrows[index].transform.rotation = Quaternion.Euler(eulerAngles);
        }
    }

    void OnDrawGizmosSelected()
    {
        if (path == null) return;

        Gizmos.color = Color.red;
        int samples = 50;
        float step = path.PathDistance / samples;

        Vector3 prevPos = path.GetPositionAtDistance(0);
        for (int i = 1; i <= samples; i++)
        {
            Vector3 currentPos = path.GetPositionAtDistance(i * step);
            Gizmos.DrawLine(prevPos, currentPos);
            prevPos = currentPos;
        }
    }
}