using UnityEditor;
using UnityEngine;
using System.Collections.Generic;


namespace cysoldierssortie.PathTool
{
    public class BakedPath : PathBase
    {
        [SerializeField] private AnimationCurve[] position;
        [SerializeField] private AnimationCurve[] orientation;
        [SerializeField] private AnimationCurve[] upVector;
        [<PERSON><PERSON><PERSON><PERSON>ield, HideInInspector] private float distance;
        [SerializeField, HideInInspector] private bool isLoop;

        public override float PathDistance => distance;

        public void Bake(PathScript path)
        {
            if (path == null) return;

            // Prepare source data
            var curved = path.CurvedPositions as IList<Vector3>;
            var orientList = path.Orientations as IList<float>;
            if (curved == null || curved.Count < 2)
                return;

            // Build cumulative length table along curved positions (local space)
            var cumLen = new List<float>(curved.Count);
            float acc = 0f;
            cumLen.Add(0f);
            for (int i = 1; i < curved.Count; i++)
            {
                acc += Vector3.Distance(curved[i - 1], curved[i]);
                cumLen.Add(acc);
            }

            // If closed loop, add final segment back to start
            isLoop = path.IsClosedLoop;
            if (isLoop)
            {
                acc += Vector3.Distance(curved[curved.Count - 1], curved[0]);
            }

            distance = acc; // total arc length in local units

            var sampleStep = Mathf.Max(0.01f, path.Step);

            position = new AnimationCurve[3];
            orientation = new AnimationCurve[4];
            upVector = new AnimationCurve[3];

            // Init curves with appropriate wrap modes depending on loop
            for (int i = 0; i < position.Length; i++)
            {
                position[i] = new AnimationCurve();
                position[i].preWrapMode = isLoop ? WrapMode.Loop : WrapMode.Clamp;
                position[i].postWrapMode = isLoop ? WrapMode.Loop : WrapMode.Clamp;
            }
            for (int i = 0; i < orientation.Length; i++)
            {
                orientation[i] = new AnimationCurve();
                orientation[i].preWrapMode = isLoop ? WrapMode.Loop : WrapMode.Clamp;
                orientation[i].postWrapMode = isLoop ? WrapMode.Loop : WrapMode.Clamp;
            }
            for (int i = 0; i < upVector.Length; i++)
            {
                upVector[i] = new AnimationCurve();
                upVector[i].preWrapMode = isLoop ? WrapMode.Loop : WrapMode.Clamp;
                upVector[i].postWrapMode = isLoop ? WrapMode.Loop : WrapMode.Clamp;
            }

            // Helper: map distance s -> segment index and local t
            int FindSegment(float s, out float t)
            {
                if (s <= 0f)
                {
                    t = 0f;
                    return 0;
                }
                if (s >= distance)
                {
                    t = 1f;
                    return curved.Count - 2; // last segment
                }

                // For loop, wrap s
                if (isLoop)
                {
                    s = Mathf.Repeat(s, distance);
                }

                // Binary search cumLen to find i where cumLen[i] <= s < cumLen[i+1]
                int lo = 0, hi = cumLen.Count - 1;
                while (lo < hi)
                {
                    int mid = (lo + hi) >> 1;
                    if (cumLen[mid] <= s) lo = mid + 1; else hi = mid;
                }
                int idx = Mathf.Max(1, lo) - 1;
                float segStart = cumLen[idx];
                float segEnd = (idx + 1 < cumLen.Count) ? cumLen[idx + 1] : distance;
                float segLen = Mathf.Max(1e-5f, segEnd - segStart);
                t = Mathf.Clamp01((s - segStart) / segLen);
                return idx;
            }

            // Sample along true arc length and write curves
            float sDist = 0f;
            const float tangentEps = 0.03f; // small lookahead for tangent
            while (sDist < distance)
            {
                float tNorm = (distance <= 1e-5f) ? 0f : (sDist / distance);

                // Position at distance sDist
                int segIdx = FindSegment(sDist, out float segT);
                Vector3 p0 = curved[segIdx];
                Vector3 p1 = (segIdx + 1 < curved.Count) ? curved[segIdx + 1] : (isLoop ? curved[0] : curved[segIdx]);
                Vector3 pos = Vector3.Lerp(p0, p1, segT);

                // Orientation (angle in degrees) interpolated along same segment
                float o0 = (orientList != null && orientList.Count > segIdx) ? orientList[segIdx] : 0f;
                float o1 = (orientList != null && orientList.Count > segIdx + 1) ? orientList[segIdx + 1] : (isLoop && orientList != null && orientList.Count > 0 ? orientList[0] : o0);
                float orientDeg = Mathf.Lerp(o0, o1, segT);

                // Tangent/forward direction
                float sAhead = Mathf.Min(distance, sDist + tangentEps);
                int segAhead = FindSegment(sAhead, out float tAhead);
                Vector3 pAhead0 = curved[segAhead];
                Vector3 pAhead1 = (segAhead + 1 < curved.Count) ? curved[segAhead + 1] : (isLoop ? curved[0] : curved[segAhead]);
                Vector3 nextPos = Vector3.Lerp(pAhead0, pAhead1, tAhead);
                Vector3 fwd = (nextPos - pos);
                if (fwd.sqrMagnitude < 1e-8f)
                {
                    // fallback: previous
                    float sBack = Mathf.Max(0f, sDist - tangentEps);
                    int segBack = FindSegment(sBack, out float tBack);
                    Vector3 pBack0 = curved[segBack];
                    Vector3 pBack1 = (segBack + 1 < curved.Count) ? curved[segBack + 1] : (isLoop ? curved[0] : curved[segBack]);
                    Vector3 prevPos = Vector3.Lerp(pBack0, pBack1, tBack);
                    fwd = (pos - prevPos);
                }
                Vector3 dir = fwd.sqrMagnitude > 1e-8f ? fwd.normalized : Vector3.forward;

                // Up vector derived by rotating world up around path tangent by orientDeg
                Vector3 up = Quaternion.AngleAxis(orientDeg, dir) * Vector3.up;

                // Rotation using dir and up
                Quaternion rot = Quaternion.LookRotation(dir, up);

                // Write keys
                position[0].AddKey(tNorm, pos.x);
                position[1].AddKey(tNorm, pos.y);
                position[2].AddKey(tNorm, pos.z);

                orientation[0].AddKey(tNorm, rot.x);
                orientation[1].AddKey(tNorm, rot.y);
                orientation[2].AddKey(tNorm, rot.z);
                orientation[3].AddKey(tNorm, rot.w);

                upVector[0].AddKey(tNorm, up.x);
                upVector[1].AddKey(tNorm, up.y);
                upVector[2].AddKey(tNorm, up.z);

                sDist += sampleStep;
            }

            // Ensure a final key at t=1
            {
                float tNorm = 1f;
                float sDistEnd = distance;
                if (isLoop)
                {
                    tNorm = 1f; // loop
                    sDistEnd = Mathf.Max(0f, distance - 1e-3f);
                }
                int segIdx = FindSegment(sDistEnd, out float segT);
                Vector3 p0 = curved[segIdx];
                Vector3 p1 = (segIdx + 1 < curved.Count) ? curved[segIdx + 1] : (isLoop ? curved[0] : curved[segIdx]);
                Vector3 pos = Vector3.Lerp(p0, p1, segT);

                float o0 = (orientList != null && orientList.Count > segIdx) ? orientList[segIdx] : 0f;
                float o1 = (orientList != null && orientList.Count > segIdx + 1) ? orientList[segIdx + 1] : (isLoop && orientList != null && orientList.Count > 0 ? orientList[0] : o0);
                float orientDeg = Mathf.Lerp(o0, o1, segT);

                // Tangent and up
                float sBack = Mathf.Max(0f, sDistEnd - 0.03f);
                int segBack = FindSegment(sBack, out float tBack);
                Vector3 prevPos = Vector3.Lerp((segBack + 1 < curved.Count) ? curved[segBack] : curved[curved.Count - 1], (segBack + 1 < curved.Count) ? curved[segBack + 1] : (isLoop ? curved[0] : curved[segBack]), tBack);
                Vector3 dir = (pos - prevPos).sqrMagnitude > 1e-8f ? (pos - prevPos).normalized : Vector3.forward;
                Vector3 up = Quaternion.AngleAxis(orientDeg, dir) * Vector3.up;
                Quaternion rot = Quaternion.LookRotation(dir, up);

                position[0].AddKey(tNorm, pos.x);
                position[1].AddKey(tNorm, pos.y);
                position[2].AddKey(tNorm, pos.z);

                orientation[0].AddKey(tNorm, rot.x);
                orientation[1].AddKey(tNorm, rot.y);
                orientation[2].AddKey(tNorm, rot.z);
                orientation[3].AddKey(tNorm, rot.w);

                upVector[0].AddKey(tNorm, up.x);
                upVector[1].AddKey(tNorm, up.y);
                upVector[2].AddKey(tNorm, up.z);
            }

            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        public override Vector3 GetPositionAtDistance(float distance, bool local = false)
        {
            var t = (PathDistance <= 1e-5f) ? 0f : (distance % PathDistance) / PathDistance;
            var pos = new Vector3(position[0].Evaluate(t), position[1].Evaluate(t), position[2].Evaluate(t));
            return local ? pos : transform.TransformPoint(pos);
        }

        public override Quaternion GetRotationAtDistance(float distance)
        {
            var t = (PathDistance <= 1e-5f) ? 0f : (distance % PathDistance) / PathDistance;
            var rot = new Quaternion(orientation[0].Evaluate(t), orientation[1].Evaluate(t), orientation[2].Evaluate(t), orientation[3].Evaluate(t));
            return rot;
        }

        public override Quaternion GetRotationAtDistance(float distance, Vector3 up)
        {
            return GetRotationAtDistance(distance);
        }

        public override Vector3 GetUpVectorAtDistance(float distance)
        {
            var t = (PathDistance <= 1e-5f) ? 0f : (distance % PathDistance) / PathDistance;
            var up = new Vector3(upVector[0].Evaluate(t), upVector[1].Evaluate(t), upVector[2].Evaluate(t));
            return up;
        }

        public override bool IsPathReady()
        {
            var ready = true;

            ready &= position != null && position.Length > 0;
            ready &= orientation != null && orientation.Length > 0;
            ready &= upVector != null && upVector.Length > 0;

            foreach (var item in position)
            {
                ready &= item != null && item.length > 0;
            }

            foreach (var item in orientation)
            {
                ready &= item != null && item.length > 0;
            }

            foreach (var item in upVector)
            {
                ready &= item != null && item.length > 0;
            }

            return ready;
        }

#if UNITY_EDITOR
        private void OnDrawGizmos()
        {
            if (!IsPathReady()) return;

            var step = 1f/position[0].length;

            for (float i = step; i < PathDistance; i += step)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(GetPositionAtDistance(i), GetPositionAtDistance(i - step));
            }
        }
#endif
    }
}