using UnityEngine;
using System.Collections.Generic;
using Unity.Jobs;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using System.Reflection;
using Vegetation;
using UnityEngine.Rendering;
using System;
using CasualGame.lib_ChuagnYi.NeeG;
using CasualGame.lib_ChuagnYi;
using UnityEngine.Jobs;
namespace cysoldierssortie
{
    public class BulletSystem : MonoBehaviour
    {
        // 单例模式（线程安全简化版）
        private static BulletSystem _instance;
        public static BulletSystem Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("BulletSystem");
                    _instance = go.AddComponent<BulletSystem>();
                }
                return _instance;
            }
        }

        // 使用List存储活跃子弹
        private List<Transform> _activeBullets = new List<Transform>(1000);
        private int _emptyIndex = 0;
        private NativeArray<float3> _bulletPositions;
        private NativeArray<float> _bulletSpeed;
        private NativeArray<float3> _bulletforwards;
        private NativeArray<float> _recycleTimes;
        private bool _needUpdatePositions;
        private List<int> _indexPool = new List<int>();
        Dictionary<Transform, int> indexCacheDic = new Dictionary<Transform, int>();
        Dictionary<Transform, LuaMono> luaMonoCacheDic = new Dictionary<Transform, LuaMono>();
        MovementJob m_movementJob;
        void Awake()
        {
            _instance = this;
            // 预分配内存（按最大预期数量）
            int maxBullets = 1000;
            _bulletPositions = new NativeArray<float3>(maxBullets,
                Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);
            _bulletSpeed = new NativeArray<float>(maxBullets,
                Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);
            _bulletforwards = new NativeArray<float3>(
                maxBullets,
                Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory
            );
            _recycleTimes = new NativeArray<float>(maxBullets, Allocator.Persistent,
                NativeArrayOptions.UninitializedMemory);

            for (int i = 0; i < maxBullets; i++)
            {
                _activeBullets.Add(null);
            }

            JobHandleManger.Instance = new JobHandleManger();
            m_movementJob = new MovementJob();
        }

        private void CacheIndex(Transform transform, int index)
        {
            if (indexCacheDic.ContainsKey(transform))
            {
                indexCacheDic[transform] = index;
            }
            else
            {
                indexCacheDic.Add(transform, index);
            }
        }

        private void CacheLuaMono(Transform transform)
        {
            if (luaMonoCacheDic.ContainsKey(transform))
            {
                return;
            }
            var luaMono = transform.GetComponent<LuaMono>();
            if (luaMono)
            {
                luaMonoCacheDic.Add(transform, luaMono);
            }
        }

        private void CacheIndexClear(Transform transform)
        {
            if (indexCacheDic.ContainsKey(transform))
            {
                indexCacheDic[transform] = -1;
            }
        }

        private LuaMono GetLuaMono(Transform transform)
        {
            LuaMono luaMono = null;
            luaMonoCacheDic.TryGetValue(transform, out luaMono);
            return luaMono;
        }

        private int GetIndex(Transform transform)
        {
            int index = -1;
            if (indexCacheDic.ContainsKey(transform))
            {
                indexCacheDic.TryGetValue(transform, out index);
            }
            return index;
        }

        private void OnEnable()
        {
            m_movementJob.OnCompleteJobAction += OnJobCompleted;
        }

        private void OnDisable()
        {
            m_movementJob.OnCompleteJobAction -= OnJobCompleted;
        }

        // 注册子弹的核心方法
        public void RegisterBullet(Transform bullet, float speed, float time)
        {
            int index = 0;
            if (_indexPool.Count > 0)
            {
                index = _indexPool[0];
                _indexPool.RemoveAt(0);
            }
            else
            {
                index = _emptyIndex;
                _emptyIndex = _emptyIndex + 1;
            }

            _activeBullets[index] = bullet;

            _bulletSpeed[index] = speed;
            _bulletPositions[index] = bullet.position;
            _bulletforwards[index] = bullet.transform.forward; // 记录旋转
            _recycleTimes[index] = time;
            CacheIndex(bullet, index);
            CacheLuaMono(bullet);
        }

        public void RegisterBullet(Transform bullet, float speed, Vector3 direction)
        {
            int index = 0;
            if (_indexPool.Count > 0)
            {
                index = _indexPool[0];
                _indexPool.RemoveAt(0);
            }
            else
            {
                index = _emptyIndex;
                _emptyIndex = _emptyIndex + 1;
            }

            _activeBullets[index] = bullet;
            _bulletSpeed[index] = speed;
            _bulletPositions[index] = bullet.position;
            _bulletforwards[index] = direction;
            _recycleTimes[index] = -1;
            CacheIndex(bullet, index);
        }

        // 注销子弹
        public void UnregisterBullet(Transform bullet)
        {
            int index = GetIndex(bullet);
            if (index > -1)
            {
                CacheIndexClear(bullet);
                _activeBullets[index] = null;
                _indexPool.Add(index);
            }
        }

        private void Update()
        {
            m_movementJob.InitNativeData(_bulletPositions, _bulletSpeed, _bulletforwards, _emptyIndex + 1);
            m_movementJob.CreateJob();
        }

        private void LateUpdate()
        {
            JobHandleManger.Instance.OnLateUpdate();
        }

        public bool IsValidPosition(Vector3 position)
        {
            // 检查每个分量是否为NaN或Infinity
            if (float.IsNaN(position.x) || float.IsNaN(position.y) || float.IsNaN(position.z))
            {
                return false;
            }
            if (float.IsInfinity(position.x) || float.IsInfinity(position.y) || float.IsInfinity(position.z))
            {
                return false;
            }
            return true;
        }


        void OnJobCompleted(NativeArray<float3> positions)
        {
            for (int i = 0; i < _emptyIndex + 1; i++)
            {
                if (_activeBullets[i] != null)
                {
                    if (_recycleTimes[i] != -1 && _recycleTimes[i] < Time.time)
                    {
                        var luaMono = GetLuaMono(_activeBullets[i]);
                        if (luaMono != null)
                        {
                            NeeGame.ReturnObject(luaMono, true, false);
                        }
                    }
                    else
                    {
                        if (IsValidPosition(positions[i]))
                        {
                            _activeBullets[i].position = positions[i];
                        }
                    }
                }
            }
        }


        void OnDestroy()
        {
            m_movementJob.OnDipose();
            _bulletPositions.Dispose();
            _bulletSpeed.Dispose();
            _bulletforwards.Dispose();
            _recycleTimes.Dispose();
            indexCacheDic.Clear();
            luaMonoCacheDic.Clear();
        }

        // Job定义（带Burst编译）
        [BurstCompile]
        struct BulletMovementJob : IJobParallelFor
        {
            public NativeArray<float3> Positions;
            public NativeArray<float3> Forwards; // 新增旋转数据
            public NativeArray<float> Speed;
            public float DeltaTime;

            public void Execute(int index)
            {
                Positions[index] += Forwards[index] * Speed[index] * DeltaTime;
            }
        }


        private class MovementJob : JobHandleManger.IJobProxy
        {
            private NativeArray<float3> _bulletPositions;
            private NativeArray<float> _bulletSpeed;
            private NativeArray<float3> _bulletforwards;
            private int _batchCount;
            public event Action<NativeArray<float3>> OnCompleteJobAction;
            public void InitNativeData(NativeArray<float3> positions, NativeArray<float> speed, NativeArray<float3> forwards, int batchCount)
            {
                _bulletPositions = positions;
                _bulletSpeed = speed;
                _bulletforwards = forwards;
                _batchCount = batchCount;
            }

            public void CreateJob()
            {
                JobHandleManger.Instance.ExecuteJob(this);
            }

            public void OnDipose()
            {
                JobHandleManger.Instance.RemoveJob(this);
            }

            public JobHandle CreateJobHandle()
            {
                var job = new BulletMovementJob
                {
                    Positions = _bulletPositions,
                    Forwards = _bulletforwards, // 传递旋转数据
                    Speed = _bulletSpeed, // 从配置读取
                    DeltaTime = Time.deltaTime
                };
                JobHandle handle = job.Schedule(_batchCount + 1, 32);
                handle.Complete();
                return handle;
            }

            public void OnJobCompleted()
            {
                OnCompleteJobAction.Invoke(_bulletPositions);
            }
        }
    }
}
