using CasualGame.lib_ChuagnYi;
using CasualGame.lib_ChuagnYi.NeeG;
using System.Collections;
using UnityEngine;

namespace cysoldierssortie
{
    public class AutoRecyclePoolHepler : MonoBehaviour
    {
        [SerializeField]
        private float m_autoRecycleTime = 1;

        private IEnumerator m_autoRcycleTimer = null;

        private LuaMono _luaComp;

        private void OnEnable()
        {
            if(m_autoRcycleTimer!=null)
            {
                StopCoroutine(m_autoRcycleTimer);
                m_autoRcycleTimer = null;
            }
            m_autoRcycleTimer = DelayRecycle();
            StartCoroutine(m_autoRcycleTimer);
        }

        private void Awake()
        {
            _luaComp = this.gameObject.GetComponent<LuaMono>();
        }

        IEnumerator DelayRecycle()
        {
            yield return new WaitForSeconds(m_autoRecycleTime);
            NeeGame.ReturnObject(_luaComp);
        }
    }
}

