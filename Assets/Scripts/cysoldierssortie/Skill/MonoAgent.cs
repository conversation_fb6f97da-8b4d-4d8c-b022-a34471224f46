using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;
namespace cysoldierssortie
{
    public class MonoAgent : MonoBehaviour
    {
        private static MonoAgent _instance;
        public static MonoAgent Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("MonoAgent");
                    _instance = go.AddComponent<MonoAgent>();
                }
                return _instance;
            }
        }

        [System.NonSerialized]
        public Action<float> UpdateFunc = null;

        public void BindLua(LuaTable t)
        {
            UpdateFunc = t.Get<Action<float>>("Update");
        }

        void Awake()
        {
            _instance = this;
        }

        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {
            if (UpdateFunc != null)
            {
                UpdateFunc(Time.deltaTime);
            }
        }

        public void Destroy()
        {
            UpdateFunc = null;
            _instance = null;
            Destroy(gameObject);
        }
    }
}
