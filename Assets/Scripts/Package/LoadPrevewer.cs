using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using UnityEngine;
using System;
public class LoadPrevewer : MonoBehaviour {
    //   string path= "Model/model";
    //// Use this for initialization
    //IEnumerator Start () {
    //       yield return null;
    //       var la = Resources.LoadAsync<GameObject>(path);
    //       yield return la;
    //       if(la.isDone)
    //       {
    //           var go = la.asset as GameObject;
    //           var newGo = GameObject.Instantiate(go);
    //           newGo.transform.SetParent(transform, false);
    //           newGo.transform.localScale = Vector3.one;
    //       }
    //   }
    int scope = -1;
    private IEnumerator Start()
    {
        scope = -1;
        var tick1 = DateTime.Now.Ticks;
            //print("1:"+ tick1);
        var thStart = new Thread(new ThreadStart(()=> {
            try
            {
                scope = War.Script.Utility.GetAreaScope();
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
                scope = 1;
            }
            //print("2:"+(DateTime.Now.Ticks - tick1));

        }));
        thStart.IsBackground = true;
        thStart.Start();
        //Debug.Log(scope);

        var last = Time.realtimeSinceStartup;

        while(scope < 0 && (Time.realtimeSinceStartup - last) <= 110.2f && Application.isPlaying)
        {
            //print("---------:"+(DateTime.Now.Ticks - tick1));

            yield return null;
        }
            //print("3:"+(DateTime.Now.Ticks - tick1));
        var sc = scope;
        if(sc<0)
        {
            sc = 1;
        }
        sc = Mathf.Clamp(scope, 0, 1);
        for (int i = 0; i < transform.childCount; i++)
        {
            var ch = transform.GetChild(i);
            ch.gameObject.SetActive(sc == i);
        } 

    } 
}
