using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class ResTag : MonoBehaviour {

    [ValueDropdown("GetTags")]
    public string cTag = "mini";
    [SerializeField]
    [FoldoutGroup("ResTagItem", expanded:true)]
    public List<ResTagItem> tags;

    [TextArea]
    public string parseList = "";
    public Object parsefile;
    //public void Exe(string tag)
    private  IEnumerable GetTags()
    {

        return tags.ConvertAll((rTi) => rTi.tag);
    }
#if UNITY_EDITOR
    [Button("ParseFromList")]
    public void ParseFromList()
    {
        if (tags == null) return;
        var parseCont = parseList;
        if (string.IsNullOrEmpty(parseList))
        {
            parseCont = File.ReadAllText(AssetDatabase.GetAssetPath(parsefile));
        }
        if (string.IsNullOrEmpty(parseCont)) return;
        var list = new List<string>();
        var lines = parseCont.Split('\n');
        foreach (var line in lines)
        {
            if (list.Contains(line)) continue;
            if (string.IsNullOrEmpty(line)) continue;
            list.Add(line);
        }
         
        if (list.Count>0)
        {
            var rtt = new ResTagItem();
            rtt.tag = rtt.GetHashCode().ToString();
            rtt.list = list;
            tags.Add(rtt);
        }

    }
    [Button("Exe")]
    public void Exe()
    {
        var tag = cTag;
        PackTag(tag);
    }

    [Button("FirstRes")]
    public void FirstRes()
    {
        PackTag("FirstRes", "Assets/EditorConfig/ResTag/FirstRes.bytes");
    }
    public static string[] PackTag(string tag, string path = "PackList.txt")
    {
        if (string.IsNullOrEmpty(tag)) return null;
        Debug.Log("PackTag:" + tag);
        var p = "Assets/EditorConfig/ResTag/ResTagConfig.prefab";
        var resGO = AssetDatabase.LoadAssetAtPath<GameObject>(p);
        if (!resGO)
        {
            Debug.LogError("error 没有包资源配置文件:" + p);
            EditorApplication.Exit(1);
            return null;
        }
        var resT = resGO.GetComponent<ResTag>();

        ResTagItem resTI = null;
        foreach (var node in resT.tags)
        {
            if (node.tag == tag)
            {
                resTI = node;
                break;
            }
        }
        if (resTI == null)
        {
            Debug.LogError("error 找不到包资源标签" + tag);
            EditorApplication.Exit(1);
            return null;
        }

        var files = resTI.Export(path);

        return files;
    }

#endif
    public static string ToJson(object o)
    {
        return Newtonsoft.Json.JsonConvert.SerializeObject(o);
    }
    public static T ToObj<T>(string s)
    {
        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(s);
    }
}
[System.Serializable]
public class ResTagItem
{
    public string tag;

    [OnValueChanged("addList")]
    //[FoldoutGroup("Drag", expanded:true)]
    public List<Object> adds;

    private void addList()
    {
#if UNITY_EDITOR
        if (adds.Count == 0 && list.Count > 0) return;
        list.Clear();
        foreach (var item in adds)
        {
            var add = AssetDatabase.GetAssetOrScenePath(item);
            if (string.IsNullOrEmpty(add)) continue;
            if (list.Contains(add)) continue;
            Debug.LogError(add);
            list.Add(add);
        }
#endif
    } 

    [Button("ClearAddList")]
    //[FoldoutGroup("Drag", expanded: true)]
    private void ClearAddList()
    {
        adds = new List<Object>();
    }

    [LabelText("计算配置")]
    [Sirenix.OdinInspector.FilePath] 
    //[FoldoutGroup("PickUp", expanded: true)]
    public List<string> list = new List<string>();

    [LabelText("固定配置")]
    public List<string> clist = new List<string>();
    [Button("ClearList")]
    //[FoldoutGroup("PickUp", expanded: true)]
    private void ClearList()
    {
        list = new List<string>();
    }

    [Button("ExportTag")]
    //[FoldoutGroup("PickUp", expanded: true)]
    public void ExportTag()
    {
        Export();
    }

    //[FoldoutGroup("PickUp", expanded: true)]
    public string[] Export(string path = "")
    {
        if (string.IsNullOrEmpty(path))
        {
            path = string.Format("Assets/EditorConfig/ResTag/{0}.json",tag);
        }
#if UNITY_EDITOR

        var resTI = this;
        var dic = new Dictionary<string, bool>();
        War.Base.AssetBundleManager.SetEncryptSets();
        System.Action<string> populateOneFile = (f) =>
        {
            if (File.Exists(f) == false)
            {
                System.Text.StringBuilder str = new System.Text.StringBuilder();
                str.Append(Application.dataPath);
                str.Append("/");
                var name = f.Replace(str.ToString(), "");
                dic[name] = true;
                return;
            }
            var abName = AssetDatabase.GetImplicitAssetBundleName("Assets/" + f.Replace(Application.dataPath + "/", ""));
            if (string.IsNullOrEmpty(abName))
            {
                Debug.LogError("ResTag asset not abname, asset_path:" + f);
                return;
            }
            abName = War.Base.AssetBundleManager.IsEncryptType(abName) ? abName + ".zip" : abName;
            dic[abName] = true;
            var dependencies = AssetDatabase.GetAssetBundleDependencies(abName, true);
            foreach (var ab in dependencies)
            {
                System.Text.StringBuilder str = new System.Text.StringBuilder();
                str.Append(ab);
                str.Append(".zip");
                var tmp = War.Base.AssetBundleManager.IsEncryptType(ab) ? str.ToString() : ab;
                dic[tmp] = true;
            }
        };
        System.Action<string> populateOneAB = (f) =>
        {
            dic[f] = true;
        };
        for (int i = 0; i < resTI.list.Count; i++)
        {
            System.Text.StringBuilder str = new System.Text.StringBuilder();
            str.Append(Application.dataPath);
            str.Append("/");
            str.Append(resTI.list[i]);
            string fullPath = str.ToString();
            if (Directory.Exists(fullPath))
            {
                var fs = Directory.GetFiles(fullPath, "*.*", SearchOption.AllDirectories);
                for (int j = 0; j < fs.Length; j++)
                {
                    string f = fs[j];
                    if (f.EndsWith(".meta")) continue;
                    if (f.EndsWith(".DS_Store")) continue;
                    populateOneFile(f);
                }
            }
            else
            {
                populateOneFile(fullPath);
            }

        }
        for (int i = 0; i < resTI.clist.Count; i++)
        {
            populateOneAB(resTI.clist[i]);
        }

        string[] files = new List<string>(dic.Keys).ToArray();
        File.WriteAllText(path, string.Join("\n", files));
        var json = ResTag.ToJson(dic.Keys);
        Debug.Log("PackList.txt :" + json);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        return files;

#endif

        return null;
    }

    [FoldoutGroup("State")]
    [Button("导出到粘贴板")]
    public void ExportClipBoard()
    {
        var str = "";
        foreach (var l in list)
        {
            str = string.Concat(str, l, "\n");
        }
        LogHelp.clipboard = str;
        this.Print("已经添加到粘贴板");
    }
    [FoldoutGroup("State")]
    [Button("从粘贴板导入")]
    public void ImportClipBoard()
    {
        var str = LogHelp.clipboard;
        var listA = str.Normalize().Split(' ');
        foreach (var l in listA)
        {
            list.Add(l);
        }
    }
}