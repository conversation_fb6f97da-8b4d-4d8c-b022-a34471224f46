using System.Collections.Generic;
using System.IO;
using UnityEngine;
using Newtonsoft.Json.Linq;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System;

[Serializable]
public struct LuaPackage
{
    public string packageName;
    public string version;

    public LuaPackage(string packageName, string version)
    {
        this.packageName = packageName;
        this.version = version;
    }
}

[Serializable]
public class LuaScriptList
{
    public List<string> luascr;
    public List<string> list;
}

public static class LuaPackageLoader
{
    private static readonly string ManifestPath = Path.Combine(Application.dataPath, "..", "Packages", "manifest.json");
    private static readonly string Packages = "Packages";
    private static readonly string runTime = "RunTime";
    private static readonly string Lua = "Lua";
    private static readonly string Prefabs = "Prefabs";
    private static readonly string UI = "UI";
    private static readonly string add_to_luaScript = "add_to_luaScript.txt";
    private static readonly string commonPath = "common";
    private static readonly string highCmdPath = "high";
    private static readonly string diffPath = "oHero";
    private static readonly string[] luaPackagePrefixs = new string[] { "com.sugw.", "com.bc.l." };
    /// <summary>
    /// 初始化LuaPackage
    /// </summary>
    public static void InitLuaPackage()
    {
        var luaPackageList = GetLuaPackagesList();
        if (luaPackageList == null)
        {
            Debug.LogError("无法找到或解析 manifest.json。");
            return;
        }

        foreach (var luaPackage in luaPackageList)
        {
            CopyLuaPackageLua(luaPackage.packageName);
            CopyLuaPackagePrefab(luaPackage.packageName);
            CopyLuaPackageDiffLua(luaPackage.packageName);
            CopyLuaPackageDiffPrefab(luaPackage.packageName);
            Debug.LogWarning($"初始化Lua包：packageName: {luaPackage.packageName}, version: {luaPackage.version}");
        }
#if UNITY_EDITOR
        // 最后刷新一次，以获取所有更改
        AssetDatabase.Refresh();
#endif
    }

    public static string[] LuaFileReplace(string[] files)
    {
        var luaPackageList = GetLuaPackagesList();
        if (luaPackageList == null)
        {
            Debug.LogError("无法找到或解析 manifest.json。");
            return files;
        }
        var fileSet = new HashSet<string>(files);
        var nameToPathDict = new Dictionary<string, string>();
        foreach(var file in files)
        {
            nameToPathDict[Path.GetFileName(file)] = file;
        }
        foreach (var luaPackage in luaPackageList)
        {
            LuaFileReplaceInternal(luaPackage, Path.Combine(runTime, commonPath), fileSet, nameToPathDict);
            LuaFileReplaceInternal(luaPackage, Path.Combine(runTime, diffPath), fileSet, nameToPathDict);
        }
        string targetPath = Path.Combine(Application.dataPath, Lua, highCmdPath);
        LuaFileReplaceInternal(targetPath, fileSet, nameToPathDict);
        return fileSet.ToArray(); 
    }

    static void LuaFileReplaceInternal(LuaPackage luaPackage, string subFolder, HashSet<string> fileSet, Dictionary<string, string> nameToPathDict)
    {
        var luaPath = Path.Combine(Packages, luaPackage.packageName, subFolder, Lua);
        if (!Directory.Exists(luaPath))
        {
            return;
        }
        string[] luaFiles = Directory.GetFiles(luaPath, "*.txt", SearchOption.AllDirectories);
        foreach (var luaFile in luaFiles)
        {
            var fileName = Path.GetFileName(luaFile);
            if (nameToPathDict.ContainsKey(fileName))
            {
                fileSet.Remove(nameToPathDict[fileName]);
            }
            fileSet.Add(luaFile);
            nameToPathDict[fileName] = luaFile;
        }
    }
    static void LuaFileReplaceInternal( string srcFolder, HashSet<string> fileSet, Dictionary<string, string> nameToPathDict)
    {
        if (!Directory.Exists(srcFolder))
        {
            return;
        }
        string[] luaFiles = Directory.GetFiles(srcFolder, "*.txt", SearchOption.AllDirectories);
        foreach (var luaFile in luaFiles)
        {
            var fileName = Path.GetFileName(luaFile);
            if (nameToPathDict.ContainsKey(fileName))
            {
                fileSet.Remove(nameToPathDict[fileName]);
            }
            fileSet.Add(luaFile);
            nameToPathDict[fileName] = luaFile;
        }
    }

    public static void LuaScriptReplace(Dictionary<string, bool> luaConfig)
    {
        var luaPackageList = GetLuaPackagesList();
        if (luaPackageList == null)
        {
            Debug.LogError("无法找到或解析 manifest.json。");
            return;
        }

        foreach (var luaPackage in luaPackageList)
        {
            LuaScriptReplaceInternal(luaPackage, Path.Combine(runTime, commonPath), luaConfig);
            LuaScriptReplaceInternal(luaPackage, Path.Combine(runTime, diffPath), luaConfig);
        }
    }

    static void LuaScriptReplaceInternal(LuaPackage luaPackage, string subFolder, Dictionary<string, bool> luaConfig)
    {
        var luaPath = Path.Combine(Packages, luaPackage.packageName, subFolder, Lua);
        var luaScriptPath = Path.Combine(Packages, luaPackage.packageName, subFolder, add_to_luaScript);
        if (!Directory.Exists(luaPath))
        {
            return;
        }
        if (File.Exists(luaScriptPath))
        {
            var lines = File.ReadAllLines(luaScriptPath);
            foreach (string line in lines)
            {
                string fileNameNoEx = Path.GetFileNameWithoutExtension(line.Trim());
                luaConfig[fileNameNoEx] = true;
            }
        }
    }

    /// <summary>
    /// 获取manifest中的Lua包
    /// </summary>
    /// <param name="manifestFilePath"></param>
    /// <returns></returns>
    private static List<LuaPackage> GetLuaPackagesList()
    {
        var manifestFilePath = ManifestPath;
        if (!File.Exists(manifestFilePath))
        {
            Debug.LogError("找不到 manifest.json。");
            return null;
        }

        string manifestJson = File.ReadAllText(manifestFilePath);
        var manifestObject = JObject.Parse(manifestJson);
        var dependencies = manifestObject?["dependencies"] as JObject;
        if (dependencies == null)
        {
            Debug.LogError("manifest.json 内容无效。");
            return null;
        }

        List<LuaPackage> luaPackagesList = new List<LuaPackage>();
        foreach (var package in dependencies.Properties())
        {
            foreach(var prefix in luaPackagePrefixs)
            {
                if (package.Name.StartsWith(prefix))
                {
                    var packageInfo = new LuaPackage(package.Name, (string)package.Value);
                    luaPackagesList.Add(packageInfo);
                    break;
                }
            }
        }

        return luaPackagesList;
    }
    /// <summary>
    /// 复制LuaPackage中的Lua
    /// </summary>
    /// <param name="packageName"></param>
    private static void CopyLuaPackageLua(string packageName)
    {
        string packagePath = Path.Combine(Packages, packageName, runTime, commonPath, Lua);
        string targetPath = Path.Combine(Application.dataPath, Lua);
        CopyAllFilesRecursive(packagePath, targetPath);
        UpdateLuaScriptList(packageName, commonPath);
    }
    /// <summary>
    /// 复制LuaPackage中的Prefabs
    /// </summary>
    /// <param name="packageName"></param>
    private static void CopyLuaPackagePrefab(string packageName)
    {
        string packagePath = Path.Combine(Packages, packageName, runTime, commonPath, Prefabs);
        string targetPath = Path.Combine(Application.dataPath, UI, Prefabs);
        CopyAllFilesRecursive(packagePath, targetPath);
    }
    /// <summary>
    /// 复制差异化Lua文件
    /// </summary>
    /// <param name="packageName"></param>
    private static void CopyLuaPackageDiffLua(string packageName)
    {
        string packagePath = Path.Combine(Packages, packageName, runTime, diffPath, Lua);
        string targetPath = Path.Combine(Application.dataPath, Lua);
        if (Directory.Exists(packagePath))
        {
            CopyAllFilesRecursive(packagePath, targetPath);
            UpdateLuaScriptList(packageName, diffPath);
        }
    }
    /// <summary>
    /// 复制差异化预制体
    /// </summary>
    /// <param name="packageName"></param>
    private static void CopyLuaPackageDiffPrefab(string packageName)
    {
        string packagePath = Path.Combine(Packages, packageName, runTime, diffPath, Prefabs);
        string targetPath = Path.Combine(Application.dataPath, UI, Prefabs);
        if (Directory.Exists(packagePath))
        {
            CopyAllFilesRecursive(packagePath, targetPath);
        }
    }
    /// <summary>
    /// 复制整个文件夹
    /// </summary>
    /// <param name="sourceFolderPath"></param>
    /// <param name="targetBasePath"></param>
    private static void CopyAllFilesRecursive(string sourceFolderPath, string targetBasePath)
    {
        if (!Directory.Exists(sourceFolderPath))
        {
            Debug.LogWarning($"在包中找不到文件夹：{sourceFolderPath}");
            return;
        }
        var files = Directory.GetFiles(sourceFolderPath, "*.*", SearchOption.AllDirectories);
        foreach (var file in files)
        {
            try
            {
                var relativePath = file.Substring(sourceFolderPath.Length + 1);
                string destFile = Path.Combine(targetBasePath, relativePath);
                var destDirectory = Path.GetDirectoryName(destFile);
                if (!Directory.Exists(destDirectory))
                {
                    Directory.CreateDirectory(destDirectory);
                }
                File.Copy(file, destFile, overwrite: true);
            }
            catch (IOException ex)
            {
                Debug.LogError($"复制文件时出错: {ex.Message}");
            }
        }
    }
    /// <summary>
    /// 更新LuaScript
    /// </summary>
    /// <param name="packageName"></param>
    private static void UpdateLuaScriptList(string packageName, string subFolder)
    {
        string addFilename = Path.Combine(Packages, packageName, runTime, subFolder, add_to_luaScript);
        if (!File.Exists(addFilename))
        {
            Debug.LogWarning($"找不到文件: {addFilename}");
            return;
        }
        // 从 add_to_luaScript.txt 文件中读取条目
        var lines = File.ReadAllLines(addFilename);
        string luascriptPath = Path.Combine(Application.dataPath, "Scripts", "GameUpdate", "Plugins", "_luascript.json");
        string jsonContent = File.ReadAllText(luascriptPath);
        var luaScriptList = JsonUtility.FromJson<LuaScriptList>(jsonContent);
        if (luaScriptList == null || luaScriptList.luascr == null)
        {
            Debug.LogError("JSON 内容无效或未找到 'luascr'");
            return;
        }
        foreach (string line in lines)
        {
            string fileNameNoEx = Path.GetFileNameWithoutExtension(line.Trim());
            if (!luaScriptList.luascr.Contains(fileNameNoEx))
            {
                luaScriptList.luascr.Add(fileNameNoEx);
            }
        }
        string updatedJsonContent = JsonUtility.ToJson(luaScriptList, true);
        File.WriteAllText(luascriptPath, updatedJsonContent);
    }
}
