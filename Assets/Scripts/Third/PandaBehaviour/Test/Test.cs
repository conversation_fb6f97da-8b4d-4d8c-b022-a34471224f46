using Panda;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Test : MonoBehaviour {

	// Use this for initialization
	void Start () {

    }
    [Task]
    public bool L(string m, int a1)
    {

        var s = (Status)a1;
        switch (s)
        {
            case Status.Ready:
                break;
            case Status.Running:
                break;
            case Status.Succeeded:
                Task.current.Succeed();

                break;
            case Status.Failed:
                Task.current.Fail();
                break;
            default:
                break;
        }
        "".Print(m, a1, Task.current.status);
        return Task.current.status == Status.Succeeded;

    }
    [Task]
    public void LVoid(string m, int a1)
    {

        var s = (Status)a1;
        switch (s)
        {
            case Status.Ready:
                break;
            case Status.Running:
                break;
            case Status.Succeeded:
                Task.current.Succeed();

                break;
            case Status.Failed:
                Task.current.Fail();
                break;
            default:
                break;
        }
        "".Print(m, a1, Task.current.status); 

    }

}
