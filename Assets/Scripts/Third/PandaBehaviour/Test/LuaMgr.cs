using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using XLua;
using Panda;
using War.Script;

namespace War.Script
{
	public class LuaMgr : MonoBehaviour
	{

		internal static LuaEnv luaEnv = null;// new LuaEnv(); //all lua behaviour shared one luaenv only!
		public Action<string> LuaCall;

		public void Init()
		{
			luaEnv = LuaManager.Instance.luaEnv;
			luaEnv.Global.Get("__LuaCall", out LuaCall);
			"".Print("LuaCallis", LuaCall);

		}

		[Task]
		public void L(string m)
		{
			 LReal(m);
		}
		[Task]
		public void L(string m, string a1)
		{
			 LReal(m, a1);
			
		}
		[Task]
		public void L(string m, string a1, string a2)
		{
			 LReal(m, a1, a2);
		}
		[Task]
		public void L(string m, string a1, string a2, string a3)
		{
			 LReal(m, a1, a2, a3);
			
		}
		[Task]
		public void L(string m, string a1, string a2, string a3, string a4)
		{
			 LReal(m, a1, a2, a3, a4);
			
		}
		[Task]
		public void L(string m, string a1, string a2, string a3, string a4, string a5)
		{
			 LReal(m, a1, a2, a3, a4, a5);
			
		}
		int tree_ind = -1;

		[Task]
		public void LReset()
		{
			tree_ind = -1;
			//"".Print("tree_ind==================", tree_ind);
			//Debug.Log("tree_ind" + tree_ind);
			Task.current.Succeed();
		}

		bool LReal(params string[] args)
		{
			string s = string.Join("#", args);
			//Debug.Log("s=======================" + s);
			if (LuaCall != null)
			{
				tree_ind++;
				//Debug.Log("s=======================" + s+ ",tree_ind"+ tree_ind);
				//LuaCall(tree_ind+"#"+s);
				LuaCall(s);
			}
			else
			{
				Debug.Log("LuaCall = null");
			}
			//"".Print("Task.current.status", Task.current.status);

			//if(Task.current.status!= Status.Running)
			//{
			//	//if(Task.current.status == Status.Succeeded)
			//	//	tree_ind++;
			//	Task.current.Complete(Task.current.status == Status.Succeeded);
			//}
			//if (Task.current.status == Status.Succeeded) return true;
			return false;
		}


		//[Task] // <-- Attribute used to tag a class member as a task implementation.
		//void L(string luafunc)
		//{
		//	Debug.Log("luafunc" + luafunc);
		//	if (LuaCall != null)
		//	{
		//		LuaCall(luafunc);
		//	}
		//	else
		//	{
		//		Debug.Log("LuaCall = null");
		//	}
		//	 // <-- Task.current gives access to the run-time task bind to this method.
		//}
		//[Task] // <-- Attribute used to tag a class member as a task implementation.
		//void L2(string luafunc,string p1)
		//{
		//	Debug.Log("luafunc" + luafunc);
		//	if (LuaCall != null)
		//	{
		//		StringBuilder builder = new StringBuilder();
		//		builder.Append(luafunc);
		//		builder.Append("#");
		//		builder.Append(p1);
		//		string s = builder.ToString();
		//		Debug.Log("builder.ToString()" + s);
		//		LuaCall(s);
		//	}
		//	else
		//	{
		//		Debug.Log("LuaCall = null");
		//	}
		//	 // <-- Task.current gives access to the run-time task bind to this method.
		//}
	}
}
