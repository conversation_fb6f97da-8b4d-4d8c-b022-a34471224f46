using System;
using System.IO;
using System.Linq;
using UnityEngine;

public class Folder<PERSON>hecker
{
    public static void CheckFolder()
    {
        string listPath = @"Assets\Scripts\ExecLua\list.txt";
        string rootPath = @"Assets\Art\Effects\Effects";

        int allNum = 0;
        int dirNum = 0;

        // 逐行读取文件内容
        foreach (var line in File.ReadLines(listPath))
        {
            // 去除空行和空白行
            string trimmedLine = line.Trim();
            if (string.IsNullOrEmpty(trimmedLine))
            {
                continue;
            }

            allNum++;

            // 构建完整路径
            string filePath = Path.Combine(rootPath, trimmedLine);
            filePath = Path.GetFullPath(filePath);

            // 去除 .meta 扩展名
            // if (filePath.EndsWith(".meta", StringComparison.OrdinalIgnoreCase))
            // {
            //     filePath = Path.GetDirectoryName(filePath) ?? ;
            // }
            filePath = filePath.Replace(".meta", "");

            Debug.Log(filePath);

            // 检查路径是否为文件夹
            bool isDirectory = Directory.Exists(filePath);
            Debug.Log($"{filePath} {isDirectory}");

            if (isDirectory)
            {
                dirNum++;
            }
        }

        Debug.Log($"allNum={allNum} dirNum={dirNum}");
    }

    public static void Main(string[] args)
    {
        CheckFolder();
    }
}