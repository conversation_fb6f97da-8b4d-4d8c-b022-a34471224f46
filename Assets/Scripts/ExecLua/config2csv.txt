
local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local typeof    = typeof
local string    = string
local table     = table
local tonumber = tonumber
local loadstring = loadstring
local setmetatable = setmetatable
local log = require "log"
require "extern"
local math =math
local os = os
local type = type
local next = next
local IO = CS.System.IO
local File = CS.System.IO.File


local tablePath = "Assets/Lua/Tables/en_oumei/Database/"
local savePath = "Log/Txt/"

-----HERE 需要反序列的表格填写在txtList中
local txtList = {
    --"Effect.txt",
    "ActivityContent.txt",
}

function GetTableFrom0(tb)
    --数组如果是枚举的话,是共用的,可能已经处理过了
    if tb[0] then
        return tb
    end
    local v1 = tb[1]
    if v1 then
        table.remove(tb, 1)
        tb[0]=v1
    end
    return tb
end

local arrayDic ={}
--如果是组合的数组,则直接赋值成下标为0的数组;如果是普通数组,则第一个值是下标为0的数组,第二个值是数组的长度
function GetTableArray(tableData, fieldName, configData)

    if not arrayDic[configData] then
        local tmpObj = nil
        if tableData.combineFields and table.index(tableData.combineFields, fieldName) then

            local count = #configData
            tmpObj = GetTableFrom0(configData)
            tmpObj.count = count
            tmpObj.isTable = true
        else
            --tmpObj = GetTableFrom0(configData)
            tmpObj = {}
            tmpObj[2] = #(configData)
            tmpObj[1] = GetTableFrom0(configData)
            tmpObj.__index = TableGet
            tmpObj.isTable = true
            tmpObj.__newIndex = function() error('Attempt to modify read-only table') end
            setmetatable(tmpObj, tmpObj)
        end
        arrayDic[configData]=tmpObj
    end
    return arrayDic[configData]

end

function SetIndex(recordData,fileData)
    --print("SetIndex",recordData)
    recordData.isSetMt=true
    recordData.__index = function(t,k)
        local kIndex= fileData.fieldNames[k]
        if kIndex then
            local fieldData = nil
            if fileData.vEnums then
                index =(t[kIndex] or 0 ) +1
                fieldData =  fileData.vEnums [index]
            elseif fileData.splitEnum then
                local tmp =t[kIndex]

                index =( tmp or 0 ) +1
                fieldData = GetDataBySplitData(fileData.splitEnum,index)
            else
                fieldData = t[kIndex]
            end

            if type(fieldData) == "table"  then
                --取值的时候如果是数组,转成原来数组的结构,并缓存修改后的数组
                --print("GetTableArray!!")
                fieldData = GetTableArray(fileData, k, fieldData)
            end
            return  fieldData
        end
    end
    recordData.__newindex = function() log.Error('Attempt to modify read-only table') end
    setmetatable(recordData,recordData)
end

function Reorganise(path)
    local fileName = path
    path = tablePath..path
    print("Reorganise",path)
    local refresh_text = File.ReadAllText(path)
    local func = loadstring(refresh_text)
    local fileData = func()
    local recordData = nil
    local num = fileData.num or 0
    local fieldNames = fileData.fieldNames
    local nameList = {}
    for k,v in pairs(fieldNames) do
        nameList[v] = k
    end

    local tList = {}
    for index=0,num-1 do  --
        local colt = {}
        if fileData and fileData.addInfos then
            recordData = fileData.addInfos[index+1]
            if recordData and not recordData.isSetMt then
                SetIndex(recordData,fileData)
            end
        elseif fileData and fileData.splitAdd then
            if type(fileData.splitNum) == "table" then
                --print(" type(fileData.splitNum) == table ")
                local tableArray = fileData.splitNum
                -- index + 1后看看是落在tableArray的哪个区间，如果不在第一个区间，计算第二个区间需要加上第一个区间的数，tableArray是{10，15，20}这样的数据
                local tableIndex = 1
                -- 计算index是第几个表的数据
                local cumulativeLength = 0
                for i, v in ipairs(tableArray) do
                    cumulativeLength = cumulativeLength + v
                    if index < cumulativeLength then
                        tableIndex = i
                        cumulativeLength = cumulativeLength - v
                        break
                    end
                end
                local tableName = fileData.splitAdd[tableIndex]                 --获取表名    
                local tableData = require(tableName)                            --加载表数据
                local realIndex = index + 1 - cumulativeLength
                recordData = tableData[realIndex]
                if recordData == nil then
                    log.Error("data_table_mgr table type recordData is nil!", tName, index)
                else
                    SetIndex(recordData,fileData)
                end
            else
                --print(" type(fileData.splitNum) == Num ")
                local tableLength = fileData.splitNum
                local tableIndex = math.ceil((index + 1) / tableLength) -- 计算index是第几个表的数据
                local tableName = fileData.splitAdd[tableIndex]                 --获取表名
                local tableData = require(tableName)                            --加载表数据
                local realIndex = index + 1 - (tableIndex - 1) * tableLength    -- 计算在表中的最终index
                recordData = tableData[realIndex]
                --print("tableName",tableName,recordData)
                if recordData == nil then
                    log.Error("data_table_mgr number type recordData is nil!", tName, index)
                else
                    SetIndex(recordData,fileData)
                end
                --print("recordData ID:",recordData.ID)
            end
        end
        
        for ii,v in ipairs(nameList) do

            local val = recordData[v]
            local col = val
            if type(val) == "table" then
                local vt = {}
                if val.count~=nil then
                    for i=0,val.count do
                        vt[i+1] = val[i]
                    end
                else
                    --print("val. num =="..val[2])
                    local tblNum = val[2]
                    if tblNum>0 then
                        for i=0,tblNum-1 do
                            vt[i+1] = val[1][i]
                        end
                    end
                end
                col = table.concat(vt,'#')
            end

            colt[ii] = col
        end

        local line = table.concat(colt,',')
        tList[index+1] = line
    end

    local theadstr = table.concat(nameList,',') .. "\n"
    local tstr = table.concat(tList,'\n')
    print("Save",savePath..fileName..".csv")

    File.WriteAllText(savePath..fileName..".csv",theadstr..tstr)
end

for i,path in ipairs(txtList) do
    Reorganise(path)
end