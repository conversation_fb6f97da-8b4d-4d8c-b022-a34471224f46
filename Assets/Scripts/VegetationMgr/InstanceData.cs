using System;
using UnityEngine;
using System.Collections.Generic;
namespace Vegetation
{
    [Serializable]
    public struct ItemData
    {
        public Vector2 m_posWS;
        public Vector3 m_size;
        public int m_matIndex;
        public int m_meshIndex;
    }

    [Serializable]
    public class ChunkData
    {
        public int m_chunkIndex;
        public Vector2 m_chunkPosition;
        /*public Vector3 m_chunkSize;*/
        public List<ItemData> m_itemDataLst;
    }

    [Serializable]
    public class LODData
    {
        public int m_lodIndex;
        public List<ChunkData> m_chunkDataLst;
    }

    [Serializable]
    public class InstanceData : ScriptableObject
    {
        public List<LODData> m_lodDataLst;
        public List<Mesh> m_meshLst;
        public List<Material> m_matLst;
        public Vector3 m_chunkSize = new Vector3(64,10,64);
    }

}
