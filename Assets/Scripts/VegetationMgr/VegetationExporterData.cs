using System;
using UnityEngine;

namespace Vegetation
{
    [Serializable]
    public class VegetationExporterData : ScriptableObject
    {
        #region private field
        [SerializeField]
        private Vector4 m_minMaxPoint = new Vector4(-90, -270, 1350, 1350);

        [Min(0.1f)]
        [SerializeField]
        private float m_density = 0.5f;

        [Space(5)]
        [Header("Templates")]
        [SerializeField]
        private GameObject[] m_treeTemplates;

        [Space(2)]
        [SerializeField]
        private int[] m_templateWeights;

        [SerializeField]
        private int m_chunkSize = 64;
        #endregion

        #region public properties
        public Vector4 MinMaxPoint
        {
            get
            {
                return m_minMaxPoint;
            }
        }

        public float Density
        {
            get
            {
                return m_density;
            }
        }

        public GameObject[] TreeTemplates
        {
            get
            {
                return m_treeTemplates;
            }
            set
            {
                m_treeTemplates = value;
            }
        }

        public int ChunkSize
        {
            get
            {
                return m_chunkSize;
            }
            set
            {
                m_chunkSize = value;
            }
        }

        public int[] TemplateWeights
        {
            get
            {
                return m_templateWeights;
            }
            set
            {
                m_templateWeights = value;
            }
        }
        #endregion
    }

}
