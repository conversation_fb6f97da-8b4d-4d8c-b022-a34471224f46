using UnityEngine;
using System.Collections.Generic;
using Unity.Jobs;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using System.Runtime.CompilerServices;
using System;
using UnityEngine.UIElements;
using War.Script;
using System.Linq;
using UnityEngine.Profiling;
using UnityEngine.Rendering;
using XLua;
using bc;
using UnityEngine.UI;
using War.Base;
namespace Vegetation
{
    public class JobHandleManger
    {
        public static JobHandleManger Instance;
        List<IJobProxy> removeList = new List<IJobProxy>();
        public interface IJobProxy
        {
            JobHandle CreateJobHandle();
            void OnJobCompleted();
        }

        Dictionary<IJobProxy, JobHandle> mAllJobs = new Dictionary<IJobProxy, JobHandle>();

        public JobHandleManger()
        {

        }

        public void ExecuteJob(IJobProxy job)
        {
            if (mAllJobs.ContainsKey(job))
                return;
            mAllJobs.Add(job, job.CreateJobHandle());
        }

        public void RemoveJob(IJobProxy job)
        {
            JobHandle handle;
            if (!mAllJobs.TryGetValue(job, out handle))
                return;

            handle.Complete();
            mAllJobs.Remove(job);
        }

        public void CompleteJob(IJobProxy job)
        {
            JobHandle handle;
            if (!mAllJobs.TryGetValue(job, out handle))
                return;
            mAllJobs.Remove(job);
            handle.Complete();
            job.OnJobCompleted();
        }

        public void OnLateUpdate()
        {
            removeList.Clear();
            var iter = mAllJobs.GetEnumerator();
            while (iter.MoveNext())
            {
                var pair = iter.Current;
                var jobHandle = pair.Value;
                if (!jobHandle.IsCompleted)
                {
                    continue;
                }

                jobHandle.Complete();
                removeList.Add(pair.Key);
            }

            for (int removeIndex = 0; removeIndex < removeList.Count; removeIndex++)
            {
                mAllJobs.Remove(removeList[removeIndex]);
                removeList[removeIndex].OnJobCompleted();
            }

        }

    }

    [BurstCompile]
    public struct ChunkCullingJob : IJobParallelFor
    {
        [ReadOnly][NativeDisableParallelForRestriction] [DeallocateOnJobCompletion] private NativeArray<float4> m_frustumPlanes;
        [ReadOnly] private NativeArray<float3> m_chunkPosWS;
        [WriteOnly] public NativeArray<int> m_cullResults;
        [ReadOnly] private NativeArray<Bounds> m_bounds;

        public ChunkCullingJob(NativeArray<float4> frustumPlanes,NativeArray<float3> chunkPosWS,NativeArray<int> cullReuslt,NativeArray<Bounds> bounds)
        {
            m_frustumPlanes = frustumPlanes;
            m_chunkPosWS = chunkPosWS;
            m_cullResults = cullReuslt;
            m_bounds = bounds;
        }

        public void Execute(int index)
        {
            Bounds bounds = m_bounds[index];
            float3 extent = new float3(bounds.extents.x, bounds.extents.y, bounds.extents.z);

            int length = m_frustumPlanes.Length;
            bool inSide = true;
            for (int i = 0; i < length; i++)
            {
                float4 plane = m_frustumPlanes[i];
                float3 normal = plane.xyz;
                float dist = math.dot(normal, m_chunkPosWS[index]) + plane.w;
                float radius = math.dot(extent, math.abs(normal));
                if (dist <= -radius)
                {
                    inSide = false;
                   
                }
            }
            if(inSide==true)
            {
                m_cullResults[index] = 1;
            }
            else
            {
                m_cullResults[index] = 0;
            }     
        }
    }

    public class VegetationCellJob : JobHandleManger.IJobProxy
    {
        #region private field
      
        private NativeArray<float3> m_chunkPosWS;
        public NativeArray<int> m_cullingResults;
        private NativeArray<Bounds> m_bounds;
        private Camera m_camera;
        private Plane[] frustrumPlanes;
        #endregion

        #region public properties
        public event Action<NativeArray<int>> OnCompleteJobAction;
        #endregion

        #region public method
        public void InitNativeData(List<Vector3> chunkPosWS,List<Vector3> chunkSize,Camera camera)
        {
            m_chunkPosWS = new NativeArray<float3>(chunkPosWS.Count, Allocator.Persistent);
            m_bounds = new NativeArray<Bounds>(chunkSize.Count, Allocator.Persistent);
            for(int boundIndex=0;boundIndex<m_bounds.Length;boundIndex++)
            {
                var position = chunkPosWS[boundIndex];
                m_chunkPosWS[boundIndex] = position;

                m_bounds[boundIndex] = new Bounds(position, chunkSize[boundIndex]);
            }
            m_cullingResults = new NativeArray<int>(chunkPosWS.Count, Allocator.Persistent);
            m_camera = camera;
            frustrumPlanes = new Plane[6];
        }

        public void DisposeData()
        {
            JobHandleManger.Instance.RemoveJob(this);

            if (m_cullingResults.IsCreated)
                m_cullingResults.Dispose();
            if (m_bounds.IsCreated)
                m_bounds.Dispose();
            if (m_chunkPosWS.IsCreated)
                m_chunkPosWS.Dispose();
        }

        public void CreateJob()
        {
            JobHandleManger.Instance.ExecuteJob(this);
        }

        public void OnCompleteJob()
        {
            JobHandleManger.Instance.CompleteJob(this);
        }
        #endregion

        #region JobHandle Manager
        public JobHandle CreateJobHandle()
        {
            Profiler.BeginSample("VegetationInstanceRender.CreateChunkCullJob");

            GeometryUtility.CalculateFrustumPlanes(m_camera.projectionMatrix * m_camera.worldToCameraMatrix, frustrumPlanes);
            NativeArray<float4> m_frustumPlanes = new NativeArray<float4>(6, Allocator.TempJob);
            for (int i=0;i<6;i++)
            {
                float4 plane;
                plane.x =  frustrumPlanes[i].normal.x;
                plane.y = frustrumPlanes[i].normal.y;
                plane.z = frustrumPlanes[i].normal.z;
                plane.w = frustrumPlanes[i].distance;
                m_frustumPlanes[i] = plane;
            }
            ChunkCullingJob cullingJob = new ChunkCullingJob(m_frustumPlanes, m_chunkPosWS, m_cullingResults, m_bounds);
            var jobHandle = cullingJob.Schedule(m_chunkPosWS.Length, 16);

            Profiler.EndSample();
            return jobHandle;
        }

        public void OnJobCompleted()
        {
            OnCompleteJobAction.Invoke(m_cullingResults);
        }
        #endregion
    }

    public class RaycastJob : JobHandleManger.IJobProxy
    {
        #region private field
        private NativeArray<RaycastCommand> m_rayCastCommands;
        private NativeArray<RaycastHit> m_rayCastResults;
        private List<ItemData> m_collect_itemDatas;
        private LayerMask m_cullLayer;
        private int m_lst_count = -1;
        #endregion

        #region const
        const int MAX_DRAW_NUM = 1024;
        #endregion

        #region public properties
        public event Action<NativeArray<RaycastHit>> OnCompleteJobAction;
        public List<ItemData> Collect_ItemDatas
        {
            get
            {
                return m_collect_itemDatas;
            }
            set
            {
                m_collect_itemDatas = value;
            }
        }

        public int LstCount
        {
            get
            {
                return m_lst_count;
            }
        }
        #endregion

        #region public method
        public void InitNativeData(LayerMask cullLayer)
        {
            m_rayCastCommands = new NativeArray<RaycastCommand>(MAX_DRAW_NUM, Allocator.Persistent);

            m_rayCastResults = new NativeArray<RaycastHit>(MAX_DRAW_NUM, Allocator.Persistent);
            m_cullLayer = cullLayer;
        }

        public void DisposeData()
        {
            JobHandleManger.Instance.RemoveJob(this);
            if (m_rayCastCommands.IsCreated)
                m_rayCastCommands.Dispose();
            if (m_rayCastResults.IsCreated)
                m_rayCastResults.Dispose();
        }

        public void OnCompleteJob()
        {
            JobHandleManger.Instance.CompleteJob(this);
        }

        public void CreateJob(List<ItemData> collect_item_data_lst)
        {
            m_collect_itemDatas = collect_item_data_lst;
            JobHandleManger.Instance.ExecuteJob(this);
        }
        #endregion

        #region JobHandle Manager
        public JobHandle CreateJobHandle()
        {
            m_lst_count = m_collect_itemDatas.Count;
           if (m_collect_itemDatas!=null&&m_collect_itemDatas.Count>0)
           {
                Profiler.BeginSample("VegetationInstanceRender.CreateRayCastJob");

                int rayCount = m_collect_itemDatas.Count;
                rayCount = Mathf.Min(rayCount, MAX_DRAW_NUM);
                Vector3 YOffset = new Vector3(0, 10, 0);
                int rayIndex = 0;
                for(int i =0;i<rayCount;i++)
                {
                    if (rayIndex >= MAX_DRAW_NUM/*-3*/)
                        break;

                    ItemData itemData = m_collect_itemDatas[i];
                    Vector3 origin = Vector3.zero;

                    for (int j = 0; j < 1;j++)
                    {
                        origin.Set(itemData.m_posWS.x, 0, itemData.m_posWS.y);
                        if(j==0)
                        {
                            origin = origin + YOffset;
                        }
                        else if(j==1)
                        {
                            origin = origin + (itemData.m_size / 4) + YOffset;
                        }
                        else if(j==2)
                        {
                            origin = origin - (itemData.m_size / 4) + YOffset;
                        }

                        Vector3 direction =  Vector3.down;   
                        m_rayCastCommands[rayIndex] = new RaycastCommand(origin, direction, 100.0f, m_cullLayer);
                        rayIndex++;
                    }                    
                }
                var jobHandle = RaycastCommand.ScheduleBatch(m_rayCastCommands, m_rayCastResults, 16, default);

                Profiler.EndSample();
                return jobHandle;
      
            }
           else
           {
                return default(JobHandle);
           }

         
        }

        public void OnJobCompleted()
        {
            OnCompleteJobAction.Invoke(m_rayCastResults);
        }
        #endregion
    }

    public class InstanceRenderData
    {
        public Mesh m_mesh;
        public Material m_material;
        public List<Matrix4x4> m_matrix = new List<Matrix4x4>();
    }

    public class VegetationInstanceRender : MonoBehaviour
    {
        #region private field
        [SerializeField]
        private InstanceData m_instanceData;

        private VegetationCellJob m_cellJob;
        private RaycastJob m_rayCastJob;

        private Dictionary<int, Dictionary<int, List<ItemData>>> m_lod_chunk_dict;
        private List<Vector3> m_chunkPosLst;
        private List<Vector3> m_chunkSizeLst;
        Dictionary<int, List<ItemData>> m_cur_chunk_itemlst_dict;
        List<ItemData> m_collect_visible_itemLst;

        private Camera m_camera;

        //[SerializeField]
        [Range(0,2)]
        private int m_curLod = 0;

        private List<InstanceRenderData> m_instanceRenderQueue;

        [SerializeField]
        private Vector3 m_objectRotation = new Vector3(40, 0, 0);
        private Quaternion m_rot;

        [SerializeField]
        private LayerMask m_cullLayer;

        [Space(10)]
        [Header("SLG Relative")]
        [SerializeField]
        private int[] m_slg_lod_layer;
        private Dictionary<int, int> m_lod_dict;

        [Space(10)]
        [Header("Performance Optimization")]
        [SerializeField]
        private bool m_enable_optimization = true;
        private Vector3 m_lastCameraPos = Vector3.positiveInfinity;
        [SerializeField]
        [Min(0.1f)]
        private float m_update_distance_threshold = 0.05f;
        private Dictionary<float, Matrix4x4> m_cache_m_matrices;
        [SerializeField]
        private bool m_cache_matrix = true;
        private bool m_cur_frame_update = true;

        #endregion

        #region public field
        public static VegetationInstanceRender Instance;
        public InstanceData InstanceData
        {
            set
            {
                m_instanceData = value;
            }
        }
        public int CurLod
        {
            get
            {
                return m_curLod;
            }
            set
            {
                m_curLod = value;
            }
        }

        public Camera RenderCamera
        {
            get
            {
                return m_camera;
            }
            set
            {
                m_camera = value;
            }
        }
        #endregion

        #region private method
        void InitInstanceData()
        {
            var lodDataLst =  m_instanceData.m_lodDataLst;
            m_lod_chunk_dict = new Dictionary<int, Dictionary<int, List<ItemData>>>();
            m_chunkPosLst = new List<Vector3>();
            m_chunkSizeLst = new List<Vector3>();

            Vector3 chunkPos = Vector3.zero;
            for(int lodIndex=0;lodIndex<lodDataLst.Count;lodIndex++)
            {
                var lodData = lodDataLst[lodIndex];
                Dictionary<int, List<ItemData>> chunk_itemlst_dict = new Dictionary<int, List<ItemData>>();
                for (int chunkIndex = 0; chunkIndex < lodData.m_chunkDataLst.Count;chunkIndex++)
                {
                    var chunkData = lodData.m_chunkDataLst[chunkIndex];
                    chunk_itemlst_dict.Add(chunkData.m_chunkIndex, chunkData.m_itemDataLst);

                    if(lodIndex==0)
                    {
                        chunkPos.Set(chunkData.m_chunkPosition.x, 0, chunkData.m_chunkPosition.y);
                        m_chunkPosLst.Add(chunkPos);
                        m_chunkSizeLst.Add(m_instanceData.m_chunkSize);
                    }
                }
                m_lod_chunk_dict.Add(lodData.m_lodIndex, chunk_itemlst_dict);
            }

            JobHandleManger.Instance = new JobHandleManger();
            m_cellJob = new VegetationCellJob();
            m_collect_visible_itemLst = new List<ItemData>();

            m_rayCastJob = new RaycastJob();

            m_instanceRenderQueue = new List<InstanceRenderData>();

            m_rot  = Quaternion.Euler(m_objectRotation);

            m_cache_m_matrices = new Dictionary<float, Matrix4x4>();
            m_lod_dict = new Dictionary<int, int>();
        }


        private void InstanceRender()
        {
            if(m_instanceRenderQueue!=null&&m_instanceRenderQueue.Count>0)
            {
                for(int renderIndex=0;renderIndex<m_instanceRenderQueue.Count;renderIndex++)
                {
                    var renderData =  m_instanceRenderQueue[renderIndex];
                    var mesh = renderData.m_mesh;
                    var mat = renderData.m_material;
                    var matrices = renderData.m_matrix;
                    if (mesh == null || mat == null || matrices == null||matrices.Count<=0)
                        continue;

                    mat.enableInstancing = true;
                    Graphics.DrawMeshInstanced(mesh, 0, mat, matrices);
                }
            }
            
        }

        private bool EnableUpdateData()
        {
            bool enable = true;
            if (m_enable_optimization==true)
            {
                if(m_instanceRenderQueue.Count<=0)
                {
                    enable = true;
                }else
                {
                    var moveDistance = (m_camera.transform.position - m_lastCameraPos).magnitude;
                    if (moveDistance < m_update_distance_threshold)
                    {
                        enable = false;
                    }
                    m_lastCameraPos = m_camera.transform.position;
                }
            }
            return enable;
        }

        private void DontCullRender()
        {
            for (int queue = 0; queue < m_instanceRenderQueue.Count; queue++)
            {
                m_instanceRenderQueue[queue].m_matrix.Clear();
            }

            for (int itemIndex=0; itemIndex<m_collect_visible_itemLst.Count; itemIndex++)
            {

                RenderEnQueue(itemIndex);
            }
        }

        Matrix4x4 matrix = Matrix4x4.identity;
        Vector3 positionWS = Vector3.zero;
        Vector3 scale = Vector3.one;
        private void RenderEnQueue(int itemIndex)
        {
            var itemData = m_collect_visible_itemLst[itemIndex];
            positionWS.Set(itemData.m_posWS.x, 0, itemData.m_posWS.y);
            scale.Set(itemData.m_size.x, itemData.m_size.x, itemData.m_size.x);
            if (false)
            {
                var key = Vector3.Dot(positionWS, scale);
                if (m_cache_m_matrices.ContainsKey(key))
                {
                    m_cache_m_matrices.TryGetValue(key, out matrix);
                }
                else
                {
                    matrix = Matrix4x4.TRS(positionWS, m_rot, scale);
                    m_cache_m_matrices.Add(key, matrix);
                }

            }
            else
            {
                matrix = Matrix4x4.TRS(positionWS, m_rot, scale);
            }

            var mesh = m_instanceData.m_meshLst[itemData.m_meshIndex];
            var mat = m_instanceData.m_matLst[itemData.m_matIndex];

            InstanceRenderData renderData = null;
            for (int i = 0; i < m_instanceRenderQueue.Count; i++)
            {
                if (m_instanceRenderQueue[i].m_material == mat && m_instanceRenderQueue[i].m_mesh == mesh)
                {
                    renderData = m_instanceRenderQueue[i];
                    break;
                }
            }
            if (renderData == null)
            {
                renderData = new InstanceRenderData();
                m_instanceRenderQueue.Add(renderData);

                renderData.m_matrix = new List<Matrix4x4>();
                renderData.m_mesh = mesh;
                renderData.m_material = mat;
            }

            if(renderData.m_matrix.Count<1023)
            {
                renderData.m_matrix.Add(matrix);
            }
        }
        #endregion

        #region Unity loop
        private void Awake()
        {
            if (m_instanceData == null)
                return;

            InitInstanceData();
            Instance = this;
        }

        private void OnEnable()
        {
            m_cellJob.OnCompleteJobAction += OnCompleteJobAction;
            m_rayCastJob.OnCompleteJobAction += OnCompleteJobAction;
            if(LevelDetailCamera.instance!=null)
            {
                LevelDetailCamera.instance.AddLodChange(LODChange);
            }
        }

        private void OnDisable()
        {
            m_cellJob.OnCompleteJobAction -= OnCompleteJobAction;
            m_rayCastJob.OnCompleteJobAction -= OnCompleteJobAction;
            if (LevelDetailCamera.instance != null)
            {
                LevelDetailCamera.instance.RemoveLodChange(LODChange);
            }
        }

        private void Start()
        {
            m_camera = Camera.main;
            m_cellJob.InitNativeData(m_chunkPosLst, m_chunkSizeLst, m_camera);
            m_rayCastJob.InitNativeData(m_cullLayer);
        }

        private void Update()
        {
            if (m_camera == null)
                return;

            if (m_curLod < 0)
                return;

            m_cur_frame_update = EnableUpdateData();
            if (m_cur_frame_update)
            {
                m_cellJob.CreateJob();
            }

            m_rayCastJob.CreateJob(m_collect_visible_itemLst);

            //if (m_curLod<1)
            //{
            //    m_rayCastJob.CreateJob(m_collect_visible_itemLst);
            //}
            //else
            //{
            //    DontCullRender();
            //}

            InstanceRender();
        }

        private void LateUpdate()
        {
            if (m_camera == null)
                return;

            if (m_curLod < 0)
                return;

            JobHandleManger.Instance.OnLateUpdate();
        }

        private void OnDestroy()
        {
            m_cellJob.DisposeData();
            m_rayCastJob.DisposeData();
        }
        #endregion

        #region Event
        private void OnCompleteJobAction(NativeArray<int> cullResult)
        {
            Profiler.BeginSample("VegetationInstanceRender.ChunkFrustumCull");

            m_lod_chunk_dict.TryGetValue(m_curLod, out m_cur_chunk_itemlst_dict);

            if (m_cur_chunk_itemlst_dict == null)
                return;
            m_collect_visible_itemLst.Clear();
            List<ItemData> curItemLst;

            for (int cullResultIndex=0;cullResultIndex<cullResult.Length;cullResultIndex++)
            {
                if (cullResult[cullResultIndex]>0)
                {
                    m_cur_chunk_itemlst_dict.TryGetValue(cullResultIndex, out curItemLst);
                    if (curItemLst != null)
                    {
                        m_collect_visible_itemLst.AddRange(curItemLst);
                    }
                }
            }
            Profiler.EndSample();
        }

        private void OnCompleteJobAction(NativeArray<RaycastHit> rayCastHits)
        {
            Profiler.BeginSample("VegetationInstanceRender.RaycastJobComplete");

            //��Ч����
            if (m_rayCastJob.LstCount != m_collect_visible_itemLst.Count)
                return;
            for(int queue = 0;queue<m_instanceRenderQueue.Count;queue++)
            {
                m_instanceRenderQueue[queue].m_matrix.Clear();
            }

            int itemIndex=0;
            for (int rayCastIndex=0; rayCastIndex < rayCastHits.Length;rayCastIndex++ /*rayCastIndex+=3*/)
            {
                if(itemIndex >= m_collect_visible_itemLst.Count)
                {
                    break;
                }

                if (rayCastHits[rayCastIndex].point == Vector3.zero /*&& rayCastHits[rayCastIndex + 1].point == Vector3.zero && rayCastHits[rayCastIndex + 2].point == Vector3.zero*/)
                {
                    //can draw instance
                    RenderEnQueue(itemIndex);
                }

                itemIndex++;
            }
            Profiler.EndSample();
        }

        public void LODChange(int pre,int cur)
        {
            int lod = -1;
            for(int lodIndex=0;lodIndex<m_slg_lod_layer.Length;lodIndex++)
            {
                if (cur <= m_slg_lod_layer[lodIndex])
                {
                    lod = lodIndex;
                    break;
                }
            }

            m_curLod = lod;
        }
        #endregion
    }
}

