using bc;
using System;
using System.Collections.Generic;

#if UNITY_EDITOR
    using UnityEditor;
#endif
using UnityEngine;
using Random = UnityEngine.Random;

namespace Vegetation
{
    public static class PoissonDiskSampling
    {
#if UNITY_EDITOR
        public static List<Vector2> GeneratePoints(float radius, Vector2 sampleRegionSize, int rejectionSamples = 30)
        {
            float cellSize = radius / Mathf.Sqrt(2);

            int[,] grid = new int[Mathf.CeilToInt(sampleRegionSize.x / cellSize), Mathf.CeilToInt(sampleRegionSize.y / cellSize)];
            List<Vector2> points = new List<Vector2>();
            List<Vector2> spawnPoints = new List<Vector2>();

            spawnPoints.Add(sampleRegionSize / 2);
            while (spawnPoints.Count > 0)
            {
                int spawnIndex = Random.Range(0, spawnPoints.Count);
                Vector2 spawnCenter = spawnPoints[spawnIndex];
                bool candidateAccepted = false;

                for (int i = 0; i < rejectionSamples; i++)
                {
                    float angle = Random.value * Mathf.PI * 2;
                    Vector2 direction = new Vector2(Mathf.Sin(angle), Mathf.Cos(angle));
                    Vector2 candidate = spawnCenter + direction * Random.Range(radius, 2 * radius);

                    if (IsValid(candidate, sampleRegionSize, cellSize, radius, points, grid))
                    {
                        points.Add(candidate);
                        spawnPoints.Add(candidate);
                        grid[(int)(candidate.x / cellSize), (int)(candidate.y / cellSize)] = points.Count;
                        candidateAccepted = true;
                        break;
                    }
                }

                if (!candidateAccepted)
                {
                    spawnPoints.RemoveAt(spawnIndex);
                }
            }

            return points;
        }

        static bool IsValid(Vector2 candidate, Vector2 sampleRegionSize, float cellSize, float radius, List<Vector2> points, int[,] grid)
        {
            if (candidate.x >= 0 && candidate.x < sampleRegionSize.x && candidate.y >= 0 && candidate.y < sampleRegionSize.y)
            {
                int cellX = (int)(candidate.x / cellSize);
                int cellY = (int)(candidate.y / cellSize);

                int searchStartX = Mathf.Max(0, cellX - 2);
                int searchEndX = Mathf.Min(cellX + 2, grid.GetLength(0) - 1);
                int searchStartY = Mathf.Max(0, cellY - 2);
                int searchEndY = Mathf.Min(cellY + 2, grid.GetLength(1) - 1);

                for (int x = searchStartX; x <= searchEndX; x++)
                {
                    for (int y = searchStartY; y <= searchEndY; y++)
                    {
                        int pointIndex = grid[x, y] - 1;
                        if (pointIndex != -1)
                        {
                            float sqrDst = (candidate - points[pointIndex]).sqrMagnitude;
                            if (sqrDst < radius * radius)
                            {
                                return false;
                            }
                        }
                    }
                }
                return true;
            }
            return false;
        }
#endif
    }

    public enum DebugLOD
    {
        LOD0,
        LOD1,
        LOD2
    }

    public class VegetationExporter : MonoBehaviour
    {
#if UNITY_EDITOR
        #region private field
        private VegetationExporterData m_exporterData;

        private Vector2 m_regionSize = new Vector2(0, 0);

        private bool m_debugView = false;

        private string m_folderPath;

        private DebugLOD m_debugLOD;

        private List<GameObject> m_treeGroups;

        InstanceData m_instanceData;

        private List<GameObject> m_debugViewGo;
        private bool m_exported = false;
        #endregion

        #region public properties
        public VegetationExporterData ExporterData
        {
            get
            {
                return m_exporterData;
            }
            set
            {
                m_exporterData = value;
            }
        }

        public bool DebugView
        {
            get
            {
                return m_debugView;
            }
            set
            {
                m_debugView = value;
            }
        }

        public string FolderPath
        {
            get
            {
                return m_folderPath;
            }
            set
            {
                m_folderPath = value;
            }
        }

        public DebugLOD P_DebugLOD
        {
            get
            {
                return m_debugLOD;
            }
            set
            {
                m_debugLOD = value;
            }
        }
        #endregion

        #region private method
        private List<Vector2> GenerateRandomPosLst(float density)
        {
            float baseRadius = 15.0f;
            float radius = baseRadius / Mathf.Sqrt(density);

            List<Vector2> points = PoissonDiskSampling.GeneratePoints(radius, m_regionSize);
            return points;
        }

        private int GetRandomIndex()
        {
            int totalWeight = 0;
            int[] weights = m_exporterData.TemplateWeights;

            int randomIndex = 0;
            for (int i = 0; i < weights.Length; i++)
            {
                totalWeight += weights[i];
            }

            int randomNumber = Random.Range(0, totalWeight);

            int cumulativeWeight = 0;
            for(int i =0;i<weights.Length;i++)
            {
                cumulativeWeight += weights[i];
                if(randomNumber < cumulativeWeight)
                {
                    randomIndex = i;
                    return randomIndex;
                }
            }
            return 0;
        }
        #endregion

        #region debug
        void DebugPosLst(List<Vector2> posLst,Vector2 minPoint, Vector2 maxPoint)
        {
            if(m_debugView)
            {
                GameObject ground =  GameObject.CreatePrimitive(PrimitiveType.Plane);
                m_debugViewGo.Add(ground);

                ground.name = "Ground";
                Vector2 center = (maxPoint + minPoint) / 2;

                ground.transform.position = new Vector3(center.x,0,center.y);
                ground.transform.localScale = new Vector3(m_regionSize.x/10,1,m_regionSize.y/10);
                ground.transform.localRotation = Quaternion.identity;
                var mr = ground.GetComponent<MeshRenderer>();
                if(mr!=null)
                {
                    mr.sharedMaterial = AssetDatabase.LoadAssetAtPath<Material>("Assets/Art/GreatWorld/Sand/Res/mat/bgMatZOff.mat");
                }
                Collider col =  ground.GetComponent<Collider>();
                if(col!=null)
                {
                    DestroyImmediate(col);
                }

                //GameObject pointRoot = new GameObject("Debug Point Root");
                //pointRoot.transform.position = Vector3.zero;
                //pointRoot.transform.rotation = Quaternion.identity;
                //pointRoot.transform.localScale = Vector3.one;
                //for (int posIndex=0;posIndex<posLst.Count;posIndex++)
                //{
                //    GameObject pointSphereView = GameObject.CreatePrimitive(PrimitiveType.Sphere);

                //    pointSphereView.transform.SetParent(pointRoot.transform);
                //    pointSphereView.transform.position = new Vector3(posLst[posIndex].x + minPoint.x, 0, posLst[posIndex].y + minPoint.y);
                //}
            }
        }

        GameObject GeneratePlantCommunity(GameObject[] templates,List<Vector2> posLst,Vector2 minPoint,Vector2 maxPoint)
        {
            GameObject pointRoot = new GameObject("Plant Root");
            m_debugViewGo.Add(pointRoot);

            pointRoot.transform.position = Vector3.zero;
            pointRoot.transform.rotation = Quaternion.identity;
            pointRoot.transform.localScale = Vector3.one;

            m_treeGroups = new List<GameObject>();
            for (int posIndex = 0; posIndex < posLst.Count; posIndex++)
            {
                int randomIndex = GetRandomIndex();//Random.Range(0, templates.Length);
                GameObject template = GameObject.Instantiate(templates[randomIndex]);
                m_treeGroups.Add(template);
                template.transform.SetParent(pointRoot.transform);
                template.transform.position = new Vector3(posLst[posIndex].x + minPoint.x, 0, posLst[posIndex].y + minPoint.y);
                
            }

            return pointRoot;
        }

        int GetChunkIndex(Vector2 pos,List<ChunkData> chunkDataLst,Vector3 chunkSize)
        {
            Vector2 chunkMinPoint;
            Vector2 chunkMaxPoint;
            for (int i = 0;i<chunkDataLst.Count;i++)
            {
                chunkMinPoint = new Vector2(chunkDataLst[i].m_chunkPosition.x, chunkDataLst[i].m_chunkPosition.y) - new Vector2(chunkSize.x, chunkSize.z) / 2;
                chunkMaxPoint = new Vector2(chunkDataLst[i].m_chunkPosition.x, chunkDataLst[i].m_chunkPosition.y) + new Vector2(chunkSize.x, chunkSize.z) / 2;

                if (pos.x>=chunkMinPoint.x&&pos.y>=chunkMinPoint.y&&pos.x<=chunkMaxPoint.x&&pos.y<= chunkMaxPoint.y)
                {
                    return i;
                }
            }

            return -1;
        }

        InstanceData CreateInstanceData(GameObject plantRoot,Vector2 minPoint, Vector2 maxPoint)
        {
            InstanceData instanceData = ScriptableObject.CreateInstance<InstanceData>();

            Vector2 mapSize = m_regionSize;
            Vector2 chunkSize = new Vector2(m_exporterData.ChunkSize, m_exporterData.ChunkSize);
            Vector2 cellNum = new Vector2(Mathf.Ceil(mapSize.x / chunkSize.x), Mathf.Ceil(mapSize.y / chunkSize.y));
            instanceData.m_chunkSize = new Vector3(chunkSize.x, 10, chunkSize.y);

            var mrs = plantRoot.GetComponentsInChildren<MeshRenderer>(true);
            List<Mesh> meshList = new List<Mesh>();
            List<Material> matList = new List<Material>();
            instanceData.m_meshLst = meshList;
            instanceData.m_matLst = matList;
            instanceData.m_lodDataLst = new List<LODData>();

            for (int mrIndex=0; mrIndex<mrs.Length;mrIndex++)
            {
                var mr =  mrs[mrIndex];
                var mf = mr.GetComponent<MeshFilter>();
                if(mf!=null)
                {
                   var mesh =  mf.sharedMesh;
                   if(mesh!=null)
                   {
                      if(!meshList.Contains(mesh))
                      {
                          meshList.Add(mesh);
                      }
                   }
                }

                var mat = mr.sharedMaterial;
                if(mat!=null)
                {
                    if(!matList.Contains(mat))
                    {
                        matList.Add(mat);
                    }
                }
                
            }

            List<ChunkData> m_lod0_chunkData = new List<ChunkData>();
            List<ChunkData> m_lod1_chunkData = new List<ChunkData>();
            List<ChunkData> m_lod2_chunkData = new List<ChunkData>();
            int cellIndex = 0;
            for (int x=0;x<cellNum.x;x++)
            {
                for(int y=0;y<cellNum.y;y++)
                {
                    int chunkIndex = cellIndex;

                    Vector2 chunk_loca_pos = chunkSize / 2;
                    Vector2 chunkMinPointWS = minPoint + new Vector2(x * chunkSize.x, y * chunkSize.y);
                    Vector2 chunkPosWS = chunk_loca_pos + chunkMinPointWS;

                    ChunkData chunkData = new ChunkData();
                    chunkData.m_chunkIndex = chunkIndex;
                    chunkData.m_chunkPosition = chunkPosWS;// new Vector3(chunkPosWS.x, 0, chunkPosWS.y);
                    chunkData.m_itemDataLst = new List<ItemData>();

                    ChunkData lod1_chunkData = new ChunkData();
                    lod1_chunkData.m_chunkIndex = chunkIndex;
                    lod1_chunkData.m_chunkPosition = chunkPosWS;// new Vector3(chunkPosWS.x, 0, chunkPosWS.y);
                    lod1_chunkData.m_itemDataLst = new List<ItemData>();

                    ChunkData lod2_chunkData = new ChunkData();
                    lod2_chunkData.m_chunkIndex = chunkIndex;
                    lod2_chunkData.m_chunkPosition = chunkPosWS;// new Vector3(chunkPosWS.x, 0, chunkPosWS.y);
                    lod2_chunkData.m_itemDataLst = new List<ItemData>();

                    m_lod0_chunkData.Add(chunkData);
                    m_lod1_chunkData.Add(lod1_chunkData);
                    m_lod2_chunkData.Add(lod2_chunkData);
                    cellIndex++;
                }
            }


            int lod0Count = 0;
            int lod1Count = 1;
            int lod2Count = 2;

            for (int mrIndex = 0; mrIndex < mrs.Length; mrIndex++)
            {
                var mr = mrs[mrIndex];
                var parent = mr.transform.parent;
                var mat = mr.sharedMaterial;
                var mf = mr.GetComponent<MeshFilter>();
                var mesh = mf.sharedMesh;

                if(parent==null || mat == null || mf ==null || mesh == null)
                {
                    continue;
                }

                ItemData m_itemData = new ItemData();
                Bounds bounds = mr.bounds;
                var meshIndex = instanceData.m_meshLst.IndexOf(mesh);
                var matIndex = instanceData.m_matLst.IndexOf(mat);
                m_itemData.m_posWS = new Vector2(bounds.center.x,bounds.center.z);

                if (m_itemData.m_posWS.x < minPoint.x || m_itemData.m_posWS.y < minPoint.y || m_itemData.m_posWS.x > maxPoint.x || m_itemData.m_posWS.y > maxPoint.y)
                {
                    continue;
                }

                m_itemData.m_size = bounds.size;
                m_itemData.m_meshIndex = meshIndex;
                m_itemData.m_matIndex = matIndex;
 
                Transform root = null;
                if(parent.parent!=null)
                {
                    root = parent.parent;
                }
                else
                {
                    root = parent;
                }

                switch (root.name)
                {
                    case "LOD0":
                        int chunk_0_Index = GetChunkIndex(new Vector2(mr.transform.position.x,mr.transform.position.z), m_lod0_chunkData,instanceData.m_chunkSize);
                        Debug.LogError("ChunKIndex:" + chunk_0_Index);
                        if (chunk_0_Index>=0)
                        {
                            m_lod0_chunkData[chunk_0_Index].m_itemDataLst.Add(m_itemData);
                            lod0Count++;
                        }                     
                        break;
                    case "LOD1":
                        int chunk_1_Index = GetChunkIndex(new Vector2(mr.transform.position.x, mr.transform.position.z), m_lod1_chunkData, instanceData.m_chunkSize);
                        Debug.LogError("ChunKIndex:" + chunk_1_Index);
                        if (chunk_1_Index>=0)
                        {
                            m_lod1_chunkData[chunk_1_Index].m_itemDataLst.Add(m_itemData);
                            lod1Count++;
                        }          
                        break;
                    case "LOD2":
                        int chunk_2_Index = GetChunkIndex(new Vector2(mr.transform.position.x, mr.transform.position.z), m_lod2_chunkData, instanceData.m_chunkSize);
                        Debug.LogError("ChunKIndex:" + chunk_2_Index);
                        if (chunk_2_Index>=0)
                        {
                            m_lod2_chunkData[chunk_2_Index].m_itemDataLst.Add(m_itemData);
                            lod2Count++;
                        }      
                        break;
                }
            }

            LODData m_lod_0_Data = new LODData();
            LODData m_lod_1_Data = new LODData();
            LODData m_lod_2_Data = new LODData();

            m_lod_0_Data.m_lodIndex = 0;
            m_lod_1_Data.m_lodIndex = 1;
            m_lod_2_Data.m_lodIndex = 2;

            m_lod_0_Data.m_chunkDataLst = m_lod0_chunkData;
            m_lod_1_Data.m_chunkDataLst = m_lod1_chunkData;
            m_lod_2_Data.m_chunkDataLst = m_lod2_chunkData;

            instanceData.m_lodDataLst.Add(m_lod_0_Data);
            instanceData.m_lodDataLst.Add(m_lod_1_Data);
            instanceData.m_lodDataLst.Add(m_lod_2_Data);

            return instanceData;
        }

        private void OnDrawGizmos()
        {
            string dataPath = m_folderPath + "InstanceData.asset";
            if (!UnityEngine.Windows. File.Exists(dataPath))
            {
                return;
            }

            var instanceData =  AssetDatabase.LoadAssetAtPath<InstanceData>(dataPath);
            int curLOD = 0;
            var lodData = instanceData.m_lodDataLst[curLOD];
            for(int chunkIndex=0;chunkIndex<lodData.m_chunkDataLst.Count;chunkIndex++)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(new Vector3(lodData.m_chunkDataLst[chunkIndex].m_chunkPosition.x, 0, lodData.m_chunkDataLst[chunkIndex].m_chunkPosition.y), instanceData.m_chunkSize);
            }
        }

        private void CreateInstanceDataAsset(InstanceData instanceData)
        {
            string dataPath = m_folderPath + "InstanceData.asset";
            if(UnityEngine.Windows.File.Exists(dataPath))
            {
                AssetDatabase.DeleteAsset(dataPath);
            }

            AssetDatabase.CreateAsset(instanceData, dataPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        #endregion

        #region public method
        public void Exporter()
        {
            if(m_exporterData==null)
            {
                EditorUtility.DisplayDialog("Error", "exporter data ==null", "ok");
                return;
            }

            m_debugViewGo = new List<GameObject>();

            Vector2 minPoint = new Vector2(m_exporterData.MinMaxPoint.x, m_exporterData.MinMaxPoint.y);
            Vector2 maxPoint = new Vector2(m_exporterData.MinMaxPoint.z, m_exporterData.MinMaxPoint.w);
            m_regionSize = maxPoint - minPoint;

            float density = m_exporterData.Density;
            List<Vector2> posLst = GenerateRandomPosLst(density);

            DebugPosLst(posLst, minPoint, maxPoint);

            var treeTemplates = m_exporterData.TreeTemplates;
            if(treeTemplates ==null || treeTemplates.Length<=0)
            {
                EditorUtility.DisplayDialog("Error", "Tree Templates Count <=0", "ok");
                return;
            }

            var plantRoot = GeneratePlantCommunity(treeTemplates, posLst, minPoint, maxPoint);
            m_instanceData =  CreateInstanceData(plantRoot, minPoint, maxPoint);
            CreateInstanceDataAsset(m_instanceData);
            m_exported = true;
        }

        public void DebugLODChange(DebugLOD lod)
        {
            if(m_treeGroups==null||m_treeGroups.Count<=0)
            {
                EditorUtility.DisplayDialog("Error", "Please generate tree groups", "OK");
                return;
            }
            switch(lod)
            {
                case DebugLOD.LOD0:
                    for(int treeGroupIndex=0;treeGroupIndex<m_treeGroups.Count; treeGroupIndex++)
                    {
                        var treeGroup = m_treeGroups[treeGroupIndex];
                        var lod0 = treeGroup.transform.GetChild(0);
                        var lod1 = treeGroup.transform.GetChild(1);
                        var lod2 = treeGroup.transform.GetChild(2);

                        if(lod0!=null)
                        {
                            lod0.gameObject.SetActive(true);
                        }
                        if(lod1!=null)
                        {
                            lod1.gameObject.SetActive(false);
                        }
                        if(lod2!=null)
                        {
                            lod2.gameObject.SetActive(false);
                        }
                    }
                    break;
                case DebugLOD.LOD1:
                    for (int treeGroupIndex = 0; treeGroupIndex < m_treeGroups.Count; treeGroupIndex++)
                    {
                        var treeGroup = m_treeGroups[treeGroupIndex];
                        var lod0 = treeGroup.transform.GetChild(0);
                        var lod1 = treeGroup.transform.GetChild(1);
                        var lod2 = treeGroup.transform.GetChild(2);

                        if (lod0 != null)
                        {
                            lod0.gameObject.SetActive(false);
                        }
                        if (lod1 != null)
                        {
                            lod1.gameObject.SetActive(true);
                        }
                        if (lod2 != null)
                        {
                            lod2.gameObject.SetActive(false);
                        }
                    }
                    break;
                case DebugLOD.LOD2:
                    for (int treeGroupIndex = 0; treeGroupIndex < m_treeGroups.Count; treeGroupIndex++)
                    {
                        var treeGroup = m_treeGroups[treeGroupIndex];
                        var lod0 = treeGroup.transform.GetChild(0);
                        var lod1 = treeGroup.transform.GetChild(1);
                        var lod2 = treeGroup.transform.GetChild(2);

                        if (lod0 != null)
                        {
                            lod0.gameObject.SetActive(false);
                        }
                        if (lod1 != null)
                        {
                            lod1.gameObject.SetActive(false);
                        }
                        if (lod2 != null)
                        {
                            lod2.gameObject.SetActive(true);
                        }
                    }
                    break;
            }
        }

        public void OnWindowCloseCallBack()
        {
            if (!m_exported)
                return;
            var instanceRender = FindObjectOfType<VegetationInstanceRender>();
            if(instanceRender==null)
            {
                GameObject instanceRenderGo = new GameObject("VegetationInstanceRender");
                instanceRender = instanceRenderGo.AddComponent<VegetationInstanceRender>();
            }
            string dataPath = m_folderPath + "InstanceData.asset";
            instanceRender.InstanceData = AssetDatabase.LoadAssetAtPath<InstanceData>(dataPath);

            if(m_debugViewGo!=null && m_debugViewGo.Count>0)
            {
                for (int debugViewGoIndex = 0; debugViewGoIndex < m_debugViewGo.Count; debugViewGoIndex++)
                {
                    DestroyImmediate(m_debugViewGo[debugViewGoIndex]);
                }
            }

        }
        #endregion
#endif
    }
}
