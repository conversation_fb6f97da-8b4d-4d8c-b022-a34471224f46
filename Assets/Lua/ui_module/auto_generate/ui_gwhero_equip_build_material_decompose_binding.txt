
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_gwhero_equip_build_material_decompose_binding")

UIPath = "ui/prefabs/gw/gw_heroprefab/heroequip/uigwheroequipbuild_materialdecompose.prefab"

WidgetTable ={
	btn_All = { path = "top/btnGroup/btn_All", type = Button, event_name = "OnBtnAllClickedProxy"},
	txt_All = { path = "top/btnGroup/btn_All/txt_All", type = Text, },
	btn_One = { path = "top/btnGroup/btn_One", type = Button, event_name = "OnBtnOneClickedProxy"},
	txt_One = { path = "top/btnGroup/btn_One/txt_One", type = Text, },
	txt_BreakDes = { path = "top/rft_NoMerge/txt_BreakDes", type = Text, },
	srt_EquipList = { path = "EquipList/Viewport/srt_EquipList", type = ScrollRectTable, },

}
