
local RectTransform = CS.UnityEngine.RectTransform
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local Button = CS.UnityEngine.UI.Button
local Animator = CS.UnityEngine.Animator


module("ui_gwhero_equip_upgrade_level_ex_binding")

UIPath = "ui/prefabs/gw/gw_heroprefab/heroequip/uigwheroequipupgradelevelex.prefab"

WidgetTable ={
	rtf_baseAttrItem = { path = "BaseAttrGrid/rtf_baseAttrItem", type = RectTransform, },
	rtf_exAttrItem = { path = "ExAttrGrid/rtf_exAttrItem", type = RectTransform, },
	rtf_CostGrid = { path = "rtf_CostGrid", type = RectTransform, },
	rtf_CostItem = { path = "rtf_CostGrid/rtf_CostItem", type = RectTransform, },
	rtf_TopMax = { path = "top/rtf_TopMax", type = RectTransform, },
	rtf_EquipItemMax = { path = "top/rtf_TopMax/rtf_EquipItemMax", type = RectTransform, },
	txt_TopMax = { path = "top/rtf_TopMax/txt_TopMax", type = Text, },
	rtf_TopNoMax = { path = "top/rtf_TopNoMax", type = RectTransform, },
	rtf_EquipItem1 = { path = "top/rtf_TopNoMax/rtf_EquipItem1", type = RectTransform, },
	rtf_EquipItem2 = { path = "top/rtf_TopNoMax/rtf_EquipItem2", type = RectTransform, },
	img_Slider1 = { path = "top/slider/img_Slider1", type = Image, },
	img_Slider2 = { path = "top/slider/img_Slider2", type = Image, },
	img_Slider3 = { path = "top/slider/img_Slider3", type = Image, },
	img_Mask = { path = "top/slider/img_Mask", type = Image, },
	btn_Up = { path = "btn_Up", type = Button, event_name = "OnBtnUpClickedProxy"},
	txt_BtnUp = { path = "btn_Up/txt_BtnUp", type = Text, },
	ator_Effect = { path = "panel/progress/ator_Effect", type = Animator, },
	rtf_MaskEffectOne = { path = "panel/progress/ator_Effect/rtf_MaskEffectOne", type = RectTransform, },
	rtf_MaskEffectFull = { path = "panel/progress/ator_Effect/rtf_MaskEffectFull", type = RectTransform, },

}
