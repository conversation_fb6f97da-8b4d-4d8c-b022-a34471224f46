local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local Toggle = CS.UnityEngine.UI.Toggle
local RectTransform = CS.UnityEngine.RectTransform


module("ui_miracle_box_open_tips_binding")

UIPath = "ui/prefabs/gw/activity/miracleboxactivity/uimiracleboxopentips.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_Title = { path = "Frame/txt_Title", type = Text, },
	txt_content = { path = "Frame/txt_content", type = Text, },
	btn_open = { path = "Frame/btn_open", type = Button, event_name = "OnBtnOpenClickedProxy"},
	img_key = { path = "Frame/btn_open/img_key", type = Image, },
	txt_curNum = { path = "Frame/btn_open/txt_curNum", type = Text, },
	tog_Toggle = { path = "Frame/toggleText/tog_Toggle", type = Toggle, value_changed_event = "OnTogToggleValueChange"},
	rtf_icon = { path = "Frame/rtf_icon", type = RectTransform, },
	img_box = { path = "Frame/img_box", type = Image, },

}
