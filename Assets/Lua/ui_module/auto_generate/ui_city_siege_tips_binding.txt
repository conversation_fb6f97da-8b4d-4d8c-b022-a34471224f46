local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text


module("ui_city_siege_tips_binding")

UIPath = "ui/prefabs/gw/activity/citysiegechallengeactivity/uicitysiegetips.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_title = { path = "Image/Flag/txt_title", type = Text, },
	txt_title_2 = { path = "Image/Flag/txt_title_2", type = Text, },
	btn_enterActivity = { path = "Image/btn_enterActivity", type = Button, event_name = "OnBtnEnterActivityClickedProxy"},

}
