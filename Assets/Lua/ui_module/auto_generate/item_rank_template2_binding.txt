
local GameObject = CS.UnityEngine.GameObject
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Text = CS.UnityEngine.UI.Text
local Transform = CS.UnityEngine.Transform
local Button = CS.UnityEngine.UI.Button


module("item_rank_template2_binding")

UIPath = "ui/prefabs/gw/activity/commonitem/item_rank_template2.prefab"

WidgetTable ={
	item_rank_template2 = { path = "", type = GameObject, },
	ssw_left_di = { path = "ssw_left_di", type = SpriteSwitcher, },
	ssw_rankIcon = { path = "ssw_rankIcon", type = SpriteSwitcher, },
	txt_ranking = { path = "txt_ranking", type = Text, },
	ssw_sexImage = { path = "roleInfo/ssw_sexImage", type = SpriteSwitcher, },
	txt_name = { path = "roleInfo/Txt/txt_name", type = Text, },
	txt_rankingContext = { path = "txt_rankingContext", type = Text, },
	tf_notInRank = { path = "tf_notInRank", type = Transform, },
	btn_bg = { path = "btn_bg", type = Button},
	tf_faceItem = { path = "tf_faceItem", type = Transform, },
	btn_detail = { path = "btn_detail", type = Button},
	tf_selfMark = { path = "tf_selfMark", type = Transform, },

}
