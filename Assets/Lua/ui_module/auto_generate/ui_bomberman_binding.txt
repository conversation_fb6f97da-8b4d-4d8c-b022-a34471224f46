
local RectTransform = CS.UnityEngine.RectTransform
local Canvas = CS.UnityEngine.Canvas
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local Animator = CS.UnityEngine.Animator
local Button = CS.UnityEngine.UI.Button


module("ui_bomberman_binding")

UIPath = "ui/prefabs/gw/gw_bomberman/uibomberman.prefab"

WidgetTable ={
	rtf_Prefab = { path = "rtf_Prefab", type = RectTransform, },
	canvas_UI = { path = "canvas_UI", type = Canvas, },
	rtf_UIParent = { path = "canvas_UI/rtf_UIParent", type = RectTransform, },
	txt_Time = { path = "canvas_UI/rtf_UIParent/txt_Time", type = Text, },
	txt_Des = { path = "canvas_UI/rtf_UIParent/GameObject/txt_Des", type = Text, },
	txt_Count = { path = "canvas_UI/rtf_UIParent/GameObject/txt_Count", type = Text, },
	img_Icon = { path = "canvas_UI/rtf_UIParent/GameObject/txt_Count/img_Icon", type = Image, },
	rtf_Pop = { path = "canvas_UI/rtf_Pop", type = RectTransform, },
	ator_Bubble = { path = "canvas_UI/rtf_Pop/ator_Bubble", type = Animator, },
	img_BomberPopBg = { path = "canvas_UI/rtf_Pop/ator_Bubble/parent/img_BomberPopBg", type = Image, },
	txt_BomberPop = { path = "canvas_UI/rtf_Pop/ator_Bubble/parent/img_BomberPopBg/txt_BomberPop", type = Text, },
	btn_Fire = { path = "canvas_UI/btn_Fire", type = Button, event_name = "OnBtnFireClickedProxy"},
	txt_Title = { path = "canvas_UI/btn_Fire/txt_Title", type = Text, },
	btn_return = { path = "canvas_UI/btn_return", type = Button, event_name = "OnBtnReturnClickedProxy"},

}
