
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Toggle = CS.UnityEngine.UI.Toggle


module("ui_defeat_activity_popup_binding")

UIPath = "ui/prefabs/gw/allianceduel/duelloginpopup/uidefeatactivitypopup.prefab"

WidgetTable ={
	back_closeBtn = { path = "back_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_hour = { path = "panel/TimeArea/txt_hour", type = Text, },
	txt_minute = { path = "panel/TimeArea/txt_minute", type = Text, },
	txt_second = { path = "panel/TimeArea/txt_second", type = Text, },
	btn_openDef = { path = "panel/BtnArea/btn_openDef", type = Button, event_name = "OnBtnOpenDefClickedProxy"},
	btn_goGather = { path = "panel/BtnArea/btn_goGather", type = Button, event_name = "OnBtnGoGatherClickedProxy"},
	tog_noTips = { path = "panel/BtnArea/tog_noTips", type = Toggle, value_changed_event = "OnTogNoTipsValueChange"},

}
