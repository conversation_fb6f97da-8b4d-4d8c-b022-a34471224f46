
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_gwhero_equip_build_break_binding")

UIPath = "ui/prefabs/gw/gw_heroprefab/heroequip/uigwheroequipbuild_break.prefab"

WidgetTable ={
	btn_SelectQuickly = { path = "top/btnGroup/btn_SelectQuickly", type = Button, event_name = "OnBtnSelectQuicklyClickedProxy"},
	btn_Break = { path = "top/btnGroup/btn_Break", type = Button, event_name = "OnBtnBreakClickedProxy"},
	txt_BreakDes = { path = "top/txt_BreakDes", type = Text, },
	rtf_Break = { path = "top/rtf_Break", type = RectTransform, },
	srt_RewardTable = { path = "top/rtf_Break/RewradList/Viewport/srt_RewardTable", type = ScrollRectTable, },
	txt_BreakReward = { path = "top/rtf_Break/txt_BreakReward", type = Text, },
	rtf_NoBreak = { path = "top/rtf_NoBreak", type = RectTransform, },
	txt_NoBreakReward = { path = "top/rtf_NoBreak/txt_NoBreakReward", type = Text, },
	srt_EquipList = { path = "EquipList/Viewport/srt_EquipList", type = ScrollRectTable, },
	txt_Select = { path = "SelectQuality/txt_Select", type = Text, },
	btn_Select = { path = "SelectQuality/btn_Select", type = Button, event_name = "OnBtnSelectClickedProxy"},

}
