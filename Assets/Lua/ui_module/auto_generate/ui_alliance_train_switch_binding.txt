
local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local Animator = CS.UnityEngine.Animator


module("ui_alliance_train_switch_binding")

UIPath = "ui/prefabs/gw/gw_alliancetrain/uialliancetrainswitch.prefab"

WidgetTable ={
	btn_LeftBtn = { path = "btn_LeftBtn", type = Button, event_name = "OnBtnLeftBtnClickedProxy"},
	rtf_teaminfoLeft = { path = "main/rtf_teaminfoLeft", type = RectTransform, },
	btn_switch1 = { path = "main/btn_switch1", type = Button, event_name = "OnBtnSwitch1ClickedProxy"},
	btn_switch2 = { path = "main/btn_switch2", type = Button, event_name = "OnBtnSwitch2ClickedProxy"},
	animator = { path = "main/rtf_teaminfoLeft", type = Animator, },

}
