local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Text = CS.UnityEngine.UI.Text


module("ui_tavern_change_confirm_binding")

UIPath = "ui/prefabs/gw/gw_tavern/uitavernchangeconfirm.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	scrItem_leftItem = { path = "Background/top/left/scrItem_leftItem", type = ScrollRectItem, },
	txt_leftName = { path = "Background/top/left/txt_leftName", type = Text, },
	scrItem_rightItem = { path = "Background/top/right/scrItem_rightItem", type = ScrollRectItem, },
	txt_rightName = { path = "Background/top/right/txt_rightName", type = Text, },
	txt_changeDesc = { path = "Background/txt_changeDesc", type = Text, },
	btn_change = { path = "Background/btn_change", type = Button, event_name = "OnBtnChangeClickedProxy"},

}
