
local Toggle = CS.UnityEngine.UI.Toggle
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local Button = CS.UnityEngine.UI.Button


module("ui_duel_rank_reward_list_binding")

UIPath = "ui/prefabs/gw/allianceduel/rankpanel/uiduelrankrewardlist.prefab"

WidgetTable ={
	txt_title = { path = "panelBg/top/txt_title", type = "Text", },
	tog_leagueToggle = { path = "panelBg/ToggleGroup/tog_leagueToggle", type = Toggle, value_changed_event = "OnTogLeagueToggleValueChange"},
	tog_rankToggle = { path = "panelBg/ToggleGroup/tog_rankToggle", type = Toggle, value_changed_event = "OnTogRankToggleValueChange"},
	tog_danToggle = { path = "panelBg/ToggleGroup/tog_danToggle", type = Toggle, value_changed_event = "OnTogDanToggleValueChange"},
	rtf_rankPanel = { path = "rtf_rankPanel", type = RectTransform, },
	rtf_danToggleGroup = { path = "rtf_rankPanel/rtf_danToggleGroup", type = RectTransform, },
	tog_week1 = { path = "rtf_rankPanel/rtf_danToggleGroup/tog_week1", type = Toggle, value_changed_event = "OnTogWeek1ValueChange"},
	tog_week2 = { path = "rtf_rankPanel/rtf_danToggleGroup/tog_week2", type = Toggle, value_changed_event = "OnTogWeek2ValueChange"},
	tog_week3 = { path = "rtf_rankPanel/rtf_danToggleGroup/tog_week3", type = Toggle, value_changed_event = "OnTogWeek3ValueChange"},
	rtf_pointsRankList = { path = "rtf_rankPanel/rtf_pointsRankList", type = RectTransform, },
	srt_rankContent = { path = "rtf_rankPanel/rtf_pointsRankList/Viewport/srt_rankContent", type = ScrollRectTable, },
	rtf_rewardParent = { path = "rtf_rankPanel/rtf_pointsRankList/Viewport/srt_rankContent/ListItem/rewardList/Viewport/rtf_rewardParent", type = RectTransform, },
	rtf_danPanel = { path = "rtf_danPanel", type = RectTransform, },
	rtf_danToggleGroup = { path = "rtf_danPanel/rtf_danToggleGroup", type = RectTransform, },
	tog_week1 = { path = "rtf_danPanel/rtf_danToggleGroup/tog_week1", type = Toggle, value_changed_event = "OnTogWeek1ValueChange"},
	tog_week2 = { path = "rtf_danPanel/rtf_danToggleGroup/tog_week2", type = Toggle, value_changed_event = "OnTogWeek2ValueChange"},
	tog_week3 = { path = "rtf_danPanel/rtf_danToggleGroup/tog_week3", type = Toggle, value_changed_event = "OnTogWeek3ValueChange"},
	rtf_danRewardList = { path = "rtf_danPanel/rtf_danRewardList", type = RectTransform, },
	srt_danContent = { path = "rtf_danPanel/rtf_danRewardList/Viewport/srt_rankContent", type = ScrollRectTable, },
	back_closeBtn = { path = "back_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},

}
