
local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local Text = CS.UnityEngine.UI.Text


module("ui_alliance_train_invite_binding")

UIPath = "ui/prefabs/gw/gw_alliancetrain/uialliancetraininvite.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	srt_ScrollRectTable = { path = "Panel/Content/ScrollRect/ViewPort/srt_ScrollRectTable", type = ScrollRectTable, },
	btn_Confirm = { path = "Panel/BottomBar/btn_Confirm", type = Button, event_name = "OnBtnConfirmClickedProxy"},
	txt_Title = { path = "Panel/BottomBar/btn_Confirm/txt_Title", type = Text, },

}
