
local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local Text = CS.UnityEngine.UI.Text


module("ui_duel_theme_popup_binding")

UIPath = "ui/prefabs/gw/allianceduel/warsituation/uiduelthemepopup.prefab"

WidgetTable ={
	back_closeBtn = { path = "back_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	rtf_duelingPanel = { path = "themePanel/rtf_duelingPanel", type = RectTransform, },
	srt_duelingScrTable = { path = "themePanel/rtf_duelingPanel/scienceBg/endedInfoList/Viewport/srt_endedScrTable", type = ScrollRectTable, },
	rtf_EndedPanel = { path = "themePanel/rtf_EndedPanel", type = RectTransform, },
	srt_endedScrTable = { path = "themePanel/rtf_EndedPanel/scienceBg/endedInfoList/Viewport/srt_endedScrTable", type = ScrollRectTable, },
	txt_result = { path = "themePanel/rtf_EndedPanel/resultArea/txt_result", type = Text, },
	txt_dayText = { path = "themePanel/topArea/txt_dayText", type = Text, },
	btn_helpBtn = { path = "themePanel/topArea/helpBtn", type = Button, event_name = "OnBtnHelpBtnClickedProxy"},

}
