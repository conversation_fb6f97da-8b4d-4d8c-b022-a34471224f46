
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local GameObject = CS.UnityEngine.GameObject
local Transform = CS.UnityEngine.Transform
local Toggle = CS.UnityEngine.UI.Toggle
local Button = CS.UnityEngine.UI.Button


module("ui_world_boss_activity_achievement_binding")

UIPath = "ui/prefabs/gw/activity/worldbossactivity/uiworldbossactivityachievement.prefab"

WidgetTable ={
	srt_content = { path = "panelBg/content/Viewport/srt_content", type = ScrollRectTable, },
	item_achievement_template1 = { path = "panelBg/content/Viewport/srt_content/item_achievement_template1", type = GameObject, },
	tf_TogleGroup = { path = "panelBg/content/tf_TogleGroup", type = Transform, },
	tog_1 = { path = "panelBg/content/tf_TogleGroup/tog_1", type = Toggle, value_changed_event = "OnTog1ValueChange"},
	tog_2 = { path = "panelBg/content/tf_TogleGroup/tog_2", type = Toggle, value_changed_event = "OnTog2ValueChange"},
	tog_3 = { path = "panelBg/content/tf_TogleGroup/tog_3", type = Toggle, value_changed_event = "OnTog3ValueChange"},
	btn_pop_btn_return = { path = "panelBg/content/btn_pop_btn_return", type = Button, event_name = "OnBtnPop_btn_returnClickedProxy"},
	btn_closeBtn = { path = "panelBg/closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},

}
