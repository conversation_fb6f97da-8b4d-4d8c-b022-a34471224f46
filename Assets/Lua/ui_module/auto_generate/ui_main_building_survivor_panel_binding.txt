local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image


module("ui_main_building_survivor_panel_binding")

UIPath = "ui/prefabs/gw/gw_survivor/uimainbuildingsurvivorpanel.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	scrItem_Item_1 = { path = "Panel/Content/SurvivorList/Viewport/content/scrItem_Item_1", type = ScrollRectItem, },
	scrItem_Item_2 = { path = "Panel/Content/SurvivorList/Viewport/content/scrItem_Item_2", type = ScrollRectItem, },
	txt_BuildingName = { path = "Panel/TopBar/txt_BuildingName", type = Text, },
	txt_BuildingLevel = { path = "Panel/TopBar/txt_BuildingLevel", type = Text, },
	img_BuildingIcon = { path = "Panel/TopBar/img_BuildingIcon", type = Image, },

}
