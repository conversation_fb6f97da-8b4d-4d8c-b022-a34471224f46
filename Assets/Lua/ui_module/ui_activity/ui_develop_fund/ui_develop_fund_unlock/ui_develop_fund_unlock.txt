local print = print
local require = require
local pairs = pairs
local type = type
local tostring = tostring

local reward_mgr = require "reward_mgr"
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local net_recharge_module = require "net_recharge_module"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local util = require "util"
local class = require "class"
local binding = require "ui_develop_fund_unlock_binding"

--region View Life
module("ui_develop_fund_unlock")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self.VData = {}

    self.srt_rewardTotal.onItemRender = function(scroll_rect_item, index, dataItem)
        self:OnItemRender(scroll_rect_item, index, dataItem, 11)
    end
    self.srt_rewardTotal.onItemDispose = function(...)
        self:OnItemDispose(...)
    end

    self.srt_rewardGet.onItemRender = function(scroll_rect_item, index, dataItem)
        self:OnItemRender(scroll_rect_item, index, dataItem, 21)
    end
    self.srt_rewardGet.onItemDispose = function(...)
        self:OnItemDispose(...)
    end
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    if self.srt_rewardTotal then
        self.srt_rewardTotal:OnItemsDispose()
        self.srt_rewardTotal.onItemRender = nil
        self.srt_rewardTotal.onItemDispose = nil 
    end
    if self.srt_rewardGet then
        self.srt_rewardGet:OnItemsDispose()
        self.srt_rewardGet.onItemRender = nil
        self.srt_rewardGet.onItemDispose = nil    
    end

    self.__base.Close(self)
end

function UIView:UpdateUI()
    if self:IsValid() then
        self.spm_effectTotalMask.frontSortingOrder = self.curOrder + 13
        self.spm_effectTotalMask.backSortingOrder = self.curOrder + 10

        self.spm_effectGetMask.frontSortingOrder = self.curOrder + 23
        self.spm_effectGetMask.backSortingOrder = self.curOrder + 20

        self.srt_rewardTotal:Refresh(-1, -1)
        self.srt_rewardGet:Refresh(-1, -1)
    end
end
--endregion

--region View Logic
function UIView:SetRechargeData(rechargeId)
    if rechargeId then
        local moneyStr, oldMoneyStr = net_recharge_module.GetMoneyStrByGoodsID(rechargeId)
        self.txt_prices.text = moneyStr

        local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
        gw_totalrecharge_mgr.SetRechargeScore(rechargeId, self.rtf_comRechargeScore, self)
        local game_scheme = require "game_scheme"
        local rechargeCfg = game_scheme:RechargeGoods_0(rechargeId)
        if rechargeCfg then
            if self.txt_giftValue then
                self.txt_giftValue.text = tostring(rechargeCfg.GiftValue) .. "%"
            end
        end
    end
end

function UIView:SetRewardList(totalRewards, getRewards)
    if totalRewards then
        self.srt_rewardTotal:SetData(totalRewards, #totalRewards)
        self.srt_rewardTotal:Refresh(-1, -1)
    end

    if getRewards and #getRewards > 0 then
        self.srt_rewardGet:SetData(getRewards, #getRewards)
        self.srt_rewardGet:Refresh(-1, -1)
    end

    util.YieldFrame(function()
        self.srt_rewardTotal:ScrollTo(0)
        self.srt_rewardGet:ScrollTo(0)
    end)
end

function UIView:OnItemRender(scroll_rect_item, index, dataItem, orderOffset)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    if not scroll_rect_item.data[3] then
        scroll_rect_item.data[3] = reward_mgr.GetRewardItemData(dataItem, scroll_rect_item.rectTransform, true, 0.7)
    else
        scroll_rect_item.data[3]:SetRewardData(dataItem.id, dataItem.num, dataItem.nType)
    end
    scroll_rect_item.data[3]:SetCustomEffectEnable(true, "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab", 1.5, self.curOrder + orderOffset, true, nil, true)
end

function UIView:OnItemDispose(scroll_rect_item, index)
    if scroll_rect_item and scroll_rect_item.data then
        if scroll_rect_item.data[3] then
            scroll_rect_item.data[3]:Dispose()
            scroll_rect_item.data[3] = nil
        end
    end
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, true, true, nil, nil, nil, true)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, true, true, nil, nil, nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
