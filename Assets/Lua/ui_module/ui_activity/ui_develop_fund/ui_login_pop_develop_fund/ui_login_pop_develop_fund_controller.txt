local print = print
local require = require
local pairs = pairs
local tonumber = tonumber
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local cfg_util = require "cfg_util"
local activity_cfg_util = require "activity_cfg_util"
local log = require "log"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_login_pop_develop_fund_controller")
local controller = nil
local UIController = newClass("ui_login_pop_develop_fund_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = data
    self:InitData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData and self.CData.isAddMessage and self.isClickClose then
        local gw_popups_mgr = require("gw_popups_mgr")
        gw_popups_mgr.StopPop()
    end
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnGoClickedProxy()
    self.isClickClose = true
    local festival_activity_mgr = require "festival_activity_mgr"
    if festival_activity_mgr.GetIsOpenByActivityID(self.CData.actId) then
        festival_activity_mgr.OpenActivityUIByActivityID(self.CData.actId)
    end
    self:OnBtnCloseClickedProxy()
end
function  UIController:OnBtnCloseClickedProxy()
    self:OnBtnCloseBtnClickedProxy()
end

function  UIController:InitData()
    local mgr_prosperity_fund = require "mgr_prosperity_fund"
    self.mgrData = mgr_prosperity_fund.GetProsperityFundData(self.CData.actId)
    if not self.mgrData then
        return
    end
    local atyCfg = self.mgrData:GetAtyCfg()
    local atyData = self.mgrData:GetAtyData()
    if atyCfg and atyData then
        local game_scheme = require "game_scheme"
        local mainCfg = game_scheme:ActivityMain_0(self.CData.actId)
        if mainCfg then
            local cfg = game_scheme:ActivityCommonUI_0(mainCfg.uiTemplateID)
            if cfg then
                local langList = {}
                if cfg.CustomParam3 then
                    langList = string.split(cfg.CustomParam3, "#",tonumber)
                end
                local itemList = {}
                local festivalActivityCfg = activity_cfg_util.GetFestivalActivityCfg(self.CData.actId)
                if festivalActivityCfg then
                    itemList = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID3)
                end
                local priceValue = 0
                local rechargeId = mgr_prosperity_fund.GetRechargeId(self.CData.actId)
                local rechargeCfg = game_scheme:RechargeGoods_0(rechargeId)
                if rechargeCfg then
                    priceValue = rechargeCfg.GiftValue
                end
                local allReward = mgr_prosperity_fund.GetAllRewardListAndFree(self.CData.actId)
                local showRewardList = {}
                local item_data = require "item_data"
                for k,v in ipairs(itemList) do
                    local item = {
                        id = tonumber(v),
                        nType = item_data.Reward_Type_Enum.Item,
                        num = mgr_prosperity_fund.GetGoodNumByTotalReward(self.CData.actId, v),
                    }
                    table.insert(showRewardList,item)
                end
                local reward_mgr = require "reward_mgr"
                table.sort(allReward, reward_mgr.sortRewardsTable)
                local festival_activity_mgr = require "festival_activity_mgr"
                local activityData = festival_activity_mgr.GetActivityDataByActivityID(self.CData.actId)
                local endTime = activityData and activityData.endTimeStamp or 0
                local data = {
                    titleName = cfg.title,
                    pic = cfg.CustomParam2,
                    langList = langList,
                    priceValue = priceValue,
                    showRewardList = allReward,
                    topRewardList = showRewardList,
                    endTime = endTime,
                }
                self:TriggerUIEvent("UpdateView",data)
            else
                log.Error("ui_login_pop_city_fund_controller 找不到ActivityCommonUI，activityId = ",self.CData.actId)
                self:OnBtnCloseBtnClickedProxy()
            end
        end
    end


end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
