local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local os = os
local UIUtil = CS.Common_Util.UIUtil

local reward_mgr = require "reward_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_login_pop_develop_fund_binding"

--region View Life
module("ui_login_pop_develop_fund")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local ShowEffect = "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab"

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
--[[    self.srt_rewardTotal.onItemRender = function(scroll_rect_item, index, dataItem)
        self:OnItemRender(scroll_rect_item, index, dataItem, 11)
    end]]

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
--[[    self.srt_rewardTotal:OnItemsDispose()
    self.srt_rewardTotal.onItemRender = nil
    self.srt_rewardTotal.onItemDispose = nil]]
    
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:UpdateView(data)
    if data.titleName then
        self.txt_Title.text = lang.Get(tonumber(data.titleName))
    end
    if data.langList then
        self.txt_Des.text = lang.Get(tonumber(data.langList[1]))
        self.txt_Des2.text = lang.Get(tonumber(data.langList[2]))
    end
    if data.priceValue then
        self.txt_giftValue.text = string.format("%d%%",data.priceValue)
    end
    if data.pic then
        self:CreateSubSprite("CreateGWLoginPop", self.img_Role, data.pic)
    end
    if data.showRewardList then
--[[        self.srt_rewardTotal:SetData(data.showRewardList, #data.showRewardList)
        self.srt_rewardTotal:Refresh(-1, -1)
        util.YieldFrame(function()
            self.srt_rewardTotal:ScrollTo(0)
        end)]]
        self:UpdateRewardList(data.showRewardList, self.rtf_rewardTotal.transform, 0.75)
    end

    if data.topRewardList then
        self:UpdateRewardList(data.topRewardList, self.rtf_rewardList.transform, 0.8)
    end

    if data.endTime and data.endTime > 0 then
        self:CreateTimer(1, function()
            if self.txt_LimitTimeText then
                local time = data.endTime - os.server_time()
                if time > 0 then
                    self.txt_LimitTimeText.text = string.format("%s%s",lang.Get(1003713), util.GetDayCountDown(time))       --活动时间：
                end
            end
        end)
    end
end

function UIView:UpdateRewardList(rewardsList, rtf, scale)
    self.VData.goodList = self.VData.goodList or {}
    self.VData.goodEffectList = self.VData.goodEffectList or {}
    for k,v in pairs(rewardsList or {}) do
        local item = reward_mgr.GetRewardItemData(v, rtf, true, scale)
        item:SetCustomEffectEnable(true, ShowEffect, 1.5, self.curOrder + 3, true, nil, true)
        table.insert(self.VData.goodList, item)
    end
end

function UIView:UpdateUI()
    if self:IsValid() then
        self.spm_effectTotalMask.frontSortingOrder = self.curOrder + 2
        self.spm_effectTotalMask.backSortingOrder = self.curOrder + 1

    end
    
end

--[[function UIView:OnItemRender(scroll_rect_item, index, dataItem, orderOffset)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    if not scroll_rect_item.data[3] then
        scroll_rect_item.data[3] = reward_mgr.GetRewardItemData(dataItem, scroll_rect_item.rectTransform, true, 0.75)
    else
        scroll_rect_item.data[3]:SetRewardData(dataItem.id, dataItem.num, dataItem.nType)
    end
    scroll_rect_item.data[3]:SetCustomEffectEnable(true, ShowEffect, 1.5, self.curOrder + orderOffset, true, nil, true)
end]]

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
