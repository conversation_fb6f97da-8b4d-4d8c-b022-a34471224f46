--新的活动中心沿用以前的 ui_festival_activity_base
local print = print
local require = require
local typeof = typeof
local table = table
local pairs = pairs
local ipairs = ipairs
local string = string
local type = type
local tostring = tostring
local math = math

local log = require "log"
local class = require "class"
local ui_base = require "ui_base"
local event = require "event"
local util = require "util"
local lang = require "lang"
local ui_window_mgr = require "ui_window_mgr"
local game_scheme = require "game_scheme"
local festival_activity_mgr = require "festival_activity_mgr"
local festival_activity_cfg = require "festival_activity_cfg"
local card_sprite_asset = require "card_sprite_asset"
local menu_bot_data = require "menu_bot_data"
local flow_text = require "flow_text"
local music_contorller = require "music_contorller"
local gw_event_activity_define = require "gw_event_activity_define"
local gw_activity_red_mgr = require "gw_activity_red_mgr"

local Canvas = CS.UnityEngine.Canvas
local GameObject = CS.UnityEngine.GameObject
local ToggleGroup = CS.UnityEngine.UI.ToggleGroup
local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local Transform = CS.UnityEngine.Transform
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local Application = CS.UnityEngine.Application
local LeanTween = CS.LeanTween
local LeanTweenType = CS.LeanTweenType
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Time = CS.UnityEngine.Time
local RawImage = CS.UnityEngine.UI.RawImage
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text

local ScrollRectItemType = typeof(ScrollRectItem)

module("ui_festival_activity_center")

--- 是否在编辑器内运行
local isEditor = Application.isEditor

---@class UIFestivalActivityCenter : UIBase
local M = {}

---@type UIFestivalActivityCenter
local _window = nil

--- 隐藏页签配置
local _hiddenToggleCfgIDs = nil
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 活动组（子活动归属的活动类型）
ActivityActType = festival_activity_cfg.ActivityActType

--- 活动组类型（ActivityActType）
local _actType = nil
--- 是否为新手活动
local _isNewbie = nil
local _atyEntrance = nil
local _customPara = nil
--- 新手活动类型（1、活动入口1；2、活动入口2）
local _newType = nil
local _curActivityID = nil
-- 春节活动 无底部按钮
local _isChineseNewYearActivity = nil
local _closeBtnCallBack = nil
local _lastAtyShowType = nil
local uiChineseNewYearEntranceShowCallBack = nil
local _cacheLastShowPara = {}

local SideType = {
    None = 0,
    Left = 1,
    Right = 2
}
local isSide = SideType.None

local _cacheRedNum = {}
local showRedType = {
    Default = 0,
    New = 1,
    Weak = 2
}
--- 设置活动组类型
---@param actType number 活动组类型（ActivityActType）
function SetActType(actType)
    _actType = actType
end

function GetActType()
    return _actType
end
---@param entranceId number 入口id
function SetEntranceId(entranceId)
    _atyEntrance = entranceId
end
---@param param table 自定义参数
function SetCustomPara(param)
    _customPara = param
end
function GetEntranceId()
    return _atyEntrance
end
--- 获取当前选中的活动id
function GetCurActivityID()
    return _curActivityID 
end
--- 设置是否为新手活动
---@param isNewbie boolean 是否为新手活动
function SetIsNewbie(isNewbie)
    _isNewbie = isNewbie
end

--- 设置新手活动类型
---@param newType number 新手活动类型（1、活动入口1 2、活动入口2）
function SetNewType(newType)
    _newType = newType
end

--- 设置活动组参数
---@param actType number 活动组类型（ActivityActType）
---@param isNewbie boolean 是否为新手活动
---@param newType number 新手活动类型
----@param atyEntrance number 入口id
----@param param table 自定义参数
function SetInputParam(actType, isNewbie, newType, atyEntrance,param)
    SetActType(actType)
    SetIsNewbie(isNewbie)
    SetNewType(newType)
    SetEntranceId(atyEntrance)
    SetCustomPara(param)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 初始模块活动ID
local _initialModuleActivityID = nil
--- 初始模块活动类型
local _initialModuleCodeType = nil
--- 初始模块脚本名称
local _initialModuleName = nil

--- 设置初始模块活动ID
---@param activityID number 活动ID
function SetInitialModuleActivityID(activityID)
    if isEditor and _window then
        log.Error("[周活动] 请在打开界面之前设置初始模块！")
    end
    _initialModuleActivityID = activityID
end

--- 设置初始模块活动类型
---@param codeType number 活动类型（festival_activity_cfg.ActivityCodeType）
function SetInitialModuleCodeType(codeType)
    if isEditor and _window then
        log.Error("[周活动] 请在打开界面之前设置初始模块！")
    end
    _initialModuleCodeType = codeType
end

--- 设置初始模块脚本名称
---@param moduleName string 模块脚本名称
function SetInitialModuleName(moduleName)
    if isEditor and _window then
        log.Error("[周活动] 请在打开界面之前设置初始模块！")
    end
    _initialModuleName = moduleName
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

M.widget_table = {
    w_sortingGroup = { path = "", type = SortingGroup },
    w_Panel = { path = "Panel", type = Transform },
    w_Content = { path = "Panel/Content", type = Transform },
    w_Bg = { path = "Panel/Bg", type = RawImage },
    
    -- 关闭按钮
    w_closeBtn = { path = "Panel/BottomBar/closeBtn", type = Button, backEvent = true, event_name = "closeBtnEvent" },
    img_liu_hai = { path = "Panel/img_liu_hai", type = Image },
    -- 底栏
    w_bottomBar = { path = "Panel/BottomBar", type = RectTransform },
    w_bottomBarCanvas = { path = "Panel/BottomBar", type = Canvas },

    -- 底栏滚动页签
    w_scrollRect = { path = "Panel/BottomBar/Scroll", type = ScrollRect },
    w_scrollRectTrans = { path = "Panel/BottomBar/Scroll", type = RectTransform },
    -- 滚动页签容器
    w_scrollContentTrans = { path = "Panel/BottomBar/Scroll/Viewport/Content", type = RectTransform },
    w_toggleGroup = { path = "Panel/BottomBar/Scroll/Viewport/Content", type = ToggleGroup },
    -- 页签模板
    w_toggleTemplate = { path = "Panel/BottomBar/Scroll/Viewport/Content/Toggle", type = RectTransform },
    -- 底栏滑动提示
    w_scrollRightTipBtn = { path = "Panel/BottomBar/Scroll/RightTipBtn", type = Button, event_name = "scrollRightTipBtnEvent" },
    w_scrollLeftTipBtn = { path = "Panel/BottomBar/Scroll/LeftTipBtn", type = Button, event_name = "scrollLeftTipBtnEvent" },

    -- 活动内容
    w_contentTrans = { path = "Panel/Content", type = RectTransform },

    -- 左红点提示 （视窗之外的左边区域存在多少个红点）
    w_leftRedTipBtn = { path = "Panel/BottomBar/Scroll/LeftRedTipBtn",type = RectTransform },
    -- 右红点提示 （视窗之外的右边区域存在多少个红点）
    w_rightRedTipBtn = { path = "Panel/BottomBar/Scroll/RightRedTipBtn",type = RectTransform },
    -- 左红点数量
    w_leftRedTipNum = { path = "Panel/BottomBar/Scroll/LeftRedTipBtn/Text", type = Text},
    -- 右红点数量
    w_rightRedTipNum = { path = "Panel/BottomBar/Scroll/RightRedTipBtn/Text", type = Text},
}

function M:ctor(selfType)
    self.__base:ctor(selfType)
    self.matchHomeIndicator = false
    self.toggleInfoNum = 0
end

--- 获取模块优先级
---@param topicID number
---@param priority number
---@return number
local function GetPriority(topicID, priority)
    local activityData = festival_activity_mgr.GetFestivalActivityDataByTopicID(topicID)
    if (not activityData) then
        return priority
    end
    if (activityData.activityCodeType == festival_activity_cfg.ActivityCodeType.WeekCard) then
        -- 周卡买过，优先级设为最低，排在后面
        if festival_activity_mgr.GetWeekCardBuyTimes(topicID) ~= 0 then
            return 9999
        end
    end
    return priority
end

--- 生成页签信息
---@param uiTemplateID number
---@param activityCommonUICfg table
---@param activityData table
---@param activityCfg table
---@param associated table
---@param priority number
---@return table
local function GenToggleInfo(uiTemplateID, activityCommonUICfg, activityData, activityCfg, associated, priority)
    activityCommonUICfg = activityCommonUICfg or game_scheme:ActivityCommonUI_0(uiTemplateID)
    if (not activityCommonUICfg) then
        log.Error(activityData.activityID, "未找到活动模板：", uiTemplateID)
        return nil
    end

    local headingCode = activityCfg.headingCode
    local topicID = activityData.topicID
    local activityID = activityData.activityID
    return {
        -- 活动信息
        name = lang.Get(activityCommonUICfg.toggleText),
        activityID = activityID,
        headingCode = headingCode,
        versionNumber = activityCfg.versionNumber,
        activityCodeType = activityCfg.codeType,
        topicID = topicID,
        uiTemplateID = uiTemplateID,
        uiTemplatePrefab = (not string.IsNullOrEmpty(activityCommonUICfg.uiPath)) and activityCommonUICfg.uiPath or nil,
        -- 入口
        toggleIcon = activityCommonUICfg.toggleCheckmarkIcon, --不配置默认选中底图 --待修改
        toggleText = activityCommonUICfg.toggleText,
        -- 优先级
        priority = GetPriority(topicID, priority),
        -- 模块
        uiModule = activityCommonUICfg.uiTemplate,
        inputParam = { activityID, uiTemplateID, associated, topicID },
        -- 红点规则
        type = activityCommonUICfg.toggle, --by  bxz  红点规则暂时废弃，需要新接入新的红点系统
        -- 绑定活动
        bindTopicID = activityData.bindTopicID,
        parentsCode = activityData.parentsCode,
        parentsVersion = activityData.parentsVersion,
        -- 配置
        activityCfg = activityCfg,
        activityCommonUICfg = activityCommonUICfg,
        -- 显示分类(用于春节活动)
        atyShowType = activityCommonUICfg.atyShowType  --by bxz 春节活动的入口规则字段直接删除
    }
end

--- 初始化
function M:Init()
    local st = Time.realtimeSinceStartup
    print("LD: check festival center init", st)
    music_contorller.SaveLastBG()
    event.Trigger(event.HALL_SCENE_SILDING, false)

    self.gwActivityCommonSpriteAsset = card_sprite_asset.CreateCardSpriteAsset("gwActivityCommon")
    --直接用预设上的透明度
    --local bgColor = self.w_Bg.color
    --bgColor.a = 0
    --self.w_Bg.color = bgColor
    self:SetActive(self.w_Content,false)
    self:SetActive(self.img_liu_hai,false)
    -- 初始化页签数据
    self:InitToggleInfos()

    if isEditor then
        local infoJsonString = util.toJsonString(self.toggleInfos, true, function(reason, value, state, defaultMessage)
            return "", nil
        end)
        log.Warning("[周活动][仅编辑器打印] 页签数量：" .. tostring(self.toggleInfoNum) .. ", 页签数据：" .. infoJsonString)
    end

    -- 生成页签开关
    --[[self:GenToggles()

    -- 更新底栏提示
    self:UpdateScrollTips()--]]

    -- 加载初始模块
    util.DelayCallOnce(
        0.001,
        function()            
            if self:IsValid() then
                self:LoadInitialModule()                
            end
        end
    )

    self:SubscribeEvents()
    local et = Time.realtimeSinceStartup
    print("LD: check festival center init last", et,et - st)
end

--- 加载初始模块
function M:LoadInitialModule()
    if (self.toggleInfoNum == 0) then
        flow_text.Add(lang.Get(15649))
        --log.Error("[周活动] 没有活动数据！")
        return
    end
    --self:SetActive(self.w_Panel,false)
    local initSubmoduleShowFunc =  function()      
        if not util.IsObjNull(self.w_Content) then
            self:SetActive(self.w_Content,true)
            if self.curModuleActivityID then
                self:TurnOnToggle(self.curModuleActivityID)
            end
        end
    end
    -- 外部打开
    if _initialModuleActivityID then
        -- 活动ID
        self:SwitchSubmoduleByActivityID(_initialModuleActivityID,initSubmoduleShowFunc,_customPara)
    elseif _cacheLastShowPara and _atyEntrance and _cacheLastShowPara[_atyEntrance] then
        self:SwitchSubmoduleByActivityID(_cacheLastShowPara[_atyEntrance],initSubmoduleShowFunc,_customPara)
    elseif _initialModuleCodeType then
        -- 活动类型
        self:SwitchSubmoduleByCodeType(_initialModuleCodeType,initSubmoduleShowFunc,_customPara)
    elseif _initialModuleName then
        -- 活动模块脚本名称
        self:SwitchSubmoduleByName(_initialModuleName,initSubmoduleShowFunc,_customPara)
    end
    -- 默认打开第一个模块
    if (not self.curModuleName) then
        self:SwitchSubmoduleByIndex(1,initSubmoduleShowFunc,_customPara)
    end   
end

--- 初始化页签数据
function M:InitToggleInfos()
    -- 需要隐藏的开关
    if (not _hiddenToggleCfgIDs) then
        _hiddenToggleCfgIDs = {}
        local hideToggleCfg = game_scheme:InitBattleProp_0(1412)
        if hideToggleCfg and hideToggleCfg.szParam.data then
            for i = 0, (hideToggleCfg.szParam.count - 1) do
                local cfgID = hideToggleCfg.szParam.data[i]
                _hiddenToggleCfgIDs[cfgID] = true
            end
        end
    end

    -- 添加页签数据
    self.toggleInfos = {}
    self.toggleInfoNum = 0
    local tryAddToggleInfo = function(atyCfgID, info)
        if _hiddenToggleCfgIDs[atyCfgID] then
            return
        end

        -- 基地跨服状态只显示配置中的活动
        local main_slg_mgr = require "main_slg_mgr"
        local state = main_slg_mgr.GetCrossServerState()
        local showActivity = false
        if state == 1 or state == 3 then
            if festival_activity_cfg.CrossServerActivity[atyCfgID] then
                showActivity = true
            end
        else
            showActivity = true
        end
        if showActivity == false then
            return
        end

        self.toggleInfoNum = self.toggleInfoNum + 1
        self.toggleInfos[self.toggleInfoNum] = info
    end

    -- 是否为运营活动
    local isOperationActivity = (_actType == ActivityActType.Operation)
    -- 运营活动外部指定活动组
    local operationActivityGroup = nil
    if isOperationActivity and _initialModuleActivityID then
        local cfg = festival_activity_cfg.GetActivityCfgByAtyID(_initialModuleActivityID)
        operationActivityGroup = cfg and cfg.priorityGroup or nil
    end

    -- 遍历当前开启的活动    
    --local activityDatas, associated = festival_activity_mgr.GetAllActivity(_actType, _isNewbie, _newType)    原本通过活动组来区分的，现在直接通过活动入口来区分
    local activityDatas, associated = festival_activity_mgr.GetAllActivityByEntrance(_atyEntrance)
    for _, activityData in pairs(activityDatas) do
        local activityCfg = festival_activity_mgr.GetActivityCfgByActivityID(activityData.activityID, _isNewbie)
        -- 20 类型为 春节活动 无底部按钮
        if not _isChineseNewYearActivity and activityCfg and activityCfg.type == 20 then
            _isChineseNewYearActivity = true
        end
        local uiTemplateID = activityCfg.uiTemplateID
        local activityCommonUICfg = game_scheme:ActivityCommonUI_0(uiTemplateID)
        if (not activityCommonUICfg) then
            log.Error(activityData.activityID, "活动在ActivityCommonUI.csv未找到模板配置：", uiTemplateID)
        else
            -- 没有指定模块脚本不生成页签
            if activityCommonUICfg and (not string.empty(activityCommonUICfg.uiTemplate)) then
                local isValid = true
                -- 运营活动特殊处理，外部打开指定活动的情况下，只显示同一入口（priorityGroup）的活动
                if (isOperationActivity and operationActivityGroup ~= nil and activityCfg.priorityGroup ~= operationActivityGroup) then
                    isValid = false
                end
                -- 添加页签数据
                if isValid then
                    --注意 优先级读取已修改成 ActivityMain表中的Seqencing
                    local toggleInfo = GenToggleInfo(uiTemplateID, activityCommonUICfg, activityData, activityCfg, associated, activityCfg.Seqencing)
                    if toggleInfo then
                        tryAddToggleInfo(activityCfg.cfgID, toggleInfo)
                    end
                end
            else
                if isEditor then
                    log.Error("[周活动][仅编辑器打印] 活动未指定模块脚本！| uiTemplateID: " .. tostring(uiTemplateID))
                end
            end
        end
    end
    --遍历活动页签，如果存在相同的headingcode，做一个特殊记号
    self:MarkSameHeadingCodeTabs()
    -- 活动页签排序
    table.sort(self.toggleInfos, function(arg1, arg2)
        if arg1.priority < arg2.priority then
            return true
        elseif arg1.priority == arg2.priority then
            return arg1.activityID < arg2.activityID
        else
            return false
        end
    end)   
    -- 插入活动前瞻的页签数据
    self:InsertWarmUpToggleInfo()
    -- 春节入口显示
    self:ShowModuleChineseYearActivity()
end

function M:MarkSameHeadingCodeTabs()
    local headingCodeMap = {}
    -- 第一次遍历：收集所有headingCode信息
    for i, toggleInfo in ipairs(self.toggleInfos) do
        local headingCode = toggleInfo.headingCode
        if headingCode then
            if not headingCodeMap[headingCode] then
                headingCodeMap[headingCode] = {count = 0, indices = {}}
            end
            headingCodeMap[headingCode].count = headingCodeMap[headingCode].count + 1
            table.insert(headingCodeMap[headingCode].indices, i)
        end
    end
    -- 第二次遍历：标记具有相同headingCode的页签
    for headingCode, info in pairs(headingCodeMap) do
        if info.count > 1 then
            -- 有多个页签使用相同的headingCode
            for _, index in ipairs(info.indices) do
                -- 给这些页签添加特殊标记
                self.toggleInfos[index].isSameHeadingCode = true
            end
        end
    end
end

--- 插入活动前瞻的页签数据
function M:InsertWarmUpToggleInfo()
    if _isNewbie then
        return
    end
    local activity_mgr = require "activity_mgr"
    if (not activity_mgr.IsWarmUpOpem(self.toggleInfos)) then
        return
    end
    -- 页签数据
    local info = {
        isWarmUpModule = true,
        activityID = 0,
        headingCode = 28,
        versionNumber = 1,
        uiModule = "ui_activity_warm_up",
        inputParam = { 99999, 0 },
        type = 999,
    }
    -- 插入到页签列表中
    if activity_mgr.GetWarmUpState() then
        -- 已领取奖励，放到最后面
        self.toggleInfos[self.toggleInfoNum + 1] = info
    else
        -- 未领取奖励，放在最前面
        table.insert(self.toggleInfos, 1, info)
    end
    self.toggleInfoNum = self.toggleInfoNum + 1
end

--- 生成页签开关
function M:GenToggles()
    self.toggleObjs = {}

    local templateGo = self.w_toggleTemplate.gameObject
    templateGo:SetActive(false)

    local toggleGroup = self.w_toggleGroup
    local toggleGroupTrans = toggleGroup.transform

    for i, v in ipairs(self.toggleInfos) do
        local go = GameObject.Instantiate(templateGo)
        local trans = go.transform
        trans:SetParent(toggleGroupTrans)
        trans.localScale = { x = 1, y = 1, z = 1 }
        trans.localPosition = { x = 0, y = 0, z = 0 }
        go:SetActive(true)

        local item = go:GetComponent(ScrollRectItemType)
        local toggle = item:Get("Toggle")
        local iconBgImg = item:Get("IconBgImg")
        local iconCheckImg = item:Get("IconCheckImg")
        local titleOffText = item:Get("OffText")
        local titleOnText = item:Get("OnText")
        gw_activity_red_mgr.RegisterActivityTabRed(v.activityID)       
        --检测活动红点类型
        local typePath ,offsetPos = gw_activity_red_mgr.GetActivityTabRedPath(v.activityID)        
        self:BindUIRed(trans, gw_activity_red_mgr.ConcatTabRedType(v.activityID), { v.activityID },{ pos = offsetPos,redPath = typePath})        
        toggle.group = toggleGroup
        toggle.onValueChanged:RemoveAllListeners()
        toggle.onValueChanged:AddListener(function(isOn)
            if isOn then
                self:SetLocalScale(iconBgImg,1,1,1)
            else
                self:SetLocalScale(iconBgImg,0.85,0.85,0.85)                
            end
        end)
        if v.isWarmUpModule then
            -- 标题文本
            titleOffText.text = lang.Get(80189)
            titleOnText.text = lang.Get(80189)
            -- 图标
            self:SetSpriteFromFestivalSpriteAsset("huodongQZ", iconBgImg)
            self:SetSpriteFromFestivalSpriteAsset("huodongQZ_S", iconCheckImg)
            -- 保存引用
            self.warmUpToggle = toggle
        elseif v.isChineseYearBoxModule then
            -- 标题文本
            titleOffText.text = lang.Get(422114)
            titleOnText.text = lang.Get(422114)
            -- 图标
            self:SetSpriteFromFestivalSpriteAsset("huodongQZ", iconBgImg)
            self:SetSpriteFromFestivalSpriteAsset("huodongQZ_S", iconCheckImg)
        else
            -- 标题文本
            titleOffText.text = lang.Get(v.toggleText)
            titleOnText.text = lang.Get(v.toggleText)
            -- 图标
            if (not string.empty(v.toggleIcon)) then
                self:SetSpriteFromFestivalSpriteAsset(v.toggleIcon, iconBgImg)
            end
            iconCheckImg.enabled = true
        end

        -- 页签对象
        local toggleObj = {
            toggleInfo = v,
            toggle = toggle,
            titleOffText = titleOffText,
            titleOnText = titleOnText,
        }
        self.toggleObjs[i] = toggleObj

        -- 页签开关回调
        local toggleCallback = function(isOn)
            if isOn and (self.curModuleActivityID ~= v.activityID) then
                self:SwitchSubmodule(v)
            end
            self:UpdateToggleStyle(toggleObj, isOn)
        end
        toggle.onValueChanged:AddListener(toggleCallback)

        -- 初始化样式
        self:UpdateToggleStyle(toggleObj, toggle.isOn)
    end

    LayoutRebuilder.ForceRebuildLayoutImmediate(toggleGroupTrans)
end
function SetChineseNewYearEntranceShowCallBack(uiEntranceShowCallBack)
    uiChineseNewYearEntranceShowCallBack = uiEntranceShowCallBack
end

--- 显示春节活动主界面
function M:ShowModuleChineseYearActivity()
    -- if not _isChineseNewYearActivity then
    --     return
    -- end

    -- local ui_Chinese_new_year_entrance = require "ui_Chinese_new_year_entrance"
    -- if not ui_window_mgr:IsModuleShown("ui_Chinese_new_year_entrance") then
    --     local Chinese_new_year_mgr = require "Chinese_new_year_mgr"
    --     Chinese_new_year_mgr.InitFesActData(self.toggleInfos)

    --     ui_Chinese_new_year_entrance.SetToggleData(self.toggleInfos)
    --     ui_window_mgr:ShowModule("ui_Chinese_new_year_entrance", function()
    --         if uiChineseNewYearEntranceShowCallBack then
    --             local temp = uiChineseNewYearEntranceShowCallBack
    --             uiChineseNewYearEntranceShowCallBack = nil
    --             temp()
    --         end
    --     end)
    -- else
    --     ui_Chinese_new_year_entrance.SetCurVisibility(true)
    --     --ui_Chinese_new_year_entrance.Show()
    --     if uiChineseNewYearEntranceShowCallBack then
    --         local temp = uiChineseNewYearEntranceShowCallBack
    --         uiChineseNewYearEntranceShowCallBack = nil
    --         temp()
    --     end
    -- end

    -- event.Trigger(event.SHOW_MODULE_CHINESE_YEAR_ACTIVITY)
end

--- 根据atyShowType显示页签开关
--- preOpenSubName需要提前打开的界面
function ShowToggleByAtyShowType(atyShowType, preOpenSubName)
    if not _window or not _window:IsValid() then
        return
    end
    if not atyShowType then
        return
    end
    -- if atyShowType == _lastAtyShowType then
    --     return
    -- end
    -- _lastAtyShowType = atyShowType
    local isOpenFirstTog = true
    local atyShowTypeActivityOpen = false
    local nowOnToggle = nil

    for i, v in ipairs(_window.toggleObjs) do
        -- print("qsy_cj:[ui_festival_activity_center]ShowToggleByAtyShowType>>>>", atyShowType,v.toggleInfo.atyShowType ,preOpenSubName)
        if (v.toggleInfo.atyShowType == atyShowType) then
            v.toggle.gameObject:SetActive(true)
            if (preOpenSubName and (v.toggleInfo.uiModule == preOpenSubName)) or isOpenFirstTog then
                nowOnToggle = v.toggle
                isOpenFirstTog = false
            end
            atyShowTypeActivityOpen = true
        else
            v.toggle.gameObject:SetActive(false)
            v.toggle.isOn = false
        end
    end
    if nowOnToggle then
        nowOnToggle.isOn = true
    end
    return atyShowTypeActivityOpen
end
--根据subName子模块名显示页签开关类型组
function M:ShowToggleBySubName(subName)
    -- if not self or not self:IsValid() then
    --     return
    -- end
    -- for i, v in ipairs(self.toggleObjs) do
    --     -- print("qsy_cj:[ui_festival_activity_center]ShowToggleByAtyShowType>>>>",v.toggleInfo.atyShowType , atyShowType)
    --     if (v.toggleInfo.uiModule == subName) then
    --         if ShowToggleByAtyShowType(v.toggleInfo.atyShowType, subName) then
    --             local ui_Chinese_new_year_entrance = require "ui_Chinese_new_year_entrance"
    --             ui_Chinese_new_year_entrance.SetCurVisibility(false)
    --         end
    --         return
    --     end
    -- end
end

function SetCloseBtnCallBack(closeBtnCallBack)
    _closeBtnCallBack = closeBtnCallBack
end

--- 加载图像
---@param name string
---@param image "Image"
function M:SetSpriteFromFestivalSpriteAsset(name, image)
    self.gwActivityCommonSpriteAsset:GetSprite(name, function(sprite)
        if (not self:IsValid()) or util.IsObjNull(image) then
            return
        end
        image.sprite = sprite
        image.enabled = true
        image:SetNativeSize()
    end)
end

--- 更新页签样式
---@param toggleObj table
---@param isOn boolean
function M:UpdateToggleStyle(toggleObj, isOn)
    toggleObj.titleOffText.gameObject:SetActive(not isOn)
    toggleObj.titleOnText.gameObject:SetActive(isOn)
end

--- 更新预热页签
function M:UpdateWarmUpToggle()
    if (not self.warmUpToggle) then
        return
    end
    local activity_mgr = require "activity_mgr"
    if activity_mgr.GetWarmUpState() then
        self.warmUpToggle.gameObject.transform:SetAsLastSibling()
    else
        self.warmUpToggle.gameObject.transform:SetAsFirstSibling()
    end
end


--- 确保页签开关可见
---@param rectTransform "RectTransform"
function M:EnsureToggleVisible(rectTransform)
    local viewportWidth = self.w_scrollRect.viewport.rect.width
    local contentPos = self.w_scrollRect.content.anchoredPosition

    local elementLeft = rectTransform.anchoredPosition.x
    local elementRight = elementLeft + rectTransform.rect.width

    local visibleLeft = -contentPos.x
    local visibleRight = -contentPos.x + viewportWidth

    local padding = -15
    if (elementLeft > (visibleLeft + padding)) and (elementRight < (visibleRight - padding)) then
        return
    end

    local scrollDelta = 0
    if (elementLeft < visibleLeft) then
        scrollDelta = visibleLeft - elementLeft
    elseif (elementRight > visibleRight) then
        scrollDelta = visibleRight - elementRight
    end

    contentPos.x = contentPos.x + scrollDelta
    self.w_scrollRect.content.anchoredPosition = contentPos
end


--- 刷新底部页签滑动提示
function M:UpdateScrollTips(flag)
    local viewportTrans = self.w_scrollRect.viewport
    local viewportWidth = viewportTrans.rect.width

    local contentTrans = self.w_scrollContentTrans
    local contentWidth = contentTrans.sizeDelta.x
    local contentPosX = contentTrans.anchoredPosition.x

    local padding = -15
    local showLeft, showRight = false, false
    if (contentWidth + (padding * 2) > viewportWidth) then
        local threshold = 100
        if (contentPosX < -(contentWidth - viewportWidth) + threshold) then
            showLeft = true
        else
            showRight = true
        end
    end

    --获取所有红点
    local allNum = 0
    if #_cacheRedNum > 0 then 
        for i,v in pairs(_cacheRedNum) do 
            allNum = v.num + allNum
        end
    else
        for i,v in pairs(self.toggleObjs) do 
            local id=v.toggleInfo.activityID
            local num,redtype = gw_activity_red_mgr.GetActivityCenterTabRed(id)
            table.insert(_cacheRedNum,{id = id,num = num,type = redtype == showRedType.New and showRedType.New or showRedType.Default })
            allNum = num + allNum
        end
    end
    -- log.Warning("UpdateScrollTips 111",#_cacheRedNum)
    if allNum == 0 or #_cacheRedNum < 5 then 
        self.w_scrollLeftTipBtn.gameObject:SetActive(showLeft)
        self.w_scrollRightTipBtn.gameObject:SetActive(showRight)
        self.w_leftRedTipBtn.gameObject:SetActive(false)
        self.w_rightRedTipBtn.gameObject:SetActive(false)
        return
    end

    local ShowLeftRedTip = false
    local ShowRightRedTip = false

    local leftNum,rightNum=self:RenderLeftAndRightRedNum()
    if leftNum > 0 then 
        ShowLeftRedTip = true
    end
    if rightNum > 0 then 
        ShowRightRedTip = true
    end
    
    isSide = SideType.None
    local position = self.w_scrollRect.horizontalNormalizedPosition
    if position >= 1 then 
        isSide = SideType.Right
        self.w_rightRedTipBtn.gameObject:SetActive(false)
        self.w_leftRedTipBtn.gameObject:SetActive(ShowLeftRedTip)
    end
    if position <= 0 then 
        isSide = SideType.Left
        self.w_leftRedTipBtn.gameObject:SetActive(false)
        self.w_rightRedTipBtn.gameObject:SetActive(ShowRightRedTip)
    end    
    if flag then 
        self.w_leftRedTipBtn.gameObject:SetActive(ShowLeftRedTip)
        self.w_rightRedTipBtn.gameObject:SetActive(ShowRightRedTip)
    end
    if isSide == SideType.None then 
        self.w_leftRedTipBtn.gameObject:SetActive(ShowLeftRedTip)
        self.w_rightRedTipBtn.gameObject:SetActive(ShowRightRedTip)
    end
    self.w_leftRedTipNum.text = tostring(leftNum) 
    self.w_rightRedTipNum.text = tostring(rightNum)
end

function M:RenderLeftAndRightRedNum()
    local leftRedNum = 0
    local rightRedNum = 0
    local viewportRect = self.w_scrollRect.viewport
    local contentRect = self.w_scrollRect.content
    local contentPosition = contentRect.anchoredPosition
    local allActivityData=_cacheRedNum

    --计算视窗左边框到Content容器中的左边框的距离
    local contentLeft = contentPosition.x - (contentRect.rect.width * contentRect.pivot.x)
    -- local leftHideObjNum = math.floor(math.abs(contentLeft)/140)  --计算左测有多少个未显示对象
    
    --计算视窗右边框到Content容器中的有边框距离
    local contentRight = contentPosition.x + (contentRect.rect.width * (1 - contentRect.pivot.x))-viewportRect.rect.width
    -- local rightHidObjNum = math.floor(math.abs(contentRight+110)/140)--计算右测有多少个未显示对象
    local length = #allActivityData
    
    --IsNewActivity
    --获取左右两侧，View区域是处于Content容器中的哪个元素上的索引
    local curRightIndex = contentRight % 140 ~= 0 and math.floor(contentRight / 140 + 1)  or math.floor( contentRight / 140)
    local curLeftIndex = 0
    if contentLeft < -10 and  math.abs(contentLeft) / 140 ~= 0 and math.abs(contentLeft) % 140 ~= 0 then 
        curLeftIndex = math.floor(math.abs(contentLeft) / 140 + 1)
    else
        curLeftIndex=math.floor( math.abs(contentLeft) / 140)
    end
    -- log.Warning("RenderLeftAndRightRedNum 111",contentRight,curRightIndex)
    -- log.Warning("RenderLeftAndRightRedNum 222",contentLeft,curLeftIndex)
    local realcurRightIndex = length
    if curRightIndex > 1 then 
        realcurRightIndex = math.floor(length-curRightIndex + 1)
    end
    --根据当前索引，计算红点个数
    if curLeftIndex > 1 then 
        for i = 1,curLeftIndex-1 do 
            leftRedNum =  allActivityData[i].num + leftRedNum
        end
        -- local isNew=gw_activity_red_mgr.IsNewActivity(allActivityData[curLeftIndex].id)
        if allActivityData[curLeftIndex].type == showRedType.New then 
            if math.abs(contentLeft) % 140 >= 80 then 
                leftRedNum = allActivityData[curLeftIndex].num + leftRedNum
            end
        else
            if math.abs(contentLeft) % 140 >= 120 then 
                leftRedNum = allActivityData[curLeftIndex].num + leftRedNum
            end
        end
        -- log.Warning("RenderLeftAndRightRedNum 999 isNew",isNew == true and 1 or 0)
    else
        if curLeftIndex == 1 then 
            -- local isNew=gw_activity_red_mgr.IsNewActivity(allActivityData[curLeftIndex].id)
            if allActivityData[curLeftIndex].type == showRedType.New then 
                if math.abs(contentLeft) % 140 <= 80 then 
                    leftRedNum = allActivityData[curLeftIndex].num + leftRedNum
                end
            else
                if math.abs(contentLeft) % 140 >= 120 then 
                    leftRedNum = allActivityData[curLeftIndex].num + leftRedNum
                end
            end
            -- log.Warning("RenderLeftAndRightRedNum 101010 isNew",isNew == true and 1 or 0)
        end
    end

    if realcurRightIndex ~= length then 
        if curRightIndex == 0 then 
            return leftRedNum,rightRedNum
        end
        -- log.Warning("RenderLeftAndRightRedNum 666",realcurRightIndex)
        for i = length, realcurRightIndex+1 , -1 do 
            rightRedNum = allActivityData[i].num + rightRedNum
        end
        -- local isNew=gw_activity_red_mgr.IsNewActivity(allActivityData[realcurRightIndex].id)
        if allActivityData[realcurRightIndex].type == showRedType.New then 
            if math.abs(contentRight) % 140 >= 85 then 
                rightRedNum = allActivityData[realcurRightIndex].num + rightRedNum
            end
        else
            if math.abs(contentRight) % 140 >= 28 then 
                rightRedNum = allActivityData[realcurRightIndex].num + rightRedNum
            end
        end
        -- log.Warning("RenderLeftAndRightRedNum 888 isNew",isNew == true and 1 or 0)
    else
        -- local isNew=gw_activity_red_mgr.IsNewActivity(allActivityData[realcurRightIndex].id)
        if allActivityData[realcurRightIndex].type == showRedType.New then 
            if math.abs(contentRight) % 140 >= 85 then 
                rightRedNum = allActivityData[realcurRightIndex].num + rightRedNum
            end
        else
            if math.abs(contentRight) % 140 >= 28 then 
                rightRedNum = allActivityData[realcurRightIndex].num + rightRedNum
            end
        end
        -- log.Warning("RenderLeftAndRightRedNum 777 isNew",isNew == true and 1 or 0)
    end
    
    -- log.Warning("RenderLeftAndRightRedNum 333",leftRedNum,rightRedNum)
    return leftRedNum,rightRedNum
end

function M:RefreshRedTips(actvityID)
    -- local index = 0
    for i,v in pairs(_cacheRedNum) do 
        if v.id == actvityID then 
            local newNum,newType = gw_activity_red_mgr.GetActivityCenterTabRed(actvityID)
            v.num = newNum
            v.type = newType
            -- log.Warning("RefreshRedTips",actvityID,newNum,newType)
            -- index = i
            break
        end
    end
    -- log.Warning("RefreshRedTips 222",_cacheRedNum[index].id,_cacheRedNum[index].num,_cacheRedNum[index].type)
end

function M:OnPreShowOnce()
    -- 生成页签开关
    self:GenToggles()
    local first = true
    -- 更新底栏提示
    self:UpdateScrollTips(first)
end

function M:OnShow()

end

--- UI层级更新
function M:UpdateUI()
    local upperSortingOrder = self.curOrder + 100
    self.w_bottomBarCanvas.sortingOrder = upperSortingOrder
    event.Trigger(event.UPDATE_FESTIVAL_BASE_UI_ORDER, self.curOrder, upperSortingOrder)
end

--- 更新顶部物品栏
function M:UpdateTopItems()
    local toggleInfo = self.curModuleToggleInfo
    if (not toggleInfo) then
        return
    end
    -- 获取活动配置
    local activityCfg = nil
    if toggleInfo.parentsCode and toggleInfo.parentsVersion then
        activityCfg = festival_activity_mgr.GetActivityCfgByAtyID(toggleInfo.activityID, _isNewbie)
    else
        activityCfg = toggleInfo.activityCfg
    end   
end

--- 打开页签开关
---@param activityID number 活动ID
function M:TurnOnToggle(activityID)   
    if (not self.toggleObjs) then
        return
    end
    for i, v in ipairs(self.toggleObjs) do
        if (v.toggleInfo.activityID == activityID) then
            v.toggle.isOn = true           
            self:EnsureToggleVisible(v.toggle.transform)
            self:UpdateScrollTips()
            break
        end
    end
end

function M:HideModule(uiModuleName)
    local m = require(uiModuleName)
    if m~=nil and m.Hide then
        m["Hide"]()
    end
end

--- 切换子模块
---@param toggleInfo table
---@param showCallback function
---@param param table 参数
function M:SwitchSubmodule(toggleInfo, showCallback,param)
    if self.curModuleToggleInfo then
        local preDetailID = self.curModuleToggleInfo and self.curModuleToggleInfo.uiTemplateID or 0
        local curDetailID = toggleInfo.uiTemplateID
        event.Trigger(event.FESTIVAL_GAME_EVENT_REPORT, 3, preDetailID, curDetailID) --页签切换打点
    end

    local moduleName = toggleInfo.uiModule
    local moduleParam = toggleInfo.inputParam
    local curToggleIsSameHeadingCode = self.curModuleToggleInfo and self.curModuleToggleInfo.isSameHeadingCode

    -- 记录
    self.curModuleToggleInfo = toggleInfo
    
    --记录缓存
    if _atyEntrance then
        --不记录超值和商城
        if _atyEntrance ~= festival_activity_cfg.ActivityEntranceType.Mall and _atyEntrance ~= festival_activity_cfg.ActivityEntranceType.Benefits then
            _cacheLastShowPara[_atyEntrance] = toggleInfo.activityID            
        end
    end

    -- 卸载当前模块
    local moduleToBeUnload = nil
    if self.curModuleName and (self.curModuleActivityID ~= toggleInfo.activityID) then
        moduleToBeUnload = self.curModuleName
        -- avoid unload module with same name
        --module名字一样，但是activityID不一样，又或者存在其他相同headingcode的tab
        if moduleToBeUnload == moduleName or curToggleIsSameHeadingCode == true then
            --ui_window_mgr:HideModule(self.curModuleName)
            --self:HideModule(self.curModuleName)
            ui_window_mgr:UnloadModuleImmediate(self.curModuleName,nil,{isActivityCenterSwitchTab = true })
            moduleToBeUnload = nil
        end
    end
    self.curModuleActivityID = toggleInfo.activityID
    self.curModuleName = moduleName

    -- 获取模块
    local contentModule = require(moduleName)
    -- 模块传参
    if contentModule.SetInputParam_2 then
        contentModule.SetInputParam_2(moduleParam, _isNewbie)
    end
    if contentModule.SetParentModule then
        contentModule.SetParentModule("ui_festival_activity_center")
    end
    --if contentModule.SetParentModule then
    --    contentModule.SetParentModule("ui_festival_activity_center")
    --end
    -- 创建模块实例
    local uiData = { activityID = self.curModuleActivityID, --传入当前活动ID
                     uiPath = toggleInfo.uiTemplatePrefab, --传入当前模板预设ActivityCommonUI.csv中uiPath对应的配置
                     uiParent = self.w_contentTrans, --绑定ui挂载节点
                     isNewbie = _isNewbie,   --预留，是否是新手
                     param = param
    }
    ----先关闭刘海背景 
    --self:SetActive(self.img_liu_hai,false)
    --这里会直接打开ActivityCommonUI.csv中配置的uiTemplate对应的moduleName，如果uiPath有配置，则重新指定该ui的预设（实现同一套逻辑执行不同预设）                
    --local win = ui_window_mgr:ShowModule(moduleName, showCallback, nil, uiData)
    local win = ui_window_mgr:ShowModule(moduleName, function()
        --弱红点设置
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_SET_WEAK_RED, self.curModuleActivityID,0)
        self:SetActive(self.img_liu_hai,true)
        if showCallback then
            showCallback()
        end
        if moduleToBeUnload then
            --ui_window_mgr:HideModule(moduleToBeUnload)
            self:HideModule(moduleToBeUnload)
            --ui_window_mgr:UnloadModuleImmediate(moduleToBeUnload,nil,{isActivityCenterSwitchTab = true })
        end
    end, nil, uiData)
    if win then
        -- 父模块名称
        win.parentModule = self._NAME
    end
    --检测是否有活动结束--当前不在活动变化时检测，是因为活动结束时，如果打开的正是结束的活，是不能隐藏关闭的
    --检测活动是否开放
    self:CheckActivityState()
    --不同页面播放不同bgm
    if moduleParam[2] then
        local commonUICfg = game_scheme:ActivityCommonUI_0(moduleParam[2])
        if commonUICfg and commonUICfg.bgm and commonUICfg.bgm ~= 0 then
            music_contorller.ChangeSceneMusic(commonUICfg.bgm)
        else
            if music_contorller.GetLastBG() ~= 0 then
                music_contorller.ChangeSceneMusic(music_contorller.GetLastBG())
            end
        end
    end
    if  util.IsObjNull(self.w_Panel) then
        -- 更新页签开关
        self:TurnOnToggle(toggleInfo.activityID)        
    end
    _curActivityID = self.curModuleActivityID   
end

--- 切换子模块
---@param toggleIndex number 页签下标
---@param showCallback function 展示回调
----@param param table param
function M:SwitchSubmoduleByIndex(toggleIndex, showCallback,param)
    if (toggleIndex < 1) or (toggleIndex > self.toggleInfoNum) then
        log.Error("[周活动] 超出下标范围！| pageIndex: " .. tostring(toggleIndex))
        return false
    end
    local toggleInfo = self.toggleInfos[toggleIndex]
    self:SwitchSubmodule(toggleInfo, showCallback,param)
    return true
end

--- 切换子模块
---@param key string
---@param value any
---@param showCallback function 展示回调
----@param param table param
function M:SwitchSubmoduleByInfoKey(key, value, showCallback,param)
    if (not self.toggleInfos) or (key == nil) or (value == nil) then
        return false
    end
    for i, v in ipairs(self.toggleInfos) do
        if (v[key] == value) then
            self:SwitchSubmodule(v, showCallback,param)
            return true
        end
    end
    return false
end

--- 切换子模块
---@param activityID number 活动ID
---@param showCallback function 展示回调
---@param param table param
function M:SwitchSubmoduleByActivityID(activityID, showCallback,param)
    return self:SwitchSubmoduleByInfoKey("activityID", activityID, showCallback,param)
end

--- 切换子模块
---@param headingCode number headingCode
---@param showCallback function 展示回调
---@param param table param
function M:SwitchSubmoduleByHeadingCode(headingCode, showCallback,param)
    return self:SwitchSubmoduleByInfoKey("headingCode", headingCode, showCallback,param)
end

--- 切换子模块
---@param moduleName string 模块名称
---@param showCallback function 展示回调
---@param param table param
function M:SwitchSubmoduleByName(moduleName, showCallback,param)
    return self:SwitchSubmoduleByInfoKey("uiModule", moduleName, showCallback,param)
end

--- 切换子模块
---@param codeType number 活动类型
---@param showCallback function 展示回调
---@param param table param
function M:SwitchSubmoduleByCodeType(codeType, showCallback,param)
    return self:SwitchSubmoduleByInfoKey("activityCodeType", codeType, showCallback,param)
end

--- 是否有子模块
---@param moduleName string 模块脚本名称
function M:HasSubmodule(moduleName)
    if (not self.toggleInfos) then
        return false
    end
    for i, v in ipairs(self.toggleInfos) do
        if (v.uiModule == moduleName) then
            return true
        end
    end
    return false
end
---@public 检测活动状态
---@des  如果某个页签对应的活动结束了，则隐藏该页签（当如果是当前页签，则仍然显示）
function M:CheckActivityState()    
    if (not self.toggleObjs) then
        return false
    end
    local isOpen = false
    for i, v in ipairs(self.toggleObjs) do
        isOpen = festival_activity_mgr.GetIsOpenByActivityID(v.toggleInfo.activityID,true)
        isOpen = self.curModuleActivityID == v.toggleInfo.activityID and true or isOpen
        self:SetActive(v.toggle, isOpen)        
    end
    return false
end

function M:Close(data)
    if (not self:IsValid()) then
        return
    end
    self:UnsubscribeEvents()
    --屏蔽关闭时候的回调
    --self:OnCheckCallBackFun(_customPara,1,_atyEntrance,self.curModuleActivityID)
    --if self.curModuleName then
    --    ui_window_mgr:UnloadModuleImmediate(self.curModuleName,nil,data)
    --    self.curModuleName = nil
    --end
    self.curModuleName = nil
    for i, v in ipairs(self.toggleInfos) do
        ui_window_mgr:UnloadModuleImmediate(v.uiModule,nil,data)
    end

    if self.topBarUI then
        self.topBarUI:Dispose()
        self.topBarUI = nil
    end

    self.curModuleToggleInfo = nil
    self.curModuleActivityID = nil
    self.toggleInfos = nil
    self.warmUpToggle = nil

    if self.toggleObjs then
        for k, v in ipairs(self.toggleObjs) do
            GameObject.Destroy(v.toggle.gameObject)
        end
        self.toggleObjs = nil
    end

    if self.gwActivityCommonSpriteAsset then
        self.gwActivityCommonSpriteAsset:Dispose()
        self.gwActivityCommonSpriteAsset = nil
    end

    _actType = nil
    _isNewbie = nil
    _newType = nil
    _atyEntrance = nil
    _curActivityID = nil
    _customPara = nil
    _initialModuleActivityID = nil
    _initialModuleCodeType = nil
    _initialModuleName = nil

    _isChineseNewYearActivity = nil
    _closeBtnCallBack = nil
    _lastAtyShowType = nil
    uiChineseNewYearEntranceShowCallBack = nil

    _cacheRedNum = {}

    event.Trigger(event.FESTIVAL_ACTIVITY_BASE_CLOSE)
    event.Trigger(event.HALL_SCENE_SILDING, true)

    self.__base:Close()
    local isCloseBtn = data and data.isCloseBtn
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_CENTER_CLOSE,isCloseBtn)
end
---@public 监测回调时间
---@param closeFromType number 关闭来源类型 0表示关闭按钮 1表示ui结束了
---@param entranceId number 传入的入口ID
---@param activityID number 传入的活动ID
function M:OnCheckCallBackFun(param,closeFromType, entranceId,activityID)
    if not param then
        return        
    end
    if not closeFromType then
        return
    end
    if closeFromType == 0 then
        if param.activityCenterBackBtnFun then           
            util.DelayCallOnce(0,function()
                if param.activityCenterBackBtnFun then
                    param.activityCenterBackBtnFun(entranceId,activityID)                    
                end
            end)
        end        
    elseif closeFromType == 1 then
        if param.activityCenterUICloseFun then          
            util.DelayCallOnce(0,function()
                if param.activityCenterUICloseFun then
                    param.activityCenterUICloseFun(entranceId,activityID)                    
                end
            end)
        end
    end
end

function GetCurActType()
    return _actType
end

function M:SubscribeEvents()
    -- 关闭按钮
    self.closeBtnEvent = function()
        self:OnCheckCallBackFun(_customPara,0,_atyEntrance,self.curModuleActivityID)
        ui_window_mgr:UnloadModule("ui_festival_activity_center",nil,{isCloseBtn = true})
    end
    -- 底栏提示箭头
    self.scrollRightTipBtnEvent = function()
        local targetX = (self.w_scrollRectTrans.rect.width - self.w_scrollContentTrans.rect.width)
        LeanTween.moveLocalX(self.w_scrollContentTrans.gameObject, targetX, 1)
                 :setEase(LeanTweenType.easeInOutQuart)
                 :setOnComplete(function()
            if (not self:IsValid()) then
                return
            end
            self:UpdateScrollTips()
        end)
    end

    -- 底栏提示箭头
    self.scrollLeftTipBtnEvent = function()
        local targetX = 0
        LeanTween.moveLocalX(self.w_scrollContentTrans.gameObject, targetX, 1)
                 :setEase(LeanTweenType.easeInOutQuart)
                 :setOnComplete(function()
            if (not self:IsValid()) then
                return
            end
            self:UpdateScrollTips()
        end)
    end

    -- 底栏滑动事件
    local scrollTimes = 0
    self.onScrollRectValueChanged = function()
        scrollTimes = scrollTimes + 1
        if scrollTimes > 10 then
            self:UpdateScrollTips()
            scrollTimes = 0
        end
    end
    self.w_scrollRect.onValueChanged:AddListener(self.onScrollRectValueChanged)


    self.onChangeToggleEvent = function(eventName, index)
        if (not self:IsValid()) then
            return
        end
        self:SwitchSubmoduleByIndex(index)
    end
    self:RegisterEvent(event.FESTIVAL_CHANGE_TAG, self.onChangeToggleEvent)

    self.onFestivalBaseSortingGroupControl = function(eventName, enabled)
        if (not self:IsValid()) then
            return
        end
        self.w_sortingGroup.enabled = enabled
    end
    self:RegisterEvent(event.FESTIVAL_BASE_SORTING_GROUP_CONTROL, self.onFestivalBaseSortingGroupControl)
    self.onGWHaveActivitiesUpdate = function(eventName)
        self:CheckActivityState()
    end
    self:RegisterEvent(gw_event_activity_define.GW_HAVE_ACTIVITIES_UPDATE, self.onGWHaveActivitiesUpdate)
    self.OnRefreshRedTips=function(eventName,actvityID)
       self:RefreshRedTips(actvityID)
    end
    self:RegisterEvent(gw_event_activity_define.GW_ACTIVITY_REFRESH_RED_TIPS,self.OnRefreshRedTips)
end

function M:UnsubscribeEvents()
    self.w_scrollRect.onValueChanged:RemoveAllListeners()
end

function M:GetToggleInfos()
    return self.toggleInfos
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

local CreateInstance = class(ui_base, nil, M)

function Show()
    if _window == nil then
        _window = CreateInstance()
        _window._NAME = _NAME
        _window._noBg = true
        _window:LoadUIResource("ui/prefabs/gw/activity/uifestivalactivitycenter.prefab", nil, nil, nil, nil, true, nil, nil, nil, true)
    end
    _window:Show()
    return _window
end

function Hide()
    if _window ~= nil then
        _window:Hide()
    end
end

function Close(data)
    if _window ~= nil then
        _window:Close(data)
        _window = nil
    end
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 打开模块
---@param moduleName string 模块脚本名称
---@param showCallback function 展示回调
function OpenByModuleName(moduleName, showCallback)
    if ui_window_mgr:IsModuleShown("ui_festival_activity_center") and _window and not ui_window_mgr:IsModuleShown("ui_Chinese_new_year_entrance") then
        -- 切换页签
        _window:SwitchSubmoduleByName(moduleName, showCallback)
    elseif ui_window_mgr:IsModuleShown("ui_festival_activity_center") and _window and ui_window_mgr:IsModuleShown("ui_Chinese_new_year_entrance") then
        -- 春节活动 ，只显示一样atyShowType的页签
        _window:ShowToggleBySubName(moduleName)
    else
        -- 设置初始模块
        SetInitialModuleName(moduleName)
        SetChineseNewYearEntranceShowCallBack(function()
            if moduleName then
                _window:ShowToggleBySubName(moduleName)
            end
        end)
        -- 打开活动
        ui_window_mgr:ShowModule("ui_festival_activity_center", function()
            -- 关闭其他页面
            menu_bot_data.CloseAllPage()
            -- 回调
            if showCallback then
                showCallback()
            end
        end)
    end
end

--- 打开模块
---@param codeType number 活动类型（festival_activity_cfg.ActivityCodeType）
---@param showCallback function 展示回调
function OpenByActivityCodeType(codeType, showCallback)
    if ui_window_mgr:IsModuleShown("ui_festival_activity_center") and _window then
        -- 切换页签
        _window:SwitchSubmoduleByCodeType(codeType, showCallback)
    else
        -- 设置初始模块
        SetInitialModuleCodeType(codeType)
        -- 打开活动
        ui_window_mgr:ShowModule("ui_festival_activity_center", function()
            -- 关闭其他页面
            menu_bot_data.CloseAllPage()
            -- 回调
            if showCallback then
                showCallback()
            end
        end)
    end
end

--- 打开模块
---@param activityID number 活动ID
---@param showCallback function 展示回调
function OpenByActivityID(activityID, showCallback)
    if ui_window_mgr:IsModuleShown("ui_festival_activity_center") and _window then
        -- 切换页签
        _window:SwitchSubmoduleByActivityID(activityID, showCallback)
    else
        -- 设置初始模块
        SetInitialModuleActivityID(activityID)
        -- 打开活动
        ui_window_mgr:ShowModule("ui_festival_activity_center", function()
            -- 关闭其他页面
            menu_bot_data.CloseAllPage()
            -- 回调
            if showCallback then
                showCallback()
            end
        end)
    end
end

--- 打开模块（已废弃）
---@param headingCode number headingCode
---@param showCallback function 展示回调
function OpenByHeadingCode(headingCode, showCallback)
    if isEditor then
        log.Warning("[周活动][仅编辑器打印] 请避免使用 headingCode 来索引活动类型，使用 'moduleName' 或 'codeType' 来代替！")
    end
    if ui_window_mgr:IsModuleShown("ui_festival_activity_center") and _window then
        -- 切换页签
        _window:SwitchSubmoduleByHeadingCode(headingCode, showCallback)
    else
        -- 打开活动
        ui_window_mgr:ShowModule("ui_festival_activity_center", function()
            -- 关闭其他页面
            menu_bot_data.CloseAllPage()
            -- 切换页签
            _window:SwitchSubmoduleByHeadingCode(headingCode, showCallback)
        end)
    end
end

---------------------------------接入新的红点系统--------------------------------------

------------------------------------------------------------------------------------

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取模块当前的SortingOrder
---@return number
function GetCurSortingOrder()
    if (not _window) or (not _window:IsValid()) then
        return 0
    end
    return (_window.curOrder or 0)
end

function GetIsChineseNewYearActivity()
    return _isChineseNewYearActivity
end

function CloseActivityView()
    if (not _window) or (not _window:IsValid()) then
        return
    end
    if _window.closeBtnEvent then
        _window.closeBtnEvent()
    end
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function OnSceneDestroy()
    ui_window_mgr:UnloadModule("ui_festival_activity_center")
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function OnActivityTimesUp()
    ui_window_mgr:UnloadModule("ui_festival_activity_center")
end
event.Register(event.ACTIVITY_TIME_UP, OnActivityTimesUp)