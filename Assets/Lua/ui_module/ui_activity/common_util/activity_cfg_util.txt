---
--- Created by: yuannan.
--- DateTime: 2024/11/12.
--- Desc: 读取配置工具
---

local require = require
local table = table
local ipairs = ipairs

local cfg_util = require "cfg_util"
local log = require "log"
local game_scheme = require "game_scheme"
local festival_activity_cfg = require "festival_activity_cfg"

module("activity_cfg_util")

local function LogError(...)
    log.Error("[activity_cfg_util] ", ...)
end

-- 获得活动配置
function GetFestivalActivityCfg(atyId)
    if atyId and atyId > 0 then
        local festivalActivityCfg = festival_activity_cfg.GetActivityCfgByActivityID(atyId)
        if festivalActivityCfg then
            return festivalActivityCfg
        else
            LogError("FestivalActivity Id " .. atyId .. " 没有配置")
        end
    end
end

-- 获取活动的通用UI配置
function GetActivityCommonUICfg(atyId)
    local festivalActivityCfg = GetFestivalActivityCfg(atyId)
    if festivalActivityCfg and festivalActivityCfg.uiTemplateID and festivalActivityCfg.uiTemplateID > 0 then
        local activityCommonUICfg = game_scheme:ActivityCommonUI_0(festivalActivityCfg.uiTemplateID)
        if activityCommonUICfg then
            return activityCommonUICfg
        else
            LogError("ActivityCommonUI Id " .. atyId .. " 没有配置")
        end
    end
end

-- 获取活动的充值ID
function GetRechargeGoodsIdByActivityContentCfg(activityContentId)
    if activityContentId and activityContentId > 0 then
        local cfg = game_scheme:ActivityContent_0(activityContentId)
        if cfg then
            return cfg.rechargeID.data and cfg.rechargeID.data[0] or 0
        else
            LogError("ActivityContent Id " .. activityContentId .. " 没有配置")
            return 0
        end
    end
end

-- 获取活动的无限宝箱配置
function GetActivityBattlePassRewardsCfg(atyId)
    if atyId and atyId > 0 then
        local rewards = {}
        local count = game_scheme:NewHeroTreasureReward_nums()
        for i = 0, count do
            local cfg = game_scheme:NewHeroTreasureReward(i)
            if cfg and cfg.AtyID == atyId then
                table.insert(rewards, cfg)
            end
        end
        if #rewards > 0 then
            return rewards
        else
            LogError("NewHeroTreasureReward Id " .. atyId .. " 没有配置")
        end
    end
end

-- 获取战令活动的无限宝箱配置
function GetActivityInfiniteBoxCfg(atyId)
    if atyId and atyId > 0 then
        local boxCfg = game_scheme:NewHeroTreasureBox_0(atyId)
        if boxCfg then
            return boxCfg
        else
            LogError("NewHeroTreasureBox Id " .. atyId .. " 没有配置")
        end
    end
end

-- 获得活动配置
function GetTaskMainCfg(taskId)
    if taskId and taskId > 0 then
        local taskMainCfg = game_scheme:TaskMain_0(taskId)
        if taskMainCfg then
            return taskMainCfg
        else
            LogError("TaskMai Id " .. taskId .. " 没有配置")
        end
    end
end

function ShowHelpTips(actId)
    if actId then
        local ActivityMain = game_scheme:ActivityMain_0(actId)
        if ActivityMain then
            local uiCfg = game_scheme:ActivityCommonUI_0(ActivityMain.uiTemplateID)
            if uiCfg and uiCfg.helpTips and uiCfg.helpTips > 0 then
                local ui_help = require "ui_help"
                ui_help.ShowWithDate(uiCfg.helpTips)
            end
        end
    end
end

-- 获得充值额外奖励的配置
function GetRechargeRewardIds(rechargeId)
    if rechargeId and rechargeId > 0 then
        local rechargeGoodsCfg = game_scheme:RechargeGoods_0(rechargeId)
        if rechargeGoodsCfg and rechargeGoodsCfg.rewardNew then
            local rewardIds = cfg_util.ArrayToLuaArray(rechargeGoodsCfg.rewardNew)
            return rewardIds
        end
    end
end

-- 生成新表
function InsertFront(t, new_arr)
    local res = {}
    if new_arr then
        for _, v in ipairs(new_arr) do
            table.insert(res, v)
        end
    end
    if t then
        for _, v in ipairs(t) do
            table.insert(res, v)
        end
    end
    return res
end
