local require = require
local pairs = pairs
local table = table
local newClass = newclass
local type = type

local reward_mgr = require "reward_mgr"
local activity_cfg_util = require "activity_cfg_util"
local log = require "log"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"

--region Controller Life
module("ui_battle_pass_unlock_controller")
local controller = nil
local UIController = newClass("ui_battle_pass_unlock_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = { }

    if data and data.list and #data.list > 0 then
        self.CData.index = -1
        self.CData.rechargeData = data
        self:UpdateData()
    else
        log.Error(view_name .. " data is nil! ")
        ui_window_mgr:UnloadModule(self.view_name)
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()

end

function UIController:AutoUnsubscribeEvents()

end
--endregion

--region Controller Logic
function UIController:UpdateData()
    self:TriggerUIEvent("SetToggleState", #self.CData.rechargeData.list > 1)
    self:TriggerUIEvent("SetTaskTextState", self.CData.rechargeData.taskState)

    if self.CData.rechargeData.index > 1 then
        self:OnTogLevel2ValueChange(true)
    else
        self:OnTogLevel1ValueChange(true)
    end
end

function UIController:GetRechargeItem()
    local item = self.CData.rechargeData.list[1]
    return self.CData.rechargeData.list[self.CData.index] or item
end

function UIController:RefreshViewData()
    local item = self:GetRechargeItem()
    self:TriggerUIEvent("SetRechargeData", item.rechargeId)
    local rechargeGoodIds = activity_cfg_util.GetRechargeRewardIds(item.rechargeId)
    local rechargeRewards = reward_mgr.GetRewardGoodsList2(rechargeGoodIds)
    local totalRewards = activity_cfg_util.InsertFront(item.totalRewards, rechargeRewards)
    local getRewards = activity_cfg_util.InsertFront(item.getRewards, rechargeRewards)
    self:TriggerUIEvent("SetRewardList", totalRewards, getRewards)
end

function UIController:OnBtnBuyClickedProxy()
    local net_activity_module = require "net_activity_module"
    local rechargeItem = self:GetRechargeItem()
    net_activity_module.Send_New_Recharge_REQ(rechargeItem.rechargeId)
    -- 关闭界面
    ui_window_mgr:UnloadModule(self.view_name)
end

function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end

function UIController:OnTogLevel1ValueChange(state)
    if state and self.CData.index ~= 1 then
        self.CData.index = 1
        self:TriggerUIEvent("SwitchToggle", true, false)
        self:RefreshViewData()
    end
end

function UIController:OnTogLevel2ValueChange(state)
    if state and self.CData.index ~= 2 then
        self.CData.index = 2
        self:TriggerUIEvent("SwitchToggle", false, true)
        self:RefreshViewData()
    end
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
