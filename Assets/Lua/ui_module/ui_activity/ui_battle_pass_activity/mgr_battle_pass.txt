---
--- Created by: yuannan.
--- DateTime: 2024/11/7.
--- Desc: 战令数据处理模块
---

local require = require
local math = math
local pairs = pairs
local ipairs = ipairs
local table = table
local os = os

local gw_event_activity_define = require "gw_event_activity_define"
local table_util = require "table_util"
local topic_pb = require "topic_pb"
local cfg_util = require "cfg_util"
local activity_cfg_util = require "activity_cfg_util"
local event_task_define = require "event_task_define"
local event = require "event"
local game_scheme = require "game_scheme"
local log = require "log"

module("mgr_battle_pass")

local registerEvent = false
local mgr_datas = nil
local registerAtyItem = nil

--region Data Logic
local function createGWActivityData(atyId, timeStamp)
    if not mgr_datas then
        mgr_datas = {}
    end

    if not mgr_datas[atyId] then
        local gw_activity_data_mgr = require "gw_activity_data_mgr"
        mgr_datas[atyId] = gw_activity_data_mgr.CreateAtyData(atyId, timeStamp)
    end
    return mgr_datas[atyId]
end

local function removeGWActivityData(atyId)
    if mgr_datas and mgr_datas[atyId] then
        local gw_activity_data_mgr = require "gw_activity_data_mgr"
        gw_activity_data_mgr.RemoveAtyData(atyId)
    end
end

local function clearGWActivityData(eventName, atyId)
    if mgr_datas and mgr_datas[atyId] then
        mgr_datas[atyId] = nil
    end
end

local function setGWActivityData_cfg(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    local festivalActivityCfg = activity_cfg_util.GetFestivalActivityCfg(atyId)
    if festivalActivityCfg then
        local recharges = { [1] = 0 }
        local tempIds = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID2)
        if tempIds then
            for k, v in ipairs(tempIds) do
                table.insert(recharges, activity_cfg_util.GetRechargeGoodsIdByActivityContentCfg(v))
            end
        end

        local infoId = 0
        if festivalActivityCfg.ctnID1.data and festivalActivityCfg.ctnID1.data[0] then
            infoId = festivalActivityCfg.ctnID1.data[0]
        end

        local itemId = 0
        if festivalActivityCfg.ctnID3.data and festivalActivityCfg.ctnID3.data[0] then
            itemId = festivalActivityCfg.ctnID3.data[0]
            -- 绑定道具刷新事件
            registerAtyItem[itemId] = registerAtyItem[itemId] or {}
            table.insert(registerAtyItem[itemId], atyId)
        end

        -- 奖励配置
        local rewards = {}
        local rewardCfgs = activity_cfg_util.GetActivityBattlePassRewardsCfg(atyId)
        if rewardCfgs then
            for k, v in ipairs(rewardCfgs) do
                local rewardItem = { id = v.nID, exp = v.nLevelExp, rewardID = cfg_util.ArrayToLuaArray(v.nRewardID),
                                     payRewardID = cfg_util.ArrayToLuaArray(v.nPayRewardID),
                                     payRoyalRewardID = cfg_util.ArrayToLuaArray(v.nPayRoyalRewardID) }
                table.insert(rewards, rewardItem)
            end
        end

        local atyCfg = mgr_data:GetAtyCfg() or {}
        atyCfg.infoId = infoId
        atyCfg.itemId = itemId
        atyCfg.recharges = recharges
        atyCfg.rewards = rewards

        atyCfg.boxCfg = game_scheme:NewHeroTreasureBox_0(atyId)
        if rewards and #rewards > 0 then
            atyCfg.minExp = rewards[1].exp
            atyCfg.maxExp = rewards[#rewards].exp
        else
            atyCfg.minExp = 0
            atyCfg.maxExp = 0
        end

        atyCfg.tasks1 = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID4)
        atyCfg.tasks2 = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID5)
        mgr_data:SetAtyCfg(atyCfg)
        return atyCfg
    end
end

local function setGWActivityData_data(atyId, k, v)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    local atyCfg = mgr_data:GetAtyCfg()
    if not atyCfg then
        return
    end

    local atyData = mgr_data:GetAtyData() or {
        taskRechargeState = 0,
        rechargeStates = { 1, 0, 0 },
        getIndex = 0,
        boxIndex = 0,
        waitGetIndex2 = 0,
        waitGetIndex3 = 0
    }

    local player_mgr = require "player_mgr"
    atyData.exp = player_mgr.GetPlayerOwnNum(atyCfg.itemId)
    -- 记录打点属性
    atyData.Passport_piont = atyData.exp

    k = k - 1
    if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 then
        atyData.taskRechargeState = v == 1 and 1 or 0
    elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 then
        atyData.rechargeStates[2] = (v / 10) > 0 and 1 or 0
        atyData.rechargeStates[3] = (v % 10) > 0 and 1 or 0
    elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 then
        atyData.getIndex = (v or 0) % 100
        atyData.boxIndex = math.floor((v or 0) / 100)
    elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 then
        atyData.waitGetIndex2 = v or 0
    elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D5 then
        atyData.waitGetIndex3 = v or 0
    end
    mgr_data:SetAtyData(atyData)
end

-- 获得充值全部奖励 
local function getRechargeAllRewardIds(atyId, rechargeIdx,totalRewards)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end

    local mgr_data = mgr_datas[atyId]
    local atyCfg = mgr_data:GetAtyCfg()
    if not atyCfg or not atyCfg.rewards then
        return
    end

    totalRewards = totalRewards or {}
    local rewardKey = rechargeIdx and rechargeIdx == 2 and "payRewardID" or "payRoyalRewardID"
    for k, v in ipairs(atyCfg.rewards) do
        for l, m in ipairs(v[rewardKey]) do
            table.insert(totalRewards, m)
        end
    end
    return totalRewards
end

-- 获得充值可领取奖励
local function getRechargeAvailableRewardIds(atyId, rechargeIdx)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end

    local mgr_data = mgr_datas[atyId]
    local atyCfg = mgr_data:GetAtyCfg()
    local atyData = mgr_data:GetAtyData()
    if not atyCfg or not atyData or not atyCfg.rewards then
        return
    end
    local availableRewards = {}
    local rewardKey = rechargeIdx and rechargeIdx == 2 and "payRewardID" or "payRoyalRewardID"
    for k, v in ipairs(atyCfg.rewards) do
        if atyData.exp >= v.exp then
            for l, m in ipairs(v[rewardKey]) do
                table.insert(availableRewards, m)
            end
        end
    end
    return availableRewards
end

-- 任务奖励领取事件
local function updateTaskState(...)
    if mgr_datas then
        for k, v in pairs(mgr_datas) do
            v:TriggerEvent()
        end
    end
end

local function updateGoodProp(eventName, id, sid, num)
    if mgr_datas and registerAtyItem[id] then
        -- 刷新战令道具
        for k, v in ipairs(registerAtyItem[id]) do
            local mgr_data = mgr_datas and mgr_datas[v]
            if mgr_data then
                local atyData = mgr_data:GetAtyData()
                if atyData then
                    atyData.exp = num
                    if atyData.Passport_piont ~= atyData.exp then
                        --获得战令积分 打点
                        local getPoint = atyData.exp - atyData.Passport_piont
                        atyData.Passport_piont = atyData.exp
                        local reportMsg = {
                            get_point = getPoint, -- 本次获得积分
                            total_point = atyData.exp, -- 当前战令总积分
                            Activity_id = v, -- 活动ID
                        }
                        event.EventReport("Passport_piont", reportMsg)
                    end
                    mgr_data:SetAtyData(atyData)
                end
            end
        end
    end
end

local function loginGoodProp()
    if mgr_datas then
        local player_mgr = require "player_mgr"
        for k, v in pairs(registerAtyItem) do
            updateGoodProp(nil, nil, k, player_mgr.GetPlayerOwnNum(k))
        end
    end
end

local function getBattlePassRed(redNum)
    return redNum
end
--endregion

function Init()
    if not registerEvent then
        registerEvent = true
        registerAtyItem = {}
        event.Register(event.FIRST_LOGIN_CREATE_ROGUE_ENTITY_FINISH, loginGoodProp)
        event.Register(event.UPDATE_GOODS_NUM_CHANGE, updateGoodProp)
        event.Register(event_task_define.REFRESH_TASK, updateTaskState)
        event.Register(gw_event_activity_define.GW_ACTIVITY_COMMON_DISPOSE, clearGWActivityData)

        -- --注册红点
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.RegisterRedFunc(red_const.Enum.BattlePassToggleRed, getBattlePassRed)
    end
end
function GetAllBattlePassData()
    return mgr_datas or {}
end

function GetBattlePassData(atyId)
    return mgr_datas and mgr_datas[atyId]
end

function SetMainBattlePassData(atyId, timeStamp, k, v)
    if not atyId then
        return
    end

    if timeStamp and timeStamp <= (os.server_time() + 1) then
        removeGWActivityData(atyId)
    else
        if not mgr_datas or not mgr_datas[atyId] then
            createGWActivityData(atyId, timeStamp)
            local cfg = setGWActivityData_cfg(atyId)
            if cfg then
                local gw_task_mgr = require "gw_task_mgr"
                local tasks = table_util.MergeTables(cfg.tasks1, cfg.tasks2)
                gw_task_mgr.SetActivityTaskId(atyId, tasks)
            end
        end
        if timeStamp and mgr_datas and mgr_datas[atyId] then
            mgr_datas[atyId].atyTimeStamp = timeStamp
        end
        setGWActivityData_data(atyId, k, v)
    end
end

-- 打开战令解锁界面
function OpenBattlePassUnlockView(atyId, rechargeId)
    if mgr_datas and mgr_datas[atyId] and rechargeId and rechargeId > 0 then
        local mgr_data = mgr_datas[atyId]
        local cfg = mgr_data:GetAtyCfg()
        local data = mgr_data:GetAtyData()
        if not cfg or not data then
            log.Error("Battle Pass activity not unlocked. id ： " .. atyId)
            return
        end

        local index = table_util.IndexOf(cfg.recharges, rechargeId)
        if not index then
            log.Error("Battle Pass rechargeId not found. id ： " .. rechargeId)
            return
        end

        local reward_mgr = require "reward_mgr"
        local viewData = {}
        if #cfg.recharges < 3 then
            viewData.index = 2
        else
            viewData.index = index and index > 2 and 2 or 1
        end
        viewData.taskState = true
        viewData.list = {}
        for k, v in ipairs(cfg.recharges) do
            if v > 0 then
                if data.rechargeStates[k] <= 0 then
                    local item = { rechargeId = v }
                    item.totalRewards = reward_mgr.GetSummaryRewards(getRechargeAllRewardIds(atyId, k), true)
                    item.getRewards = reward_mgr.GetSummaryRewards(getRechargeAvailableRewardIds(atyId, k), true)
                    table.insert(viewData.list, item)
                else
                    data.taskState = false
                end
            end
        end
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_battle_pass_unlock", nil, nil, viewData)
        local reportMsg = {
            Activity_id = atyId, -- 活动ID
        }
        event.EventReport("Passport_Buy_Enter", reportMsg)
    end
end
function GetAllReward(atyId,isSort)
    local rewardsList = {}
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local cfg = mgr_data:GetAtyCfg()
        local data = mgr_data:GetAtyData()
        if not cfg or not data then
            log.Error("Battle Pass activity not unlocked. id ： " .. atyId)
            return
        end
        local reward_mgr = require "reward_mgr"
        local rewardsIdList = {}
        for k, v in ipairs(cfg.recharges) do
            if v > 0 then
                --if data.rechargeStates[k] <= 0 then
                    --table.insert(rewardsIdList,v)
                    rewardsIdList = getRechargeAllRewardIds(atyId, k,rewardsIdList)
                --end
            end
        end
        for k, v in ipairs(cfg.rewards) do
            for l, m in ipairs(v["rewardID"] or {}) do
                table.insert(rewardsIdList, m)
            end
        end
        rewardsList = reward_mgr.GetRewardGoodsMergers(rewardsIdList)
        if isSort then
            table.sort(rewardsList,reward_mgr.sortRewardsTable)
        end
    end
    return rewardsList
end
-- 获得未解锁的战令充值ID
function GetRechargeId(atyId)
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local atyCfg = mgr_data:GetAtyCfg()
        local atyData = mgr_data:GetAtyData()
        if atyCfg and atyData then
            for k, v in ipairs(atyCfg.recharges) do
                if v > 0 and atyData.rechargeStates[k] <= 0 then
                    return v
                end
            end
        end
    end
    return 0
end
function GetMaxRechargeId(atyId)
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local atyCfg = mgr_data:GetAtyCfg()
        local atyData = mgr_data:GetAtyData()
        if atyCfg and atyData then
            return atyCfg.recharges[#atyCfg.recharges]
        end
    end
    return nil
end

--获得未解锁战令的activityContentID
function GetActivityContentID(atyId)
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local atyCfg = mgr_data:GetAtyCfg()
        local atyData = mgr_data:GetAtyData()
        local festivalActivityCfg = activity_cfg_util.GetFestivalActivityCfg(atyId)
        local tempIds = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID2)
        if atyCfg and atyData then
            for k, v in ipairs(atyCfg.recharges) do
                if v > 0 and atyData.rechargeStates[k] <= 0 then
                    return tempIds[k - 1]
                end
            end
        end
    end
    return nil
end

--获取充值礼包的所有奖励
function GetRewardByRechargeID(atyId, rechargeId)
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local atyCfg = mgr_data:GetAtyCfg()
        if atyCfg then
            for k, v in ipairs(atyCfg.recharges) do
                if v == rechargeId then
                    return getRechargeAllRewardIds(atyId, k)
                end
            end
        end
    end
end
-- 获得已领取的任务下标
--@return {index1, index2} 每个挡位的任务下标
function GetRewardIndexTable(atyId)
    local indexTable = { 0, 0, 0 }
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local data = mgr_data:GetAtyData()
        if data then
            indexTable[1] = data.getIndex
            indexTable[2] = data.waitGetIndex2 == 1 and data.getIndex or 0
            indexTable[3] = data.waitGetIndex3 == 1 and data.getIndex or 0
        end
    end
    return indexTable
end

-- 获得可领取的奖励下标
function GetCanGetRewardIndex(atyId)
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local atyCfg = mgr_data:GetAtyCfg()
        local atyData = mgr_data:GetAtyData()
        if atyCfg and atyData then
            if atyData.getIndex < #atyCfg.rewards then
                for i = #atyCfg.rewards, 1, -1 do
                    if atyData.exp >= atyCfg.rewards[i].exp and i > atyData.getIndex then
                        return i
                    end
                end
            end
        end
    end
    return 0
end

-- 获得可领取任务奖励的数量
function GetCanGetTaskCount(tasks, rechargeState)
    if not tasks or #tasks == 0 then
        return 0
    end
    local num = 0
    local gw_task_mgr = require "gw_task_mgr"
    if tasks then
        for _, v in ipairs(tasks) do
            local taskData = gw_task_mgr.GetTaskData(v)
            if taskData and taskData.rate >= taskData.completeValue then
                if not taskData.status then
                    num = num + 1
                elseif rechargeState == 1 and not taskData.exStatus then
                    num = num + 1
                end
            end
        end
    end
    return num
end

-- 判断战令是否可有可以领取的奖励
function GetCanGetRewardCount(atyId, notTask)
    local num = 0
    if mgr_datas and mgr_datas[atyId] then
        local mgr_data = mgr_datas[atyId]
        local atyCfg = mgr_data:GetAtyCfg()
        local atyData = mgr_data:GetAtyData()
        if atyCfg and atyData then
            if atyData.exp >= atyCfg.maxExp and atyCfg.boxCfg then
                local diff = atyData.exp - atyCfg.maxExp - atyCfg.boxCfg.Point * atyData.boxIndex
                if diff >= atyCfg.boxCfg.Point then
                    num = num + math.floor(diff / atyCfg.boxCfg.Point)
                end
            end

            local index = atyData.getIndex;
            if (atyData.rechargeStates[2] == 1 and atyData.waitGetIndex2 ~= 1) or (atyData.rechargeStates[3] == 1 and atyData.waitGetIndex3 ~= 1) then
                index = 0
            end

            for i = index + 1, #atyCfg.rewards do
                if atyData.exp >= atyCfg.rewards[i].exp then
                    num = num + 1
                end
            end

            if not notTask then
                num = num + GetCanGetTaskCount(atyCfg.tasks1, atyData.taskRechargeState)
                num = num + GetCanGetTaskCount(atyCfg.tasks2, atyData.taskRechargeState)
            end
        end
    end
    return num
end

