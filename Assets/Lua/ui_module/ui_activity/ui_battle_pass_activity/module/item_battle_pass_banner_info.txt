---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/11/6.
--- Desc: 战令活动Banner背景模块
---

local require = require
local newclass = newclass
local tostring = tostring

local binding = require "item_battle_pass_banner_info_binding"
local item_activity_banner_info = require "item_activity_banner_info"

module("item_battle_pass_banner_info")
local itemView = newclass("item_battle_pass_banner_info", item_activity_banner_info)

itemView.widget_table = binding.WidgetTable

function itemView:Init(prefab, order, onScoreClick, onRechargeClick)
    self.onScoreClick = onScoreClick
    item_activity_banner_info.Init(self, prefab, order, onRechargeClick)
end

function itemView:UpdateCfg(cfg)
    item_activity_banner_info.UpdateCfg(self, cfg)
    if self.activityCfg then
        if self.btn_information then
            self.btn_information.gameObject:SetActive(self.activityCfg.infoId and self.activityCfg.infoId > 0 or false)
        end
        if self.img_score then
            self:LoadSpriteAsset(self.img_score, self.activityCfg.itemPath)
        end
        if self.btn_score then
            self.btn_score.gameObject:SetActive(self.activityCfg.btnScoreState or false)
        end
    end
end

function itemView:UpdateData(data)
    item_activity_banner_info.UpdateData(self, data)
    if self.activityData then
        if self.txt_score then
            self.txt_score.text = tostring(self.activityData.itemCount)
        end
    end
end

function itemView:OnBtnScoreClickedProxy()
    if self:CheckActivityEvent() and self.onScoreClick then
        self.onScoreClick()
    end
end

function itemView:OnBtnInformationClickedProxy()
    if self:CheckActivityEvent() and self.activityCfg.infoId and self.activityCfg.infoId > 0 then
        local gw_hero_mgr = require "gw_hero_mgr"
        gw_hero_mgr.OpenHeroDetailUI(self.activityCfg.infoId)
    end
end

return itemView