---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/12/23.
--- Desc: 战令活动任务模块
---

local require = require
local newclass = newclass
local tostring = tostring
local pairs = pairs
local ipairs = ipairs
local type = type

local gw_task_mgr = require "gw_task_mgr"
local scheduler_activity_tasks = require "scheduler_activity_tasks"
local reward_mgr = require "reward_mgr"
local Mathf = require "Mathf"
local binding = require "item_activity_tasks_binding"
local item_activity_tasks = require "item_activity_tasks"

module("item_battle_pass_tasks")
local itemView = newclass("item_battle_pass_tasks", item_activity_tasks)

itemView.widget_table = binding.WidgetTable

function itemView:Init(prefab, order, onRechargeClick)
    item_activity_tasks.Init(self, prefab, order, onRechargeClick)
end

function itemView:UpdateCfg(cfg)
    item_activity_tasks.UpdateCfg(self, cfg)
end

function itemView:UpdateData(data)
    item_activity_tasks.UpdateData(self, data)
end

function itemView:OnTasksRender(scroll_rect_item, index, dataItem)
    item_activity_tasks.OnTasksRender(self, scroll_rect_item, index, dataItem)

    local progress = scroll_rect_item:Get("progress")
    if progress then
        progress.fillAmount = Mathf.Clamp(dataItem.data.rate / dataItem.data.completeValue, 0, 1)
    end

    local progressText = scroll_rect_item:Get("progressText")
    if progressText then
        progressText.text = tostring(dataItem.data.rate) .. "/" .. tostring(dataItem.data.completeValue)
    end
end

function itemView:SetTaskGoodsItemList(scroll_rect_item, rewardId, root)
    if rewardId and scroll_rect_item.data.rewardId ~= rewardId then
        scroll_rect_item.data.rewardId = rewardId
        --  先卸载所有的GoodsItem
        if scroll_rect_item.data[3] and #scroll_rect_item.data[3] > 0 then
            for i, v in pairs(scroll_rect_item.data[3]) do
                if type(v) == "table" and v.Dispose then
                    v:Dispose()
                end
            end
        end

        if rewardId > 0 then
            scroll_rect_item.data[3] = scroll_rect_item.data[3] or {}
            local goodsItemList = reward_mgr.GetRewardItemList(rewardId, root, self.onRewardClick, 0.5)
            if #goodsItemList > 0 then
                for k, v in ipairs(goodsItemList) do
                    scroll_rect_item.data[3][k] = v
                end
            end
        end
    end
end

function itemView:OnGetClickEvent(dataItem)
    if self:CheckActivityEvent() and dataItem.taskState == scheduler_activity_tasks.StateType.get then
        gw_task_mgr.ReceiveTaskListReward(self.activityCfg.tasks, self.activityCfg.atyId, nil)
    end
end

function itemView:OnRechargeClickEvent(dataItem)
    if self:CheckActivityEvent() and dataItem.taskState == scheduler_activity_tasks.StateType.recharge then
        if dataItem.rechargeState then
            -- 已充值
            gw_task_mgr.ReceiveTaskListRechargeReward(self.activityCfg.tasks, self.activityCfg.atyId, nil, dataItem.rechargeState)
        elseif dataItem.rechargeId > 0 and self.IData.onRechargeClick then
            -- 未充值（可充值）
            self.IData.onRechargeClick(dataItem.rechargeId)
        end
    end
end
return itemView