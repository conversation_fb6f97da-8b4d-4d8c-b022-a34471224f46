---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/11/28.
--- Desc: activity Banner 的数据模块
---

module("data_battle_pass_banner_info")

--- 创建适用于活动Banner的数据模块
function NewActivityCfg(uiCfg, timestamp, itemPath, infoId, btnScoreState)
    local cfgTab = {
        uiCfg = uiCfg or nil,
        timestamp = timestamp or 0,
        itemPath = itemPath or nil,
        infoId = infoId or 0,
        btnScoreState = btnScoreState or nil,
    }
    return cfgTab
end

--- 创建适用于活动Banner的数据模块
function NewActivityData(rechargeId, itemCount)
    local dataTab = {
        rechargeId = rechargeId or 0,
        itemCount = itemCount or 0,
    }
    return dataTab
end