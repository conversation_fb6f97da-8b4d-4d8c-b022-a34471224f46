local require = require
local pairs = pairs
local ipairs = ipairs
local table = table
local newClass = newclass
local type = type

local scheduler_activity_tasks = require "scheduler_activity_tasks"
local data_activity_tasks = require "data_activity_tasks"
local game_scheme = require "game_scheme"
local gw_event_activity_define = require "gw_event_activity_define"
local event = require "event"
local mgr_battle_pass = require "mgr_battle_pass"
local data_activity_infinite_box = require "data_activity_infinite_box"
local data_activity_rewards = require "data_activity_rewards"
local data_battle_pass_banner_info = require "data_battle_pass_banner_info"
local controller_base = require "controller_base"

--region Controller Life
module("ui_battle_pass_controller")
local controller = nil
local UIController = newClass("ui_battle_pass_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = { toggleIndex = 1, atyId = data.activityID, state = true }
    self:SetUICfg()
    self:SetUIData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    self.mgrData = nil
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.onScoreClickEvent = function()
        if self.mgrData then
            self:OnTogActiveTaskValueChange(true)
        end
    end

    self.onRechargeEvent = function(...)
        self:OnRechargeEventClick(...)
    end

    self.onGetRewardEvent = function(...)
        self:OnGetRewardEventClick(...)
    end

    self.onUpdateDataEvent = function(eventName, atyId)
        if atyId == self.CData.atyId then
            self:SetUIData()
        end
    end

    self.onRemoveDataEvent = function(eventName, atyId)
        if atyId == self.CData.atyId then
            self:SetActivityState(false)
        end
    end

    event.Register(gw_event_activity_define.GW_ACTIVITY_COMMON_DATA_UPDATE, self.onUpdateDataEvent)
    event.Register(gw_event_activity_define.GW_ACTIVITY_COMMON_REMOVE, self.onRemoveDataEvent)
end

function UIController:AutoUnsubscribeEvents()
    event.Unregister(gw_event_activity_define.GW_ACTIVITY_COMMON_DATA_UPDATE, self.onUpdateDataEvent)
    event.Unregister(gw_event_activity_define.GW_ACTIVITY_COMMON_REMOVE, self.onRemoveDataEvent)
end
--endregion

--region Controller Logic
function UIController:SetUICfg()
    self.mgrData = mgr_battle_pass.GetBattlePassData(self.CData.atyId)
    if not self.mgrData or not self.CData.state then
        return
    end
    self:TriggerUIEvent("CreateViewModule", self.onGetRewardEvent, self.onRechargeEvent, self.onScoreClickEvent)

    local atyCfg = self.mgrData:GetAtyCfg()
    if not atyCfg then
        return
    end

    local itemCfg = game_scheme:Item_0(atyCfg.itemId)
    local itemIcon = itemCfg and itemCfg.icon or nil
    local rewardItems = {}
    for _, v in ipairs(atyCfg.rewards) do
        table.insert(rewardItems, data_activity_rewards.NewActivityItem(v.id, v.exp, v.rewardID, v.payRewardID, v.payRoyalRewardID))
    end

    local bannerCfg = data_battle_pass_banner_info.NewActivityCfg(self.mgrData.atyUICfg, self.mgrData.atyTimeStamp, itemIcon, atyCfg.infoId, true)
    local rewardCfg = data_activity_rewards.NewActivityCfg(rewardItems, atyCfg.recharges, itemIcon)
    local boxCfg = data_activity_infinite_box.NewActivityCfg(self.CData.atyId, self.mgrData.atyUICfg.BoxIcon, itemIcon, atyCfg.minExp, atyCfg.maxExp)
    local task1Cfg = data_activity_tasks.NewActivityCfg(self.CData.atyId, atyCfg.tasks1, scheduler_activity_tasks.SchedulerType.default)
    local task2Cfg = data_activity_tasks.NewActivityCfg(self.CData.atyId, atyCfg.tasks2, scheduler_activity_tasks.SchedulerType.default)

    self:TriggerUIEvent("SetViewModuleCfg", bannerCfg, rewardCfg, boxCfg, task1Cfg, task2Cfg)
end

function UIController:SetUIData()
    if not self.mgrData or not self.CData.state then
        return
    end
    local atyCfg = self.mgrData:GetAtyCfg()
    local atyData = self.mgrData:GetAtyData()
    if atyCfg and atyData then
        local rechargeId = mgr_battle_pass.GetRechargeId(self.CData.atyId)
        local indexTable = mgr_battle_pass.GetRewardIndexTable(self.CData.atyId)
        local index = indexTable[1]
        local rewardIndex = mgr_battle_pass.GetCanGetRewardIndex(self.CData.atyId)
        if rewardIndex > 0 then
            index = rewardIndex
        end
        local rewardCount = mgr_battle_pass.GetCanGetRewardCount(self.CData.atyId, true)

        self:TriggerUIEvent("SetViewModuleData", data_battle_pass_banner_info.NewActivityData(rechargeId, atyData.exp),
                data_activity_rewards.NewActivityData(atyData.exp, index, indexTable, atyData.rechargeStates),
                data_activity_infinite_box.NewActivityData(atyData.exp, rechargeId, rewardCount, atyData.boxIndex),
                data_activity_tasks.NewActivityData(rechargeId, atyData.taskRechargeState),
                data_activity_tasks.NewActivityData(rechargeId, atyData.taskRechargeState))

        local task1Count = mgr_battle_pass.GetCanGetTaskCount(atyCfg.tasks1, atyData.taskRechargeState)
        local task2Count = mgr_battle_pass.GetCanGetTaskCount(atyCfg.tasks2, atyData.taskRechargeState)
        self:TriggerUIEvent("SetActivityRedState",rewardCount, task1Count, task2Count)
    end
end

function UIController:SetActivityState(state)
    self.CData.state = state
    self:TriggerUIEvent("SetActivityState", state)
end

function UIController:OnTogRewardsValueChange(state)
    if state and self.CData.toggleIndex ~= 1 then
        self.CData.toggleIndex = 1
        self:TriggerUIEvent("RefreshToggleState", true, false, false)
    end
end

function UIController:OnTogDailyTaskValueChange(state)
    if state and self.CData.toggleIndex ~= 2 then
        self.CData.toggleIndex = 2
        self:TriggerUIEvent("RefreshToggleState", false, true, false)
    end
end

function UIController:OnTogActiveTaskValueChange(state)
    if state and self.CData.toggleIndex ~= 3 then
        self.CData.toggleIndex = 3
        self:TriggerUIEvent("RefreshToggleState", false, false, true)
    end
end

function UIController:OnRechargeEventClick(rechargeId)
    if self.CData.state and rechargeId and rechargeId > 0 then
        mgr_battle_pass.OpenBattlePassUnlockView(self.CData.atyId, rechargeId)
    end
end

function UIController:OnGetRewardEventClick(rewardId)
    if self.CData.state and rewardId and rewardId > 0 then
        local net_script = require "net_script"
        net_script.RequestServerLuaFuncNew("Request_BattlePassGetAllReward", { AtyID = self.CData.atyId })
    end
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
