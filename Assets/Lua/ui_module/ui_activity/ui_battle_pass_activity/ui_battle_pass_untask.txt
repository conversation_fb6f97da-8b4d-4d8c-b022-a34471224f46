local require = require
local pairs = pairs
local type = type

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local class = require "class"
local binding = require "ui_battle_pass_untask_binding"

local item_activity_banner_info = require "item_battle_pass_banner_info"
local item_activity_rewards = require "item_activity_rewards"
local item_activity_infinite_box = require "item_activity_infinite_box"

--region View Life
module("ui_battle_pass_untask")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    self.__base.Close(self)
end

function UIView:UpdateUI()
    if self.VData then
        if self.VData.item_bannerInfo then
            self.VData.item_bannerInfo:UpdateOrder(self.curOrder)
        end

        if self.VData.item_reward then
            self.VData.item_reward:UpdateOrder(self.curOrder)
        end

        if self.VData.item_infiniteBox then
            self.VData.item_infiniteBox:UpdateOrder(self.curOrder)
        end
    end
end

--endregion

--region View Logic
function UIView:CreateViewModule(onGetRewardClick, onRechargeClick, onScoreClick)
    if not self.VData.item_bannerInfo then
        self.VData.item_bannerInfo = item_activity_banner_info.new()
        self.VData.item_bannerInfo:Init(self.item_battlePass_bannerInfo, self.curOrder, onScoreClick, onRechargeClick)
    end

    if not self.VData.item_reward then
        self.VData.item_reward = item_activity_rewards.new()
        self.VData.item_reward:Init(self.item_activity_rewards, self.curOrder, onGetRewardClick, onRechargeClick)
    end
    if not self.VData.item_infiniteBox then
        self.VData.item_infiniteBox = item_activity_infinite_box.new()
        self.VData.item_infiniteBox:Init(self.item_activity_infiniteBox, self.curOrder, onGetRewardClick, onRechargeClick)
    end
end

function UIView:SetViewModuleCfg(bannerCfg, rewardCfg, boxCfg)
    if bannerCfg and self.VData.item_bannerInfo then
        self.VData.item_bannerInfo:UpdateCfg(bannerCfg)
    end
    if rewardCfg and self.VData.item_reward then
        self.VData.item_reward:UpdateCfg(rewardCfg)
    end
    if boxCfg and self.VData.item_infiniteBox then
        self.VData.item_infiniteBox:UpdateCfg(boxCfg)
    end
end

function UIView:SetViewModuleData(bannerData, rewardData, boxData)
    if bannerData and self.VData.item_bannerInfo then
        self.VData.item_bannerInfo:UpdateData(bannerData)
    end
    if rewardData and self.VData.item_reward then
        self.VData.item_reward:UpdateData(rewardData)
    end
    if boxData and self.VData.item_infiniteBox then
        self.VData.item_infiniteBox:UpdateData(boxData)
    end
end

function UIView:SetActivityState(state)
    if self.VData.item_bannerInfo then
        self.VData.item_bannerInfo:UpdateState(state)
    end
    if self.VData.item_reward then
        self.VData.item_reward:UpdateState(state)
    end
    if self.VData.item_infiniteBox then
        self.VData.item_infiniteBox:UpdateState(state)
    end
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == "table" then
            local uiPath = data.uiPath or ui_path
            local uiParent = data.uiParent or nil
            window:LoadUIResource(uiPath, nil, uiParent, nil)
        else
            window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
