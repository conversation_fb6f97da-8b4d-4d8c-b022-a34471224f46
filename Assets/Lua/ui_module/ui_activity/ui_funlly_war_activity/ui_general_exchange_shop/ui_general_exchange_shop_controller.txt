local require = require
local pairs = pairs     
local string = string
local table = table
local newClass = newclass
local type = type
local tonumber 			= tonumber

local util = require "util"
local gw_event_activity_define = require "gw_event_activity_define"
local net_activity_module = require "net_activity_module"
local math = require "math"
local flow_text = require "flow_text"
local player_mgr = require "player_mgr"
local reward_mgr = require "reward_mgr"
local log = require "log"
local game_scheme = require "game_scheme"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_general_exchange_shop_controller")
local controller = nil
local UIController = newClass("ui_general_exchange_shop_controller", controller_base)
local T_ExchangeTye = {
    ActivityContent = 1,      --使用活动通用兑换，配置数据在ActivityContent里   
}

--@Param data = {
---------通用活动需要传入的参数-------------
--    exchangeType,      --兑换类型（1为活动通用兑换）
--    AtyID,             --活动ID(使用活动通用兑换时需要)
--    ActivityContentID, --兑换物品ActivityContentID
--    OnClickCallBack    --点击购买按钮时触发回调函数（可选）
--------普通兑换需要传入的参数-------------
--    goodId,            --物品ID
--    goodNum,           --数量
--    name,              --名字
--    price,             --价格
--    limitNum,          --限购数量
--    priceId,           --价格ID
--    count,             --默认兑换数量（可不传默认为1）
--}

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    if data == nil then
        log.Error("data is nil,请传入数据")
        return
    end
    self.exchangeType = data.exchangeType
    self:ParseData(data)
    self:InitBaseData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents() 
    local refreshUI = function(_, msg)
        if self.exchangeType == T_ExchangeTye.ActivityContent then
            --使用活动兑换
            if msg.AtyID == self.AtyID then
                self.limitNum = self.limitNum - self.curCount
                if self.limitNum <= 0 then
                    ui_window_mgr:UnloadModule(self.view_name)
                    return
                end
                self.curCount = 1
                self.canSpendNum = player_mgr.GetPlayerOwnNum(self.priceId)
                self.maxCanBuyNum = self.limitNum > math.floor(self.canSpendNum/self.price) and math.floor(self.canSpendNum/self.price) or self.limitNum
                self:RefreshUI()
            end
        end
    end
    self:RegisterEvent(gw_event_activity_define.GW_FULL_SIEGE_EXCHANGE_SHOP_DATA_UPDATE, refreshUI)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

--解析数据
function UIController:ParseData(data)

    if data.exchangeType == T_ExchangeTye.ActivityContent then
        --使用活动兑换
        self.AtyID = data.AtyID
        self.ActivityContentID = data.ActivityContentID         --兑换物品ID
        self.OnClickCallBack = data.OnClickCallBack             --点击购买按钮触发回调函数
        local cfg = game_scheme:ActivityContent_0(data.ActivityContentID)
        if cfg == nil then
            log.Error("cfg is nil, ActivityContentID = " .. data.ActivityContentID)
            return
        end
        local arr = util.SplitString(cfg.expenditure, "#")
        self.priceId = tonumber(arr[1])      --花费物品的itemId
        self.price = tonumber(arr[2])        --价格
        local rewardID = util.SplitString(cfg.rewardGroup, "#")[1]
        local rewardList = {}
        rewardList = reward_mgr.GetRewardGoodsList(rewardID, rewardList)
        if rewardList[1] then
            self.goodId = rewardList[1].id     --物品id
            self.goodNum = rewardList[1].num   --数量
        else
            log.Error("rewardList is nil, rewardID = " .. rewardID)
            self.goodId = 1                         --物品id
            self.goodNum = 1                       --数量
        end
        
        self.name = cfg.remark                  --名字
        self.canSpendNum = player_mgr.GetPlayerOwnNum(self.priceId)     --玩家拥有的用于兑换物品的物品数量
    else
        self.goodId = data.goodId      --兑换的物品id
        self.goodNum = data.goodNum     --兑换的物品数量
        self.priceId = data.priceId     --花费物品的itemI
        self.name = data.name               --名字
        self.canSpendNum = data.canSpendNum           --玩家拥有的用于兑换物品的物品数量
        self.price = data.price             --价格
        self.limitNum = data.limitNum       --限购数量
    end
    local priceCfg = game_scheme:Item_0(self.priceId)
    if priceCfg then
        self.priceIcon = priceCfg.icon          --花费物品的icon
        self.priceType = priceCfg.type                   --价格类型
        self.priceName = priceCfg.nameKey                   --价格类型
    else
        self.priceIcon = 1                      --花费物品的icon
        self.priceType = 0                   --价格类型
        self.priceType = 1                  --价格类型
        log.Error("priceCfg is nil, priceId = " .. self.priceId)
    end
    local goodCfg = game_scheme:Item_0(self.goodId)
    if goodCfg then
        self.name = goodCfg.nameKey
    else
        self.name = ""
        log.Error("goodCfg is nil, goodId = " .. self.goodId)
    end
    self.limitNum = data.LimitNumber         --限购数量
    self.curCount = data.count or 1        --初始兑换数量
    self.maxCanBuyNum = self.limitNum > math.floor(self.canSpendNum/self.price) and math.floor(self.canSpendNum/self.price) or self.limitNum
end

--初始化基础数据
function UIController:InitBaseData()
    local data = {
        priceIcon = self.priceIcon,
        name = self.name,
        canSpendNum = self.canSpendNum,
        price = self.price,
        goodId = self.goodId,
        goodNum = self.goodNum,
        count = self.curCount
    }

    self:TriggerUIEvent("InitBaseShow", data)
end

--刷新界面数据
function UIController:RefreshUI()
    local data = {
        count = self.curCount,
        canSpendNum = self.canSpendNum,
        price = self.price
    }
    self:TriggerUIEvent("RefreshUI", data)
end

function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--减少兑换数量
function  UIController:OnBtnReduceButtonClickedProxy()
    self.curCount = self.curCount - 1 > 1 and self.curCount - 1 or 1
    self:RefreshUI()
end

--增加兑换数量
function  UIController:OnBtnAddButtonClickedProxy()
    local count = self.curCount + 1
    if count * self.price > self.canSpendNum then
        --货币不足
        if math.floor(self.canSpendNum/self.price) == 0 then 
            count = 1
        else
            count = math.floor(self.canSpendNum/self.price)
        end
        self:FlowNotEnoughText()
    elseif count > self.limitNum then
        --超出限购数量
        count = self.limitNum
        flow_text.Add(string.format(lang.Get(1000940)))
    end
    self.curCount = count
    self:RefreshUI()
end

--货币不足提示
function UIController:FlowNotEnoughText()
    if self.priceType ~= 0 and self.priceType < 3 then
        flow_text.Add(lang.Get(100005 + self.priceType))
    else
        flow_text.Add(string.format(lang.Get(100009), lang.Get(self.priceName)))
    end
end

--输入兑换数量
function  UIController:OnInputCountInputFieldEndEdit(value)
    local count = tonumber(value)
    if count == nil then
        --没输入
        count = 1
    elseif count * self.price > self.canSpendNum then
        --货币不足
        if math.floor(self.canSpendNum/self.price) == 0 then 
            count = 1
            self:FlowNotEnoughText()
        else
            local canBuyCount = math.floor(self.canSpendNum/self.price)
            count = canBuyCount
            if canBuyCount >self.limitNum then 
                count = self.limitNum
            end
        end
    elseif count > self.limitNum then
        --超出限购数量
        count = self.limitNum
        flow_text.Add(string.format(lang.Get(1000940)))
    elseif count < 1 then
        --小于1
        count = 1
    end
    self.curCount = count
    self:RefreshUI()
end

--兑换
function  UIController:OnBtnBuyButtonClickedProxy()
    if self.curCount * self.price > self.canSpendNum then
        --货币不足
        self:FlowNotEnoughText()
        if self.OnClickCallBack then 
            self.OnClickCallBack()
        end
        return
    end
    if self.exchangeType == T_ExchangeTye.ActivityContent then
        local data = {
            AtyID = self.AtyID,
            ContentID = self.ActivityContentID,
            ExchangeNum = self.curCount
        }
        net_activity_module.MSG_FULLBATTLE_EXCHANGE_REQ(data)
    end
end



--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
