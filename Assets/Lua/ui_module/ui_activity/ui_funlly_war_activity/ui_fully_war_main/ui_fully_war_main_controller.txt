local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local tonumber 			= tonumber

local gw_recharge_mgr = require "gw_recharge_mgr"
local event = require "event"
local red_const = require "red_const"
local red_system = require "red_system"
local gw_task_const = require "gw_task_const"
local event_task_define = require "event_task_define"
local util = require "util"
local player_mgr = require "player_mgr"
local gw_event_activity_define = require "gw_event_activity_define"
local flow_text = require "flow_text"
local net_recharge_module = require "net_recharge_module"
local log = require "log"
local reward_mgr = require "reward_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local gw_fully_war_mgr = require "gw_fully_war_mgr"
local lang = require "lang"
local game_scheme = require "game_scheme"
local gw_task_mgr = require "gw_task_mgr"

--region Controller Life
module("ui_fully_war_main_controller")
local controller = nil
local UIController = newClass("ui_fully_war_main_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    if data == nil then
        return
    else
        self.AtyMainID = data.activityID    --主活动id
    end
    gw_fully_war_mgr.SetEnterFullyWarTime(self.AtyMainID)

    local atyCfg = game_scheme:ActivityMain_0(self.AtyMainID)
    if not atyCfg then
        log.Error("atyCfg is nil, activityMainID = " .. self.AtyMainID)
        return
    end
    self.atyIDList = atyCfg.bindActivity.data
    if not self.atyIDList then
        log.Error("without sub activity, activityMainID = " .. self.AtyMainID)
        return
    end
    self.isFirst = true
    self.isInitRechargeList = true
    self.selectPanelIndex = 1       --1为主界面，2为兑换商店
    self.rechargeList = {}
    self:TriggerUIEvent("UpdateExchangeTip", gw_fully_war_mgr.GetIsShowExchangeTip())
    
    self:SetBaseData()          --设置基础数据
    
    self:InitRechargeListData() --初始化礼包列表
    self:InitRewardBaseInfo()   --初始化礼券基础信息
    self:UpdateExchangeInfo()   --初始化兑换商店列表
    
end

function UIController:OnShow()
    self.__base.OnShow(self)
    --进入活动界面 打点
    local reportMsg =
    {
        Voucher_num = player_mgr.GetPlayerOwnNum(self.drawActivityCoinId) --拥有的礼券数量
    }
    event.EventReport("BattleExchange_Enter", reportMsg)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    gw_fully_war_mgr.SetEnterFullyWarTime(self.AtyMainID)
    self.CData = nil
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.AtyMainID)
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents() 
    local buyRecharge = function()
        --购买礼包
        self:UpdateRechargeInfo()
    end

    local exchangeUpdate = function()
        --刷新兑换商品界面数据
        self:UpdateExchangeInfo()
    end
    
    local updateGiftProgress = function(_,_, moduleId)
        if moduleId == gw_task_const.TaskModuleType.FullyWar or moduleId == -1 then
            --刷新礼券进度
            event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.AtyMainID)
            red_system.TriggerRed(red_const.Enum.FullyExchangeShop)
            self:UpdateRewardListData()
            self:UpdateExchangeInfo()
        end
    end

    self:RegisterEvent(event_task_define.REFRESH_TASK, updateGiftProgress)
    self:RegisterEvent(event.GW_REFRESH_RECHARGE_GOODS, buyRecharge)
    self:RegisterEvent(gw_event_activity_define.GW_FULL_SIEGE_EXCHANGE_SHOP_DATA_UPDATE,exchangeUpdate)
    
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
--获取根据类型获取对应的子活动id
function UIController:GetSubActivityIDByHeadingCode(headingCode)
    for i, v in pairs(self.atyIDList) do
        local atyCfg = game_scheme:ActivityMain_0(v)
        if atyCfg then
            if atyCfg.headingCode == headingCode then
                return v
            end
        end
    end
    return 0
end
--设置基础数据
function UIController:SetBaseData()
    local data = {}
    local activityCfg = game_scheme:ActivityMain_0(self.AtyMainID)
    if not activityCfg then
        return
    end
    local uiCfg = game_scheme:ActivityCommonUI_0(activityCfg.uiTemplateID)
    if not uiCfg then
        return
    end
    data.title = uiCfg.title
    data.title2 = uiCfg.title2
    data.bannerIcon = uiCfg.bannerIcon
    data.endTime = festival_activity_mgr.GetActivityDataByActivityID(self.AtyMainID).endTimeStamp or 0
    self.helpTips = uiCfg.helpTips
    data.selectPanelIndex = self.selectPanelIndex
    self:TriggerUIEvent("InitBaseInfo", data)
end

--初始化礼包列表数据
function UIController:InitRechargeListData()
    local data = {}
    data.selectIndex = 0
    self.rechargeList = self.rechargeList or {}
    if self.isInitRechargeList then
        self.isInitRechargeList = false
        local atyID = self:GetSubActivityIDByHeadingCode(104)
        if atyID == 0 then
            log.Error("without sub activity, activityMainID = " .. self.AtyMainID)
            return
        end
        local activityRechargeCfg = game_scheme:festivalActivity_0(atyID)
        if activityRechargeCfg then
            local contentIDList = activityRechargeCfg.ctnID1.data
            for i = 0, #contentIDList do
                local contentID = contentIDList[i]
                local contentCfg = game_scheme:ActivityContent_0(contentID)
                if contentCfg then
                    local recharge = {}
                    recharge.activityContentID = contentID
                    recharge.rechargeID = contentCfg.rechargeID.data[0]     --充值ID
                    local rechargeCfg = game_scheme:RechargeGoods_0(recharge.rechargeID)
                    if rechargeCfg then
                        recharge.rechargeIcon = rechargeCfg.PackageImage
                        recharge.multiples = string.format("%d%%", tonumber(rechargeCfg.GiftValue))
                        recharge.name = rechargeCfg.strGoodsNameID.data[0]  --礼包名称
                        recharge.remark = rechargeCfg.strGoodsNameID.data[1]  --切页描述
                        --奖励列表
                        local mustGetRewardIDList = rechargeCfg.rewardNew
                        recharge.mustGetRewardList = {}
                        for j = 0, mustGetRewardIDList.count do
                            local rewardID = mustGetRewardIDList.data[j]
                            recharge.mustGetRewardList = reward_mgr.GetRewardGoodsList(rewardID, recharge.mustGetRewardList)
                        end
                        local LotteryBoxID = rechargeCfg.LotteryBoxGroup
                        recharge.probabilityGetRewardList = {}
                        recharge.probabilityGetRewardList= reward_mgr.GetLotteryRandomRewardID(LotteryBoxID)
                    else
                        log.Error("rechargeCfg is nil, rechargeID = " .. recharge.rechargeID)
                        recharge.multiples = string.format("%d%%", 1)
                        recharge.rechargeIcon = ""
                    end
                    recharge.priceText = net_recharge_module.GetMoneyStrByGoodsID(recharge.rechargeID)
                    recharge.limitNum = contentCfg.LimitNumber
                    recharge.curCanBuyNum = recharge.limitNum - gw_recharge_mgr.GetRechargeBuyCount(recharge.rechargeID)
                    if recharge.curCanBuyNum > 0 and data.selectIndex == 0 then
                        data.selectIndex = i + 1
                        self:UpdateSelectIndex(data.selectIndex)
                    end
                    table.insert(self.rechargeList, recharge)
                end
            end
        else
            log.Error("activityRechargeCfg is nil, activityID = " .. atyID)
            return
        end 
    end
    if data.selectIndex == 0 then
        data.selectIndex = 1
    end
    data.rechargeList = self.rechargeList
    data.updateIndexFun = function(index)
        self:UpdateSelectIndex(index) 
    end 
    self:TriggerUIEvent("UpdateRechargeInfoAll", data)
end

--记录当前选中的礼包
function UIController:UpdateSelectIndex(index)
    self.selectRechargeListIndex = index
end

--刷新礼包信息
function UIController:UpdateRechargeInfo()
    local data = {}
    --data.selectIndex = 0
    data.selectIndex = self.selectRechargeListIndex
    data.updateIndexFun = function(index)
        self:UpdateSelectIndex(index)
    end
    for k, v in ipairs(self.rechargeList) do
        v.curCanBuyNum = v.limitNum - gw_recharge_mgr.GetRechargeBuyCount(v.rechargeID)
        --[[if v.curCanBuyNum > 0 and data.selectIndex == 0 then
            data.selectIndex = k
            self:UpdateSelectIndex(data.selectIndex)
        end]]
    end
    --[[if data.selectIndex == 0 then
        data.selectIndex = 1
    end]]
    data.rechargeList = self.rechargeList
    self:TriggerUIEvent("UpdateRechargeInfoAll", data)
end

--初始化礼券基础信息
function UIController:InitRewardBaseInfo()
    self.rewardList = {}
    local data = {}
    data.AtyMainID = self.AtyMainID
    local atyID = self:GetSubActivityIDByHeadingCode(101)
    if atyID == 0 then
        log.Error("without sub activity, activityMainID = " .. self.AtyMainID)
        return
    end
    local activityRechargeCfg = game_scheme:festivalActivity_0(atyID)
    if activityRechargeCfg then
        local rewardTaskIDList = activityRechargeCfg.ctnID1.data
        if not rewardTaskIDList then
            log.Error("rewardTaskIDList is nil, activityID = ", self.AtyMainID)
            return
        else
            data.rewardListCount = #rewardTaskIDList + 1
            for i = 0, #rewardTaskIDList do
                local reward = {}
                reward.taskID = rewardTaskIDList[i]
                local taskCfg = game_scheme:TaskMain_0(reward.taskID)
                if not taskCfg then
                    log.Error("taskCfg is nil, taskID = " .. reward.taskID)
                else
                    self.drawActivityCoinId = taskCfg.ConditionValue2.data[0]
                    gw_fully_war_mgr.SetVoucherId(self.drawActivityCoinId)
                    reward.progressNum = taskCfg.ConditionValue1
                    reward.rewardList = {}
                    reward.rewardList = reward_mgr.GetRewardGoodsList(taskCfg.TaskReward, reward.rewardList)
                    if reward.rewardList[1] then
                        reward.id = reward.rewardList[1].id
                        reward.num = reward.rewardList[1].num
                    else
                        reward.id = 1
                        reward.num = 1
                    end
                    local taskData = gw_task_mgr.GetTaskData(reward.taskID)
                    if not taskData then
                        log.Warning("taskData is nil, taskID = " .. reward.taskID)
                    end
                    reward.isGet = taskData and taskData.status or false
                    table.insert(self.rewardList, reward)
                end
            end
            local taskData = gw_task_mgr.GetTaskData(self.rewardList[#self.rewardList].taskID)
            data.rewardCount = taskData and taskData.rate or 0
            data.rewardItemList = self.rewardList

            data.GetRewardFun = function(index)
                if self.rewardList[index] then
                    local reward = self.rewardList[index]
                    local taskID = reward.taskID
                    local isGet = reward.isGet
                    rewardCount = gw_task_mgr.GetTaskData(self.rewardList[#self.rewardList].taskID).rate
                    local canGet = reward.progressNum <= rewardCount
                    if isGet then
                        flow_text.Add(lang.Get(1000917))     --奖励已领取
                    elseif canGet then
                        gw_task_mgr.ReceiveTaskReward(taskID, self.AtyMainID, true)
                    end
                end
            end
            
            data.value = self:UpdateRewardProgress(data.rewardCount, data.rewardItemList)
            data.icon = self:GetGiftIcon()
            self:TriggerUIEvent("InitGiftShow", data)
        end
    else
        log.Error("activityRechargeCfg is nil, activityID = " .. atyID)
        return
    end
end

--刷新奖励进度值
function UIController:UpdateRewardProgress(rewardCount,rewardItemList)
    local value = 0
    local num = rewardCount
    local curStageNum
    local curStageMaxNum
    for k, cur in ipairs(rewardItemList) do
        local last = rewardItemList[k - 1] and rewardItemList[k - 1].progressNum or 0
        if rewardCount >= cur.progressNum then
            value = value + 1/ #rewardItemList
        else
            --设置当前阶段进度
            curStageNum = cur.progressNum - num
            curStageMaxNum = rewardItemList[k + 1] and rewardItemList[k + 1].progressNum or cur
            --计算当前进度
            num = num - last
            local tmpValue = num/(cur.progressNum - last)
            value = value + tmpValue*(1 / #rewardItemList)
            break
        end
    end
    return value
end

--获取礼券icon
function UIController:GetGiftIcon()
    if not self.drawActivityCoinIcon then
        local itemCfg = game_scheme:Item_0(self.drawActivityCoinId)
        if not itemCfg then
            return 1
        else
            self.drawActivityCoinIcon = itemCfg.icon
        end
    end
    return self.drawActivityCoinIcon
end

--刷新奖励列表数据
function UIController:UpdateRewardListData()
    local data = {}
    data.rewardCount = gw_task_mgr.GetTaskData(self.rewardList[#self.rewardList].taskID).rate
    data.rewardItemList = self.rewardList
    for k, reward in ipairs(self.rewardList)  do
        local taskData = gw_task_mgr.GetTaskData(reward.taskID)
        if not taskData then
            log.Warning("taskData is nil, taskID = " .. reward.taskID)
        end
        reward.isGet = taskData and taskData.status or false
    end
    data.value = self:UpdateRewardProgress(data.rewardCount, data.rewardItemList)
    self:TriggerUIEvent("UpdateGiftProgress", data)
end

--刷新兑换界面数据
function UIController:UpdateExchangeInfo()
    self.exchangeGoodList = self.exchangeGoodList or {}
    local data = {}
    data.icon = self:GetGiftIcon()
    self.exchangeAtyID = self:GetSubActivityIDByHeadingCode(103)
    if self.exchangeAtyID == 0 then
        log.Error("without sub activity, activityMainID = " .. self.exchangeAtyID)
        return
    end
    if self.isFirst then
        --初始化
        self.isFirst = false
        local activityRechargeCfg = game_scheme:festivalActivity_0(self.exchangeAtyID)
        if activityRechargeCfg then
            local contentIDList = activityRechargeCfg.ctnID1.data
            data.goodsList = {}
            for i = 0, #contentIDList do
                local contentID = contentIDList[i]
                local contentCfg = game_scheme:ActivityContent_0(contentID)
                if contentCfg then
                    local good = {}
                    good.isLimitDaily = contentCfg.RefreshType == 1           --是否每日限购
                    good.hotFlag = contentCfg.hotFlag                --左上角标签
                    good.LimitNumber = contentCfg.LimitNumber or 0        --限购数量
                    good.contentID = contentID
                    local arr = util.SplitString(contentCfg.expenditure, "#")
                    good.priceId = tonumber(arr[1])      --花费物品的itemId
                    good.price = tonumber(arr[2])
                    local priceCfg = game_scheme:Item_0(good.priceId)
                    if priceCfg then
                        good.priceIcon = priceCfg.icon
                        good.priceName = priceCfg.nameKey
                    else
                        good.priceIcon = 1
                        log.Error("priceCfg is nil, priceId = " .. priceId)
                    end
                    local rewardID = util.SplitString(contentCfg.rewardGroup, "#")[1]
                    local rewardList = {}
                    rewardList = reward_mgr.GetRewardGoodsList(rewardID, rewardList)
                    if rewardList[1] then
                        good.id = rewardList[1].id     --物品id
                        good.num = rewardList[1].num   --数量
                    else
                        good.id = 1                         --物品id
                        good.num = 1                       --数量
                    end
                    table.insert(data.goodsList, good)
                else
                    log.Error("contentCfg is nil, activityMainID = " .. self.exchangeAtyID)
                end
            end

        end
        data.goodsList = self:SortGoodsList(data.goodsList)
    else
        data.goodsList = self:SortGoodsList(self.exchangeGoodList)
    end
    data.ExchangeFun = function(good)
       surplusNum = gw_fully_war_mgr.GetExchangeInfoByID(self.exchangeAtyID, good.contentID).residueNum
        if player_mgr.GetPlayerOwnNum(good.priceId) >= good.price and surplusNum > 0 then
            --可以兑换
            local goodData = {
                AtyID = self.exchangeAtyID,
                ActivityContentID = good.contentID,
                exchangeType = 1,
                LimitNumber = good.surplusNum
            }
            ui_window_mgr:ShowModule("ui_general_exchange_shop",nil,nil,goodData)
        elseif player_mgr.GetPlayerOwnNum(good.priceId) < good.price then
            --礼券不足
            flow_text.Add(string.format(lang.Get(100009), lang.Get(good.priceName)))
        elseif surplusNum <= 0 then
            --已经购买完限购次数
            flow_text.Add(string.format(lang.Get(1000940)))
        end
        
    end
    self.exchangeGoodList = data.goodsList
    self.exchangeGoodList = data.goodsList
    data.num = player_mgr.GetPlayerOwnNum(self.drawActivityCoinId)      --礼券数量
    data.atyMainID = self.AtyMainID
    self:TriggerUIEvent("UpdateExchangeShow", data)
end

--商品列表排序
function UIController:SortGoodsList(goodsList)
    local canBuyLimitDaily = {}
    local cannotBuyLimitDaily = {}
    local canBuyNormal = {}
    local cannotBuyNormal = {}
    local finalList = {}
    for k, v in ipairs(goodsList) do
        v.surplusNum = gw_fully_war_mgr.GetExchangeInfoByID(self.exchangeAtyID, v.contentID).residueNum        --剩余数量
        if v.isLimitDaily then
            --是每日限购类型
            if v.surplusNum == 0 then
                --已经购买完限购次数
                table.insert(cannotBuyLimitDaily, v)
            else
                table.insert(canBuyLimitDaily, v)
            end
        else
            --活动限购类型
            if v.surplusNum == 0 then
                --已经购买完限购次数
                table.insert(cannotBuyNormal, v)
            else
                table.insert(canBuyNormal, v)
            end
        end
    end
    for k, v in ipairs(canBuyLimitDaily) do
        table.insert(finalList, v)
    end
    for k, v in ipairs(cannotBuyLimitDaily) do
        table.insert(finalList, v)
    end
    for k, v in ipairs(canBuyNormal) do
        table.insert(finalList, v)
    end
    for k, v in ipairs(cannotBuyNormal) do
        table.insert(finalList, v)
    end
    return finalList
end

--活动规则提示
function  UIController:OnBtnTipBtnClickedProxy()
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(self.helpTips)
end

--购买礼包
function  UIController:OnBtnBuyRechargeBtnClickedProxy()
    local rechargeInfo = self.rechargeList[self.selectRechargeListIndex]
    gw_recharge_mgr.RequestBuyRecharge(rechargeInfo.rechargeID, rechargeInfo.activityContentID)
    if self.drawActivityCoinId then
        gw_fully_war_mgr.SetOldVoucherNum(player_mgr.GetPlayerOwnNum(self.drawActivityCoinId))
    end
end

--兑换提示
function  UIController:OnTogExchangeTipSwitchValueChange(state)
    gw_fully_war_mgr.SetIsShowExchangeTip(state)
    red_system.TriggerRed(red_const.Enum.FullyExchangeShop)
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.AtyMainID)
    self:TriggerUIEvent("UpdateExchangeTip", state)
end

--切换主页
function  UIController:OnTogBuyMainValueChange(state)
    if state then
        self:UpdateRechargeInfo()
        self.selectPanelIndex = 1
        self:TriggerUIEvent("ChangeUIType", self.selectPanelIndex)
        self:UpdateRechargeInfo()
    end
end
--切换兑换
function  UIController:OnTogExchangeMainValueChange(state)
    if state then
        self.selectPanelIndex = 2
        self:TriggerUIEvent("ChangeUIType", self.selectPanelIndex)
    end
end

function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
