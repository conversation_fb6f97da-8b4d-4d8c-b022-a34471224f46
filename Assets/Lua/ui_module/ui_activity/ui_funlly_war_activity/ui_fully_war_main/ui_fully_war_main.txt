local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local type = type
local typeof = typeof
local GameObject = CS.UnityEngine.GameObject
local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
local red_const = require "red_const"
local item_data = require "item_data"
local net_login_module = require "net_login_module"
local Common_Util = CS.Common_Util.UIUtil
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_fully_war_main_binding"
local GWAssetMgr = require "gw_asset_mgr"
local goods_item = require "goods_item_new"
local sprite_Asset = require"card_sprite_asset"
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local Canvas                    = CS.UnityEngine.Canvas

--region View Life
module("ui_fully_war_main")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self.VData = {}
    self.GWActivityCommonAsset = self.GWActivityCommonAsset or sprite_Asset:CreateGWActivityCommonAsset()
    self.tog_BuyMain.isOn = true
    self.isInitRed = true
    self.RechargeIconAsset = self.RechargeIconAsset or sprite_Asset:CreateGWRechargeIcon()
end

function UIView:OnShow()
    self.__base.OnShow(self)
    self.rtf_rewardBoxEffect:GetComponent(typeof(SortingGroup)).sortingOrder = self.curOrder + 1
    self.img_RechargeIcon:GetComponent(typeof(Canvas)).sortingOrder = self.curOrder + 2
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    if self.RechargeIconAsset then
        self.RechargeIconAsset:Dispose()
        self.RechargeIconAsset = nil
    end
    if self.mustGetRewardList then
        for k, v in ipairs(self.mustGetRewardList) do
            if v and v.good then
                v.good:Dispose()
                v = nil
            end
        end
    end

    if self.probabilityGetRewardList then
        for k, v in ipairs(self.probabilityGetRewardList) do
            if v and v.good then
                v.good:Dispose()
                v = nil
            end
        end
    end

    if self.exchangeGoodList then
        for k, v in ipairs(self.exchangeGoodList) do
            if v then
                if v.item then
                    local ExchangeBtn = v.item:Get("ExchangeBtn")
                    ExchangeBtn.onClick:RemoveAllListeners()
                end
                if v.goodsItem then
                    v.goodsItem:Dispose()
                    v.goodsItem = nil
                end
            end
        end
    end

    if self.rewardItemList then
        for k, v in ipairs(self.rewardItemList) do
            if v then
                if v.item then
                    local GetBtn = v.item:Get("GetBtn")
                    GetBtn.onClick:RemoveAllListeners()
                end
                if v.goodsItem then
                    v.goodsItem:Dispose()
                    v.goodsItem = nil
                end
            end
        end
    end

    if self.rechargeToggleList then
        for k, v in ipairs(self.rechargeToggleList) do
            if v then
                local toggle = v:Get("toggle")
                toggle.onValueChanged:RemoveAllListeners()
            end
        end
    end

    if self.GWActivityCommonAsset then
        self.GWActivityCommonAsset:Dispose()
        self.GWActivityCommonAsset = nil
    end

    self.__base.Close(self)
end
--endregion

--region View Logic

--设置基础显示（只要初始化一次）
function UIView:InitBaseInfo(data)
    self.txt_TitleText.text = lang.Get(data.title)
    self.txt_TipText.text = lang.Get(data.title2)
    self.GWActivityCommonAsset:GetSprite(data.bannerIcon,function(sprite)
        if sprite then
            self.img_banner.sprite = sprite
        end
    end)
    
    local curTime = data.endTime - net_login_module.GetServerTime()
    if curTime > 0 then
        self:CreateTimer(1, function()
            local time = data.endTime - net_login_module.GetServerTime()
            if time > 0 then
                local text = util.GetDayCountDown(time)
                self.txt_TimeText.text = text
            else
                self.txt_TimeText.text = util.GetDayCountDown(0)
            end
        end)
    else
        self.txt_TimeText.text = util.GetDayCountDown(0)
    end
    self.tog_BuyMain.isOn = data.selectPanelIndex == 1 --默认显示主界面
    self.tog_ExchangeMain.isOn = data.selectPanelIndex == 2
    --self:BindUIRed(self.tog_BuyMain.transform,red_const.Enum.FullyMain, nil, { redPath = red_const.Type.Default })
end

--刷新全部礼包内容
function UIView:UpdateRechargeInfoAll(data)
    local rechargeToggleList = self.rechargeToggleList or {}
    for k, v in ipairs(data.rechargeList) do
        local tmp = rechargeToggleList[k] or GameObject.Instantiate(self.rtf_RechargeItem, self.rtf_RechargeItem.transform.parent).transform:GetComponent("ScrollRectItem")
        local toggle = tmp:Get("toggle")
        local normal_txt_Title = tmp:Get("normal_txt_Title")
        local select_txt_Title = tmp:Get("select_txt_Title")
        local selectToggleCanvas = tmp:Get("selectToggleCanvas")
        selectToggleCanvas.sortingOrder = self.curOrder + 1
        normal_txt_Title.text = lang.Get(v.remark)
        select_txt_Title.text = lang.Get(v.remark)
        toggle.isOn = data.selectIndex == k
        selectToggleCanvas.enabled = data.selectIndex == k
        toggle.onValueChanged:RemoveAllListeners()
        toggle.onValueChanged:AddListener(function(value)
            if value then
                data.updateIndexFun(k)
                self:UpdateRechargeInfo(v)
            end
            selectToggleCanvas.enabled = value
        end)
        Common_Util.SetActive(tmp, true)
        rechargeToggleList[k] = tmp
    end
    self.rechargeToggleList = rechargeToggleList
    self:UpdateRechargeInfo(data.rechargeList[data.selectIndex])
end

--刷新礼包详细信息显示
function UIView:UpdateRechargeInfo(data)
--[[    if data.selectIndex and data.selectIndex ~= 0 then
        self.rechargeToggleList[data.selectIndex]:Get("toggle").isOn = true
    end]]
    gw_totalrecharge_mgr.SetRechargeScore(data.rechargeID,self.rtf_ComRechargeScore,self)
    local mustGetRewardList = self.mustGetRewardList or {}
    mustGetRewardList = self:UpdateRechargeRewardShow(data.mustGetRewardList, mustGetRewardList, self.rtf_MustGetGood)
    self.mustGetRewardList = mustGetRewardList
    
    local probabilityGetRewardList = self.probabilityGetRewardList or {}
    probabilityGetRewardList = self:UpdateRechargeRewardShow(data.probabilityGetRewardList, probabilityGetRewardList, self.rtf_ProbabilityGetGood)
    self.probabilityGetRewardList = probabilityGetRewardList
    
    self.txt_LimitNum.text = string.format2(lang.Get(1000907), data.curCanBuyNum, data.limitNum) --今日限购[%s]/[%s]
    self.txt_MultiplesNum.text = data.multiples
    self.txt_RechargeName.text = lang.Get(data.name)
    self.RechargeIconAsset:GetSprite(data.rechargeIcon, function(sprite)
        if sprite then
            self.img_RechargeIcon.sprite = sprite
        end
    end)
    local btnGray = self.btn_BuyRechargeBtn:GetComponent("ImageGray")
    btnGray:SetEnable(data.curCanBuyNum == 0)
    if data.curCanBuyNum == 0 then
        self.txt_rechargePriceText.text = lang.Get(1000916) --今日已售罄
    else
        self.txt_rechargePriceText.text = data.priceText
    end
    
end

--设置奖励列表显示
function UIView:UpdateRechargeRewardShow(data, rewardList, itemTransform)
    for k, v in ipairs(rewardList) do
        if v then
            Common_Util.SetActive(v.item, false)
        end
    end
    for k, v in ipairs(data) do
        local tmp = rewardList[k] or {}
        local item = tmp and tmp.item or GameObject.Instantiate(itemTransform, itemTransform.parent)
        local good = tmp and tmp.good or goods_item.CGoodsItem()
        good:Init(item, nil, 0.7)
        good:SetGoods(nil, v.id, v.num, function()
            local iui_item_detail = require "iui_item_detail"
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil, v.rewardid)
        end)
        Common_Util.SetActive(item, true)
        tmp.item = item
        tmp.good = good
        rewardList[k] = tmp
    end
    return rewardList
end

--设置礼券基础显示
function UIView:InitGiftShow(data)
    GWAssetMgr:LoadGoodsIcon(data.icon, function(sprite)
        if not util.IsObjNull(self.img_GiftCertificatesIcon) and sprite then
            self.img_GiftCertificatesIcon.sprite = sprite
        end
    end)
    local rewardItemList = self.rewardItemList or {}
    for k, v in ipairs(data.rewardItemList) do
        local tmp = rewardItemList[k] or {}
        local item = tmp and tmp.item or GameObject.Instantiate(self.scrItem_RewardItem, self.scrItem_RewardItem.transform.parent).transform:GetComponent("ScrollRectItem")
        local BG = self.rtf_ProgressFill.parent
        item.transform.sizeDelta = {x = BG.transform.sizeDelta.x / #data.rewardItemList, y = item.transform.sizeDelta.y}
        Common_Util.SetActive(item, true)
        local ProgressNum = item:Get("ProgressNum")
        ProgressNum.text = v.progressNum
        local EndImage = item:Get("EndImage")
        Common_Util.SetActive(EndImage, data.rewardListCount > k)
        
        local GoodItem = item:Get("GoodItem")
        local goodsItem = tmp and tmp.goodsItem or goods_item.CGoodsItem()
        goodsItem:Init(GoodItem, nil, 0.6)
        goodsItem:SetGoods(nil, v.id, v.num,function()
            local iui_item_detail = require "iui_item_detail"
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil, v.rewardid)
        end)
        goodsItem:SetCountEnable(true)
        
        local GetBtn = item:Get("GetBtn")
        GetBtn.onClick:AddListener(function()
            data.GetRewardFun(k)
        end)
        self:BindUIRed(GoodItem.transform,red_const.Enum.FullyProgressReward, {data.AtyMainID, v.taskID}, { redPath = red_const.Type.Default })

        tmp.item = item
        tmp.goodsItem = goodsItem
        rewardItemList[k] = tmp
    end
    self.rewardItemList = rewardItemList
    self:UpdateGiftProgress(data)
end

--刷新礼券进度,状态
function UIView:UpdateGiftProgress(data)
    for k, v in ipairs(data.rewardItemList) do
        local item = self.rewardItemList[k].item
        if item then
            local CanGet = item:Get("CanGet")
            local HasGet = item:Get("HasGet")
            Common_Util.SetActive(CanGet, v.progressNum <= data.rewardCount and not v.isGet)
            Common_Util.SetActive(HasGet, v.isGet)
        end
    end
    self.txt_AllGiftCertificatesNum.text = data.rewardCount
    self.rtf_ProgressFill.anchoredPosition = {x = self.rtf_ProgressFill.sizeDelta.x * data.value, y = 0, z = 0}
end


--切换界面类型
function UIView:ChangeUIType(ShowType)
    --1为主界面，2为兑换界面
    Common_Util.SetActive(self.rtf_BuyMainShow, ShowType == 1)
    Common_Util.SetActive(self.rtf_ExchangeMainShow, ShowType == 2)
end

--刷新兑换界面提示显示
function UIView:UpdateExchangeTip(isShow)
    self.tog_ExchangeTipSwitch.isOn = isShow
end

--刷新兑换界面显示
function UIView:UpdateExchangeShow(data)
    if self.isInitRed then
        self.isInitRed = false
        self:BindUIRed(self.tog_ExchangeMain.transform,red_const.Enum.FullyExchangeShop, {data.atyMainID}, { redPath = red_const.Type.Default })
    end
    GWAssetMgr:LoadGoodsIcon(data.icon, function(sprite)
        if sprite then
            self.img_CertificatesIcon.sprite = sprite
        end
    end)
    self.txt_GiftCertificatesNum.text = data.num
    local exchangeGoodList = self.exchangeGoodList or {}

    for k, v in ipairs(exchangeGoodList) do
        if v then
            Common_Util.SetActive(v.item, false)
        end
    end

    for k, v in ipairs(data.goodsList) do
        local tmp = exchangeGoodList[k] or {}
        local item = tmp and tmp.item or GameObject.Instantiate(self.scrItem_ExchangeGoodItem, self.scrItem_ExchangeGoodItem.transform.parent).transform:GetComponent("ScrollRectItem")
        Common_Util.SetActive(item, true)
        local BG = item:Get("BG")
        local GreatTip = item:Get("GreatTip")
        local SurplusNumText = item:Get("SurplusNumText")
        local PriceText = item:Get("PriceText")
        local PriceIcon = item:Get("PriceIcon")
        local ExchangeBtn = item:Get("ExchangeBtn")
        local ExchangeBtnGray = item:Get("ExchangeBtnGray")
        local good = item:Get("good")
        local BuyOver = item:Get("BuyOver") 
        
        BG:Switch(v.hotFlag == 3 and 1 or 0)
        Common_Util.SetActive(GreatTip, v.hotFlag == 3)
        
        if v.isLimitDaily then
            --是否每日限购
            SurplusNumText.text = string.format2(lang.Get(1000914), v.surplusNum)		--今日剩余[%s]
        else
            SurplusNumText.text = string.format2(lang.Get(1000915), v.surplusNum)		--活动剩余[%s]
        end
        ExchangeBtnGray:SetEnable(v.surplusNum > 0)
        Common_Util.SetActive(BuyOver, v.surplusNum == 0)
        ExchangeBtn.onClick:RemoveAllListeners()
        ExchangeBtn.onClick:AddListener(function()
            data.ExchangeFun(v)
        end)
        local priceColor = "#ffffff"
        if v.price > data.num then
            priceColor = "#E95959"
        end
        PriceText.text = "<color="..priceColor..">"..v.price .. "</color>"
        GWAssetMgr:LoadGoodsIcon(v.priceIcon, function(sprite)
            if sprite then
                PriceIcon.sprite = sprite
            end
        end)
        local goodsItem = tmp and tmp.goodsItem or goods_item.CGoodsItem()
        goodsItem:Init(good.transform, nil, 0.6)
        goodsItem:SetGoods(nil, v.id, v.num,function()
            local iui_item_detail = require "iui_item_detail"
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil, v.rewardid)
        end)
        goodsItem:SetCountEnable(true)
        tmp.item = item
        tmp.goodsItem = goodsItem
        exchangeGoodList[k] = tmp
    end
    self.exchangeGoodList = exchangeGoodList
end



--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, true, nil, nil, nil, true)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, nil, true, nil, nil, nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
