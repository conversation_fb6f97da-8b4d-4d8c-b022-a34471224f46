--全面备战数据获取

local require   = require
local ipairs    = ipairs
local pairs     = pairs
local table     = table
local string    = string
local tonumber  = tonumber
local gw_task_mgr = require "gw_task_mgr"
local event_task_define = require "event_task_define"
local gw_task_const = require "gw_task_const"
local player_prefs = require "player_prefs"
local red_const = require "red_const"
local red_system = require "red_system"
local math = require "math"
local util = require "util"
local game_scheme = require "game_scheme"
local os = require "os"
local player_mgr = require "player_mgr"
local log = require "log"
local event         = require "event"
local gw_event_activity_define = require "gw_event_activity_define"
module("gw_fully_war_mgr")

local ExchangeInfo = {}     --兑换商品信息
local fullyWarRechargeInfo = {}     --全面备战礼包信息
local isShowExchangeTip = true
local mainAtyIDList = {}
local progressRewardList = {}
local oldVoucherNum = 0 --上次礼券数量
local voucherId = 0    --礼券ID

--每日进入全面备战界面时间
local GW_ENTER_FULLY_WAR_ACTIVITY_TIME = "GW_ENTER_FULLY_WAR_ACTIVITY_TIME"

--设置活动ID列表
function SetMainAtyIDList(activityID)
    if mainAtyIDList[activityID] then
        return
    end
    mainAtyIDList[activityID] = 1
end

--设置每日进入全面备战时间
function SetEnterFullyWarTime(activityID)
    local dbid = player_mgr.GetPlayerRoleID()
    local str = string.format("%d#%d", os.server_time() , activityID)
    local const  = string.format("%s_%d", GW_ENTER_FULLY_WAR_ACTIVITY_TIME, dbid)
    player_prefs.SetString(const, str)
end

--获取礼包红点
function GetRechargeRed(activityID)
    local dbid = player_mgr.GetPlayerRoleID()
    local const  = string.format("%s_%d", GW_ENTER_FULLY_WAR_ACTIVITY_TIME, dbid)
    local str = player_prefs.GetString(const, '')
    if not string.empty(str) then
        local arr = string.split(str, "#")
        if #arr == 2 and tonumber(arr[1]) ~= 0 and tonumber(arr[2]) == activityID then
            -- 同一个账号，同一个活动
            return util.IsAfterDay(tonumber(arr[1]), os.server_time()) and 1 or 0
        end
    end
    return 1
end

--跨天刷新主界面红点
function CrossDay()
    for k, v in pairs(mainAtyIDList) do
        if v then
            event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,k)
        end
    end
end

--[[--获取进度奖励红点
function GetProgressRewardRed(AtyMainID, taskID)
    local hasRed = 0
    if progressRewardList[AtyMainID] then
        for k,v in ipairs (progressRewardList[AtyMainID]) do
            local taskData = gw_task_mgr.GetTaskData(v.taskID)
            if taskData then
                if not taskData.status and taskData.rate >= v.progressNum then
                    if taskID and taskID == v.taskID then
                        return 1
                    end
                    hasRed = hasRed + 1
                end
            else
                log.Error("taskData is nil, taskID = " .. v.taskID)
            end
        end
    else
        progressRewardList[AtyMainID] = {}
        local ArtID = GetSubActivityID(101,AtyMainID)
        local cfg = game_scheme:festivalActivity_0(ArtID)
        if cfg then
            for i = 0, cfg.ctnID1.count - 1 do
                local taskID = cfg.ctnID1.data[i]
                local progressReward = {}
                progressReward.taskID = taskID
                local taskCfg = game_scheme:TaskMain_0(taskID)
                if not taskCfg then
                    log.Error("taskCfg is nil, taskID = " .. taskID)
                else
                    progressReward.progressNum = taskCfg.ConditionValue1
                    table.insert(progressRewardList[AtyMainID], progressReward)
                    local taskData = gw_task_mgr.GetTaskData(taskID)
                    if taskData then
                        if not taskData.status and taskData.rate >= progressReward.progressNum then
                            hasRed = hasRed + 1
                        end
                    else
                        log.Error("taskData is nil, taskID = " .. taskID)
                    end
                end
            end
        end
    end
    if taskID then
        return 0
    end
    return hasRed
end]]

-- 获取进度奖励红点
function GetProgressRewardRed(AtyMainID, taskID)
    local hasRed = 0
    if progressRewardList[AtyMainID] then
        for k, v in ipairs(progressRewardList[AtyMainID]) do
            local taskData = gw_task_mgr.GetTaskData(v.taskID)
            if taskData then
                if not taskData.status and taskData.rate >= v.progressNum then
                    if taskID and taskID == v.taskID then
                        return 1
                    end
                    hasRed = hasRed + 1
                end
            else
                log.Warning("taskData is nil, taskID = " .. v.taskID)
            end
        end
    else
        progressRewardList[AtyMainID] = {}
        local ArtID = GetSubActivityID(101, AtyMainID)
        local cfg = game_scheme:festivalActivity_0(ArtID)
        if cfg then
            for i = 0, cfg.ctnID1.count - 1 do
                local taskID = cfg.ctnID1.data[i]
                local progressReward = {
                    taskID = taskID,
                    progressNum = game_scheme:TaskMain_0(taskID) and game_scheme:TaskMain_0(taskID).ConditionValue1 or 0
                }
                table.insert(progressRewardList[AtyMainID], progressReward)
                local taskData = gw_task_mgr.GetTaskData(taskID)
                if taskData and not taskData.status and taskData.rate >= progressReward.progressNum then
                    hasRed = hasRed + 1
                end
            end
        end
    end
    if taskID then
        return 0
    end
    return hasRed
end

--刷新进度奖励红点
function UpdateProgressRewardRed()
    for k, v in pairs(mainAtyIDList) do
        if v then
            red_system.TriggerRed(red_const.Enum.FullyProgressReward)
            event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,k)
        end
    end
end

--刷新兑换界面红点
function UpdateExchangeRed(_,_, moduleId)
    if moduleId == gw_task_const.TaskModuleType.FullyWar or moduleId == -1 then
        UpdateProgressRewardRed()
        red_system.TriggerRed(red_const.Enum.FullyExchangeShop)
    end
end

--获取兑换界面红点
function GetExchangeShopEnterRed(AtyMainID)
    if not isShowExchangeTip then
        --不显示兑换提示
        return 0
    end

    local priceId = 0
    local priceNum = 0
    local hasRed = 0
    local ArtID = GetSubActivityID(103, AtyMainID)

    if ExchangeInfo[ArtID] then
        for k, v in pairs(ExchangeInfo[ArtID]) do
            if v.residueNum > 0 then
                local cfg = v.cfg
                if not cfg then
                    cfg = game_scheme:ActivityContent_0(k)
                end
                if cfg then
                    local arr = util.SplitString(cfg.expenditure, "#")
                    if priceId == 0 then
                        priceId = tonumber(arr[1])      --花费物品的itemId
                        priceNum = player_mgr.GetPlayerOwnNum(priceId)
                    end
                    local price = tonumber(arr[2])
                    local canBuyNum = math.floor(priceNum / price)
                    hasRed = canBuyNum > 0 and 1 or hasRed
                    if hasRed == 1 then
                        break
                    end
                    v.cfg = cfg
                end
            end
        end
    end
    return hasRed
end


--获取对应子活动id通过headingCode和主活动id
function GetSubActivityID(headingCode, AtyMainID)
    if AtyMainID then
        local atyMainCfg = game_scheme:ActivityMain_0(AtyMainID)
        if not atyMainCfg then
            log.Warning("atyCfg is nil, activityMainID = " .. AtyMainID)
            return 0
        end
        local atyIDList = atyMainCfg.bindActivity.data
        if atyIDList[0] ~= 1 then
            ArtID = AtyMainID
        else
            for k, v in pairs(atyIDList) do
                local atyCfg = game_scheme:ActivityMain_0(v)
                if atyCfg then
                    if atyCfg.headingCode == headingCode then
                        return v
                    end
                end
            end
            return 0
        end
    else
        log.Warning("AtyMainID is nil")
        return 0
    end
end


function Init()
    event.Register(event_task_define.REFRESH_TASK, UpdateExchangeRed)         --刷新兑换红点
    --red_system.RegisterRedFunc(red_const.Enum.FullyMain, GetEnterMainRed)  --获取主界面红点
    red_system.RegisterRedFunc(red_const.Enum.FullyExchangeShop, GetExchangeShopEnterRed)  --获取兑换界面红点
    red_system.RegisterRedFunc(red_const.Enum.FullyProgressReward, GetProgressRewardRed)  --获取进度奖励红点
    event.Register(event.SERVER_CROSS_DAY, CrossDay)        --跨天红点
    
end


--设置是否开启兑换提示
function SetIsShowExchangeTip(isShow)
    isShowExchangeTip = isShow
end

--获取是否开启兑换提示
function GetIsShowExchangeTip()
    return isShowExchangeTip
end


---@deprecated 刷新兑换数据
function UpdateExchangeInfo(msg)
    local list = ExchangeInfo[msg.AtyID] or {}
    for k, v in ipairs(msg.exchangeInfos) do
        local item = list[v.ContentID] or {}
        item.residueNum = v.residueNum
        item.exchangeNum = v.ExchangeNum
        list[v.ContentID] = item
    end
    ExchangeInfo[msg.AtyID] = list
    event.Trigger(gw_event_activity_define.GW_FULL_SIEGE_EXCHANGE_SHOP_DATA_UPDATE, msg)
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,msg.AtyID)
    red_system.TriggerRed(red_const.Enum.FullyExchangeShop)
end

---@deprecated 刷新礼包数据
function UpdateRechargeInfo(msg)
    for k, v in ipairs(msg.goods) do
        fullyWarRechargeInfo[v.ActivityContentId] = v.goodsNum
    end
    event.Trigger(gw_event_activity_define.GW_FULL_SIEGE_GIFT_DATA_UPDATE)
    --获得礼券打点
    if voucherId~=0 then
        local getNum = player_mgr.GetPlayerOwnNum(voucherId) - oldVoucherNum
        local reportMsg =
        {
            headingCode_id = msg.AtyID, --活动ID
            get_Voucher_num = getNum, --本次获得的礼券数量
        }
        event.EventReport("BattleExchange_getVoucher", reportMsg)
    end
end


---@deprecated 获取兑换商品数据
function GetExchangeInfoByID(artID, ActivityContentID)
    if not ActivityContentID or not artID then
        log.Warning("param error, please check", artID, ActivityContentID)
        return {residueNum = 0, exchangeNum = 0}
    end
    local list = ExchangeInfo[artID]
    if list then
        if list and list[ActivityContentID] then
            return list[ActivityContentID]
        end
    else
        log.Warning("ExchangeInfo is nil", ActivityContentID)
    end
    
    return {residueNum = 0, exchangeNum = 0}
end

---@deprecated 获取礼包数据
function GetRechargeInfoByID(ActivityContentID)
    if not ActivityContentID then
        return 0
    end
    if fullyWarRechargeInfo and fullyWarRechargeInfo[ActivityContentID] then
        return fullyWarRechargeInfo[ActivityContentID]
    end
    return 0
end

---@deprecated 存储兑换商品数据
function SetOldVoucherNum(num)
    oldVoucherNum = num
end

---@deprecated 存储兑换商品数据
function SetVoucherId(ID)
    voucherId = ID
end

--清除数据
function ClearData()
    ExchangeInfo = {}
    fullyWarRechargeInfo = {}
    isShowExchangeTip = true
    mainAtyIDList = {}
    progressRewardList = {}
    oldVoucherNum = 0
end

Init()

event.Register(event.USER_DATA_RESET, ClearData)