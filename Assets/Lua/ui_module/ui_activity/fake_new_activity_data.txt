---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
--- DateTime: 2024/11/7 21:41
---@class FakeData
local FakeNewActivityData = {}

FakeNewActivityData.activities = {
    --故意弄俩个来切换的
    --【测试】无任务战令
    --{
    --    topicID = 1413,--伪造的一个topicID
    --    activityID = 2003, --你的活动ID
    --    headingCode = 1003, --活动的headingCode
    --    versionNumber = 0,
    --    activityCodeType = 1003,--活动的枚举，最新活动已经改成和headingCode一致了
    --    parentsCode = 0,--父活动的headingCode
    --    parentsVersion = 0,
    --    actType = 1,--活动归属，主要用于管理入口
    --    popui = 0
    --},
    {
        topicID = 1413,
        activityID = 2010,
        headingCode = 201,
        versionNumber = 0,
        activityCodeType = 201,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    --先锋目标
    {
        topicID = 1413, --伪造的一个topicID
        activityID = 2005, --你的活动ID
        headingCode = 1004, --活动的headingCodezyao
        versionNumber = 0,
        activityCodeType = 1004, --活动的枚举，最新活动已经改成和headingCode一致了
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    --先锋指挥官
    {
        topicID = 1413, --伪造的一个topicID
        activityID = 2006, --你的活动ID
        headingCode = 1005, --活动的headingCode
        versionNumber = 0,
        activityCodeType = 1005, --活动的枚举，最新活动已经改成和headingCode一致了
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    --军备竞赛
    {
        topicID = 1413, --伪造的一个topicID
        activityID = 2011, --你的活动ID
        headingCode = 202, --活动的headingCode
        versionNumber = 0,
        activityCodeType = 2011, --活动的枚举，最新活动已经改成和headingCode一致了
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    --英雄试炼
    {
        topicID = 1416, --伪造的一个topicID
        activityID = 2009, --你的活动ID
        headingCode = 101, --活动的headingCode
        versionNumber = 0,
        activityCodeType = 101, --活动的枚举，最新活动已经改成和headingCode一致了
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    -- 战令模块
    {
        topicID = 1908,
        activityID = 2001,
        headingCode = 1001,
        versionNumber = 0,
        activityCodeType = 201,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    {
        topicID = 1908,
        activityID = 2002,
        headingCode = 1002,
        versionNumber = 0,
        activityCodeType = 201,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    {
        topicID = 1908,
        activityID = 2003,
        headingCode = 1003,
        versionNumber = 0,
        activityCodeType = 201,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    {
        topicID = 1908,
        activityID = 2004,
        headingCode = 1004,
        versionNumber = 0,
        activityCodeType = 201,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    {
        topicID = 1413,
        activityID = 1018,
        headingCode = 102,
        versionNumber = 0,
        activityCodeType = 201,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
    -- 累计充值
    {
        topicID = 1413,
        activityID = 2201,
        headingCode = 1009,
        versionNumber = 0,
        activityCodeType = 1009,
        parentsCode = 0, --父活动的headingCode
        parentsVersion = 0,
        actType = 1, --活动归属，主要用于管理入口
        popui = 0
    },
}
return FakeNewActivityData