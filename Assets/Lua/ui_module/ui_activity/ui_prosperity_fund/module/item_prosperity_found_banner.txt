---
--- Created by: yuannan.
--- DateTime: 2024/11/6.
--- Desc: 繁荣基金Banner背景模块
---

local require = require
local newclass = newclass
local ipairs = ipairs

-- banner 的预设路径不一样
local activity_cfg_util = require "activity_cfg_util"
local item_data = require "item_data"
local reward_item = require "reward_item"
local cfg_util = require "cfg_util"
local binding = require "item_prosperity_found_banner_binding"
local item_activity_banner_info = require "item_activity_banner_info"

module("item_prosperity_found_banner")
local itemView = newclass("item_prosperity_found_banner", item_activity_banner_info)

itemView.widget_table = binding.WidgetTable

function itemView:Init(prefab, order, onRechargeClick)
    item_activity_banner_info.Init(self, prefab, order, onRechargeClick)
end

function itemView:UpdateCfg(cfg)
    item_activity_banner_info.UpdateCfg(self, cfg)
    if self.activityCfg and self.activityCfg.uiCfg then
        if self.rtf_rewardList then
            local festivalActivityCfg = activity_cfg_util.GetFestivalActivityCfg(self.activityCfg.atyId)
            if not festivalActivityCfg then
                return
            end
            self.IData.rewardListMap = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID3)
            if self.IData.rewardListMap then
                local mgr_prosperity_fund = require "mgr_prosperity_fund"
                for k, v in ipairs(self.IData.rewardListMap) do
                    local num = mgr_prosperity_fund.GetGoodNumByTotalReward(self.activityCfg.atyId, v)
                    self.IData[v] = reward_item.new()
                    self.IData[v]:Init(self.rtf_rewardList.transform, true, 0.7)
                    self.IData[v]:SetRewardData(v, num, item_data.Reward_Type_Enum.Item)
                    self.IData[v]:SetCustomEffectEnable(true, "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab", 1.5, self.curOrder + 3, false, nil, true)
                end
            end
        end
    end
end

function itemView:UpdateOrder(order)
    item_activity_banner_info.UpdateOrder(self, order)
    if self.IData.rewardListMap then
        for k, v in ipairs(self.IData.rewardListMap) do
            if self.IData[v] then
                self.IData[v]:SetCustomEffectEnable(true, "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab", 1.5, self.curOrder + 3, false, nil, true)
            end
        end
    end
end

function itemView:UpdateData(data)
    item_activity_banner_info.UpdateData(self, data)
end

return itemView