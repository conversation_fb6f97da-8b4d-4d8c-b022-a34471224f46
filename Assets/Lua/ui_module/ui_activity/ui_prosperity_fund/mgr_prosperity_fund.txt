---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/11/20.
--- Desc: 处理繁荣基金数据和判定逻辑的模块
---

local require = require
local ipairs = ipairs
local pairs = pairs
local table = table
local os = os
local string = string

local gw_event_activity_define = require "gw_event_activity_define"
local topic_pb = require "topic_pb"
local event_task_define = require "event_task_define"
local cfg_util = require "cfg_util"
local activity_cfg_util = require "activity_cfg_util"
local data_personalInfo = require "data_personalInfo"
local event_personalInfo = require "event_personalInfo"
local event = require "event"
local game_scheme = require "game_scheme"

module("mgr_prosperity_fund")

--local mgr_data = nil
local mgr_datas = nil
local registerEvent = false

--region Data Logic
local function createGWActivityData(atyId, timeStamp)
    if not mgr_datas then
        mgr_datas = {}
    end

    if not mgr_datas[atyId] then
        local gw_activity_data_mgr = require "gw_activity_data_mgr"
        mgr_datas[atyId] = gw_activity_data_mgr.CreateAtyData(atyId, timeStamp)
    end
    return mgr_datas[atyId]
end

local function removeGWActivityData(atyId)
    if mgr_datas and mgr_datas[atyId] then
        local gw_activity_data_mgr = require "gw_activity_data_mgr"
        gw_activity_data_mgr.RemoveAtyData(atyId)
    end
end

local function clearGWActivityData(eventName, atyId)
    if mgr_datas and mgr_datas[atyId] then
        mgr_datas[atyId] = nil
    end
end

local function setGWActivityData_cfg(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    
    local mgr_data = mgr_datas[atyId]

    local festivalActivityCfg = activity_cfg_util.GetFestivalActivityCfg(mgr_data.atyId)
    if festivalActivityCfg then
        local tasks = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID1)

        local taskRewards = {}
        local tasksCfg = {}
        if tasks and #tasks > 0 then
            for i = 1, #tasks do
                local cfg = game_scheme:TaskMain_0(tasks[i])
                if cfg then
                    table.insert(tasksCfg, cfg)
                    table.insert(taskRewards, { conditionValue = cfg.ConditionValue1, taskReward = cfg.TaskReward, chargingReward = cfg.ChargingReward })
                end
            end
        end

        local recharges = { [1] = 0 }
        local tempIds = cfg_util.ArrayToLuaArray(festivalActivityCfg.ctnID2)
        if tempIds then
            for k, v in ipairs(tempIds) do
                table.insert(recharges, activity_cfg_util.GetRechargeGoodsIdByActivityContentCfg(v))
            end
        end

        local atyCfg = mgr_data:GetAtyCfg() or {}
        atyCfg.tasks = tasks
        atyCfg.tasksCfg = tasksCfg
        atyCfg.taskRewards = taskRewards
        atyCfg.recharges = recharges
        atyCfg.taskType = tasksCfg[1].ConditionType
        mgr_data:SetAtyCfg(atyCfg)
        return atyCfg
    end
end

local function setGWActivityData_data(atyId, topicValue)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]

    local atyCfg = mgr_data:GetAtyCfg()
    if not atyCfg then
        return
    end

    local atyData = mgr_data:GetAtyData() or {}
    -- if
    if atyCfg.taskType == 202 then
        --建筑解锁类型任务
        if atyData then
            atyData.exp = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel) or 0
        end
    else
        local gw_task_mgr = require "gw_task_mgr"
        local taskID = atyCfg.tasks[#atyCfg.tasks]
        local taskData = gw_task_mgr.GetTaskData(taskID)
        atyData.exp = taskData and taskData.rate or 0
    end
    atyData.rechargeStates = atyData.rechargeStates or { 1, 0 }
    if topicValue then
        atyData.rechargeStates[2] = topicValue == 1 and 1 or 0
    end
    mgr_data:SetAtyData(atyData)
end

-- 获得充值全部奖励 
local function getRechargeAllRewardIds(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]

    local atyCfg = mgr_data:GetAtyCfg()
    if atyCfg then
        local rewardIds = {}
        for k, v in ipairs(atyCfg.taskRewards) do
            table.insert(rewardIds, v.chargingReward)
        end
        return rewardIds
    end
end

-- 获得免费的所有奖励
local function getFreeRewardIds(atyId, list)
    local rewardList = list or {}
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]

    local atyCfg = mgr_data:GetAtyCfg()
    if atyCfg then
        for k, v in ipairs(atyCfg.taskRewards) do
            table.insert(rewardList, v.taskReward)
        end
        return rewardList
    end
end


-- 获得充值可领取奖励
local function getRechargeAvailableRewardIds(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]

    local atyCfg = mgr_data:GetAtyCfg()
    local atyData = mgr_data:GetAtyData()
    if atyCfg and atyData then
        local rewardIds = {}
        for k, v in ipairs(atyCfg.taskRewards) do
            if atyData.exp >= v.conditionValue then
                table.insert(rewardIds, v.chargingReward)
            end
        end
        return rewardIds
    end
end

-- 等级更新事件
local function updateCityLevel(eventName, eventData)
    if not mgr_datas then
        return
    end
    for k, v in pairs(mgr_datas)do
        if v then
            local atyCfg = v:GetAtyCfg()
            if atyCfg.taskType == 202 then
                --建筑解锁类型任务
                local mgr_data = mgr_datas[v.atyId]
                if mgr_data then
                    local atyData = mgr_data:GetAtyData()
                    if atyData and eventData[data_personalInfo.PropEnum.RoleLevel] then
                        atyData.exp = eventData[data_personalInfo.PropEnum.RoleLevel].newValue
                        mgr_data:SetAtyData(atyData)
                    end
                end
                break
            end
        end
        
    end
end

-- 任务奖励领取事件
local function updateTaskState(_,_, moduleId)
    -- if
    if not mgr_datas then
        return
    end
    for k, v in pairs(mgr_datas)do
        if v then
            local atyCfg = v:GetAtyCfg()
            local taskModuleID = atyCfg.tasksCfg[1].ModuleID
            if moduleId == taskModuleID or moduleId == -1 then
                if v then
                    v:TriggerEvent()
                end
                local gw_task_const = require "gw_task_const"
                if moduleId ~= gw_task_const.TaskModuleType.ProsperityFund then
                    local atyData = v:GetAtyData()
                    if atyData then
                        local gw_task_mgr = require "gw_task_mgr"
                        local taskID = atyCfg.tasks[#atyCfg.tasks]
                        local taskData = gw_task_mgr.GetTaskData(taskID)
                        atyData.exp = taskData and taskData.rate or 0
                    end
                end
            end 
        end
    end
end

--获取物品在所有奖励中的数量
function GetGoodNumByTotalReward(atyId, goodID)
    local totalList = GetAllRewardListAndFree(atyId)
    if not totalList then 
        return 0
    end
    for key, value in ipairs(totalList) do
        local curId = value.id
        if curId == goodID then
            return value.num
        end
    end
    return 0
end

--endregion

function Init()
    if not registerEvent then
        registerEvent = true
        event.Register(event_task_define.REFRESH_TASK, updateTaskState)
        event.Register(event_personalInfo.UPDATE_BASE_INFO, updateCityLevel)
        event.Register(gw_event_activity_define.GW_ACTIVITY_COMMON_DISPOSE, clearGWActivityData)
    end
end

function GetProsperityFundData(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    return mgr_data
end

function SetProsperityFundData(atyId, timeStamp, k, v)
    if not atyId then
        return
    end
    -- 给1秒的浮动时间差
    if timeStamp and timeStamp <= (os.server_time() + 1) then
        removeGWActivityData(atyId)
    else
        if not mgr_data then
            createGWActivityData(atyId, timeStamp)
            local cfg = setGWActivityData_cfg(atyId)
            if cfg then
                local gw_task_mgr = require "gw_task_mgr"
                gw_task_mgr.SetActivityTaskId(atyId, cfg.tasks)
            end
        end
        if timeStamp and mgr_datas and mgr_datas[atyId] then
            mgr_datas[atyId].atyTimeStamp = timeStamp
        end

        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
            setGWActivityData_data(atyId, v)
        end
    end
end

-- 打开战令解锁界面
function OpenProsperityFundRechargeView(rechargeId, atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    
    if rechargeId and rechargeId > 0 then
        local reward_mgr = require "reward_mgr"
        local rechargeGoodIds = activity_cfg_util.GetRechargeRewardIds(rechargeId) 
        local allRechargeRewardIds =  activity_cfg_util.InsertFront(getRechargeAllRewardIds(atyId), rechargeGoodIds)
        local getRechargeRewardIds =  activity_cfg_util.InsertFront(getRechargeAvailableRewardIds(atyId), rechargeGoodIds)
        local totalRewards = reward_mgr.GetSummaryRewards(allRechargeRewardIds, true)
        local getRewards = reward_mgr.GetSummaryRewards(getRechargeRewardIds, true)
        local viewData = {
            rechargeId = rechargeId,
            totalRewards = totalRewards,
            getRewards = getRewards,
        }
        local ui_window_mgr = require "ui_window_mgr"
        local atyCfg = mgr_data:GetAtyCfg()
        if atyCfg.taskType == 202 then
            ui_window_mgr:ShowModule("ui_prosperity_fund_unlock", nil, nil, viewData)
        elseif atyCfg.taskType == 118 then
            ui_window_mgr:ShowModule("ui_develop_fund_unlock", nil, nil, viewData)
        end
    end
end

function GetAllRewardIdList(atyId)
    return getRechargeAllRewardIds(atyId)
end

function GetAllRewardListAndFree(atyId)
    local idList = getRechargeAllRewardIds(atyId)
    idList = getFreeRewardIds(atyId, idList)
    local reward_mgr = require "reward_mgr"
    local list = reward_mgr.GetRewardGoodsMergers(idList)
    return list
end

-- 获得充值Id
function GetRechargeId(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    if mgr_data then
        local atyCfg = mgr_data:GetAtyCfg()
        local atyData = mgr_data:GetAtyData()
        if atyCfg and atyData then
            return atyData.rechargeStates[2] == 1 and 0 or atyCfg.recharges[2]
        end
    end
    return nil
end

-- 获得已领取的任务下标
--@return {index1, index2} 每个挡位的任务下标
function GetTaskIndexTable(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    if mgr_data then
        local cfg = mgr_data:GetAtyCfg()
        if cfg then
            local indexTable = { #cfg.tasks, #cfg.tasks }
            local setState = { 0, 0 }
            local gw_task_mgr = require "gw_task_mgr"
            for i = 1, #cfg.tasks do
                local taskId = cfg.tasks[i]
                local taskData = gw_task_mgr.GetTaskData(taskId)
                if taskData then
                    if not taskData.status and setState[1] == 0 then
                        indexTable[1] = i - 1
                        setState[1] = 1
                    end
                    if not taskData.exStatus and setState[2] == 0 then
                        indexTable[2] = i - 1
                        setState[2] = 1
                    end
                end

                if setState[1] == 1 and setState[2] == 1 then
                    break
                end
            end
            return indexTable
        end
    end
    return { 0, 0 }
end

-- 判断是否可有可以领取的奖励下标
function GetCanGetTaskIndex(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    if mgr_data then
        local cfg = mgr_data:GetAtyCfg()
        local data = mgr_data:GetAtyData()
        if cfg and data then
            local gw_task_mgr = require "gw_task_mgr"
            for i = #cfg.tasks, 1, -1 do
                local taskData = gw_task_mgr.GetTaskData(cfg.tasks[i])
                if taskData and taskData.rate >= taskData.completeValue then
                    return i
                end
            end
        end
    end
    return 0
end

-- 判断是否可有可以领取的奖励(计算付费奖励)
function GetCanGetTaskId(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    if mgr_data then
        local cfg = mgr_data:GetAtyCfg()
        local data = mgr_data:GetAtyData()
        if cfg and data then
            local gw_task_mgr = require "gw_task_mgr"
            for _, v in ipairs(cfg.tasks) do
                local taskData = gw_task_mgr.GetTaskData(v)
                if taskData and taskData.rate >= taskData.completeValue then
                    if not taskData.status then
                        return v
                    elseif data.rechargeStates[2] == 1 and not taskData.exStatus then
                        return v
                    end
                end
            end
        end
    end
    return 0
end

-- 获得可领取奖励的数量(计算付费奖励)
function GetCanGetTaskCount(atyId)
    if not mgr_datas or not mgr_datas[atyId] then
        return
    end
    local mgr_data = mgr_datas[atyId]
    local num = 0
    if mgr_data then
        local cfg = mgr_data:GetAtyCfg()
        local data = mgr_data:GetAtyData()
        if cfg and data then
            local gw_task_mgr = require "gw_task_mgr"
            for _, v in ipairs(cfg.tasks) do
                local taskData = gw_task_mgr.GetTaskData(v)
                if taskData and taskData.rate >= taskData.completeValue then
                    if not taskData.status then
                        num = num + 1
                    elseif data.rechargeStates[2] == 1 and not taskData.exStatus then
                        num = num + 1
                    end
                end
            end
        end
    end
    return num
end