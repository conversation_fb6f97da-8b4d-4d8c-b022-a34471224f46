local require = require
local pairs = pairs
local newClass = newclass
local type = type

local reward_mgr = require "reward_mgr"
local activity_cfg_util = require "activity_cfg_util"
local log = require "log"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"

--region Controller Life
module("ui_prosperity_fund_unlock_controller")
local controller = nil
local UIController = newClass("ui_prosperity_fund_unlock_controller", controller_base)

--- data struct 
---{   
---  rechargeId,
---  totalRewards
---  getRewards
---}
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    if data and data.rechargeId > 0 then
        self.CData.rechargeData = data
        self:UpdateData()
    else
        log.Error(view_name .. " data is nil! ")
        ui_window_mgr:UnloadModule(self.view_name)
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:UpdateData()
    local item = self.CData.rechargeData
    self:TriggerUIEvent("SetRechargeData", item.rechargeId)
    self:TriggerUIEvent("SetRewardList", item.totalRewards, item.getRewards)
end

function UIController:OnBtnBuyClickedProxy()
    local net_activity_module = require "net_activity_module"
    net_activity_module.Send_New_Recharge_REQ(self.CData.rechargeData.rechargeId)
    -- 关闭界面
    self:OnBtnCloseBtnClickedProxy()
end

function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
