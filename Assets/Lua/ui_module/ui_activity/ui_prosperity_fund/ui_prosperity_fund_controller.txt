local require = require
local table = table
local pairs = pairs
local ipairs = ipairs
local newClass = newclass
local type = type

local data_activity_infinite_box = require "data_activity_infinite_box"
local data_activity_rewards = require "data_activity_rewards"
local data_activity_banner_info = require "data_activity_banner_info"
local mgr_prosperity_fund = require "mgr_prosperity_fund"
local gw_event_activity_define = require "gw_event_activity_define"
local event = require "event"
local controller_base = require "controller_base"

--region Controller Life
module("ui_prosperity_fund_controller")
local controller = nil
local UIController = newClass("ui_prosperity_fund_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = { atyId = data.activityID, state = true }
    self:SetUICfg()
    self:SetUIData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    self.mgrData = nil
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.onRechargeEvent = function(...)
        self:OnRechargeEventClick(...)
    end

    self.onGetRewardEvent = function(...)
        self:OnGetRewardEventClick(...)
    end

    self.onUpdateDataEvent = function(eventName, atyId, data)
        if atyId == self.CData.atyId then
            self:SetUIData()
        end
    end

    self.onRemoveDataEvent = function(eventName, atyId)
        if atyId == self.CData.atyId then
            self:SetActivityState(false)
        end
    end
    event.Register(gw_event_activity_define.GW_ACTIVITY_COMMON_DATA_UPDATE, self.onUpdateDataEvent)
    event.Register(gw_event_activity_define.GW_ACTIVITY_COMMON_REMOVE, self.onRemoveDataEvent)
end

function UIController:AutoUnsubscribeEvents()
    event.Unregister(gw_event_activity_define.GW_ACTIVITY_COMMON_DATA_UPDATE, self.onUpdateDataEvent)
    event.Unregister(gw_event_activity_define.GW_ACTIVITY_COMMON_REMOVE, self.onRemoveDataEvent)
end
--endregion

--region Controller Logic
function UIController:SetUICfg()
    self.mgrData = mgr_prosperity_fund.GetProsperityFundData(self.CData.atyId)
    if not self.mgrData or not self.CData.state then
        return
    end
    self:TriggerUIEvent("CreateViewModule", self.onGetRewardEvent, self.onRechargeEvent)

    local atyCfg = self.mgrData:GetAtyCfg()
    if not atyCfg then
        return
    end

    local initExp = 0
    local limitExp = 0
    local rewardItems = {}
    for k, v in ipairs(atyCfg.taskRewards) do
        if k == 1 then
            initExp = v.conditionValue
        end
        if k == #atyCfg.taskRewards then
            limitExp = v.conditionValue
        end
        table.insert(rewardItems, data_activity_rewards.NewActivityItem(atyCfg.tasks[k], v.conditionValue, v.taskReward, v.chargingReward))
    end

    local bannerCfg = data_activity_banner_info.NewActivityCfg(self.mgrData.atyUICfg, self.mgrData.atyTimeStamp, self.CData.atyId)
    local rewardCfg = data_activity_rewards.NewActivityCfg(rewardItems, atyCfg.recharges)
    local boxCfg = data_activity_infinite_box.NewActivityCfg(nil, nil, nil, initExp, limitExp)

    self:TriggerUIEvent("SetViewModuleCfg", bannerCfg, rewardCfg, boxCfg)
end

function UIController:SetUIData()
    if not self.mgrData or not self.CData.state then
        return
    end
    local atyCfg = self.mgrData:GetAtyCfg()
    local atyData = self.mgrData:GetAtyData()
    if atyCfg and atyData then
        local rechargeId = mgr_prosperity_fund.GetRechargeId(self.CData.atyId)
        local indexTable = mgr_prosperity_fund.GetTaskIndexTable(self.CData.atyId)
        local index = mgr_prosperity_fund.GetCanGetTaskIndex(self.CData.atyId)
        local taskId = mgr_prosperity_fund.GetCanGetTaskId(self.CData.atyId)
        self:TriggerUIEvent("SetViewModuleData", data_activity_banner_info.NewActivityData(rechargeId),
                data_activity_rewards.NewActivityData(atyData.exp, index, indexTable, atyData.rechargeStates, atyCfg.taskType),
                data_activity_infinite_box.NewActivityData(atyData.exp, rechargeId, taskId))
    end
end

function UIController:SetActivityState(state)
    self.CData.state = state
    self:TriggerUIEvent("SetActivityState", state)
end

function UIController:OnRechargeEventClick(rechargeId)
    if self.CData.state and rechargeId and rechargeId > 0 then
        mgr_prosperity_fund.OpenProsperityFundRechargeView(rechargeId, self.CData.atyId)
    end
end

function UIController:OnGetRewardEventClick(taskId)
    if self.CData.state then
        local rechargeId = mgr_prosperity_fund.GetRechargeId(self.CData.atyId)
        local rechargeState = rechargeId and rechargeId == 0 or false

        local gw_task_mgr = require "gw_task_mgr"
        gw_task_mgr.ReceiveTaskAllReward(taskId, self.mgrData.atyId, rechargeState, true)
    end
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
