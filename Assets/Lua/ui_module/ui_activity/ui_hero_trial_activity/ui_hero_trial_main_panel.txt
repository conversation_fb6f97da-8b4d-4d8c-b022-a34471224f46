local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local type = type

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_hero_trial_main_panel_binding"
local game_scheme = require "game_scheme"
local base_game_object = require "base_game_object"
local Common_Util = CS.Common_Util.UIUtil
local ui_hero_star_item = require "ui_hero_star_item"
local model_res = require "model_res"
local model_img_res = require "model_img_res"
local card_sprite_asset = require "card_sprite_asset"
local log = require "log"
local gw_hero_data = require "gw_hero_data"
local gw_hero_mgr = require "gw_hero_mgr"
local goods_item = require "goods_item_new"
local math = math
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local os = os
local time_util = require "time_util"
local event = require "event"
local flow_text = require "flow_text"
local gw_event_activity_define = require "gw_event_activity_define"
local windowMgr = require "ui_window_mgr"
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local typeof = typeof
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image

--region View Life
module("ui_hero_trial_main_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local renderImg = nil
local redDot = -1

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    renderImg = card_sprite_asset.CreateRenderImgAsset()
    self.cardSpriteAsset = card_sprite_asset.CreateGWActivityCommonAsset()
    self.VData = {}
    self.timeTicker = nil
    self:InitScrollRectTable()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.VData = nil
    if renderImg then
        renderImg:Dispose()
        renderImg = nil
    end
    if self.cardSpriteAsset then
        self.cardSpriteAsset:Dispose()
        self.cardSpriteAsset = nil
    end
    if self.srt_content then
        self.srt_content:ItemsDispose()
    end
    if self.objList then
        for i,v in ipairs(self.objList) do
            v:Dispose()
            v=nil;
        end
    end

    if self.itemBtnList then
        for i,v in ipairs(self.itemBtnList) do
            v.onClick:RemoveAllListeners();
            v = nil;
        end
    end

    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil
    end
    self.__base.Close(self)
end
--endregion

--region View Logic

function UIView:InitScrollRectTable()
    self.srt_content.onItemRender = OnItemRender;
    self.srt_content.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item then
            if scroll_rect_item.data and scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
            end
        end
    end
end

function GetRedDot(activityId)
    if window then
        if activityId == window.data.activityID then
            return redDot
        else
            return -1
        end
    end
    return -1
end

function OnItemRender(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local objParent = scroll_rect_item:Get("parent");
    local finishMark = scroll_rect_item:Get("SelectMask");
    local item = scroll_rect_item.data[3]
    if not item then
        item = goods_item.CGoodsItem():Init(objParent, nil, 0.75)
        scroll_rect_item.data[3] = item
    end
    item:SetGoods(nil, dataItem.id, dataItem.count, function()
        iui_item_detail.Show(dataItem.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
    end)
    Common_Util.SetActive(finishMark,dataItem.isFinish)
end

function UIView:UpdatePanel(data,showParticle)
    for i,v in ipairs(self.objList) do
        v:Dispose()
        v=nil;
    end
    for i,v in ipairs(self.itemBtnList) do
        v.onClick:RemoveAllListeners();
        v = nil;
    end
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil
    end
    self:SetPanelData(data,showParticle)
    self.srt_content:Refresh(0,-1)
end

function UIView:SetPanelData(data,showParticle)
    self.data = data;
    self.objList = {};
    self.itemBtnList = {}
    local taskIsFinish = true
    local uiData = game_scheme:ActivityCommonUI_0(data.uiTemplateID)
    if uiData then
        self.txt_Title.text = lang.Get(uiData.title)
        self.cardSpriteAsset:GetSprite(uiData.bannerIcon, function(sprite)
            self.img_Banner.sprite = sprite
        end)
    end
    
    redDot = 0
    local timeLeft = data.endTime - os.server_time()
    self.txt_Text.text = time_util.FormatTime5(timeLeft);
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil
    end
    self.timeTicker = util.IntervalCall(1,function()
        timeLeft = timeLeft - 1
        self.txt_Text.text = time_util.FormatTime5(timeLeft);
        if timeLeft <= 0 then
            if self.timeTicker then
                util.RemoveDelayCall(self.timeTicker)
                self.timeTicker = nil
            end
            windowMgr:UnloadModule("ui_festival_activity_center")
            --Common_Util.SetActive(self.shieldValue,false)
        end
    end)
    
    for i,v in pairs(data.cutTask) do
        local heroId = v.ConditionValue2.data[0]
        local targetStar = v.ConditionValue1
        local heroCfg = game_scheme:Hero_0(heroId)
        local tempObj = base_game_object()
        local heroData = gw_hero_data.GetAllHeroDataByCfgID(heroId)
        local heroEntity = gw_hero_data.GetHeroCfgId2Entity(heroId)
        local _showParticle = showParticle
        if heroData.isUnLock then
            if math.floor(heroEntity.serverData.heroStarLv / 5) < targetStar then
                taskIsFinish = false
            end
        else
            taskIsFinish = false
        end
        tempObj:LoadResource("ui/prefabs/gw/activity/herotrialactivity/herotrialitem.prefab","",function(obj)
            if obj then
                local quality1 = Common_Util.GetComponent(obj.transform,typeof(SpriteSwitcher),"ssw_qualityBg_1")
                local quality2 = Common_Util.GetComponent(obj.transform,typeof(SpriteSwitcher),"ssw_qualityBg_1/ssw_qualityMask_1")
                local quality3 = Common_Util.GetComponent(obj.transform,typeof(SpriteSwitcher),"ssw_qualityBg_1/ssw_qualityFrame_1")
                
                local raceIcon = Common_Util.GetComponent(obj.transform,typeof(SpriteSwitcher),"ssw_qualityBg_1/ssw_raceIcon_1")
                local jobIcon = Common_Util.GetComponent(obj.transform,typeof(SpriteSwitcher),"ssw_qualityBg_1/ssw_careerIcon_1")
                
                local level = Common_Util.GetComponent(obj.transform,typeof(Text),"ssw_qualityBg_1/txt_heroLevel_1")
                local heroName = Common_Util.GetComponent(obj.transform,typeof(Text),"TitleBg/txt_Name_1")
                local taskDesc = Common_Util.GetComponent(obj.transform,typeof(Text),"TaskTitleBg/txt_Desc_1")
                
                local heroBtn = Common_Util.GetComponent(obj.transform,typeof(Button),"ssw_qualityBg_1")
                
                local lockObj = Common_Util.GetComponent(obj.transform,typeof(Image),"ssw_qualityBg_1/obj_isUnlockMask_1")
                local finishMark = Common_Util.GetComponent(obj.transform,typeof(Image),"obj_FinishMark_1")
                local heroImg = Common_Util.GetComponent(obj.transform,typeof(Image),"ssw_qualityBg_1/img_heroImg_1")
                
                local starTrans = Common_Util.GetTrans(obj.transform,"ssw_qualityBg_1/tf_heroStar_1")
                local particleObj = Common_Util.GetTrans(obj.transform,"Particle")
                
                LoadHeroIcon(heroId, heroImg)
                
                taskDesc.text = string.format2(lang.Get(630090),targetStar)
                heroName.text = lang.Get(heroCfg.HeroNameID)
                
                Common_Util.SetActive(particleObj.gameObject,showParticle)
                
                Common_Util.SetActive(lockObj,not heroData.isUnLock)
                Common_Util.SetActive(level,heroData.isUnLock ~= nil and heroData.isUnLock)
                Common_Util.SetActive(starTrans,heroData.isUnLock ~= nil and heroData.isUnLock)
                if heroData.isUnLock then
                    level.text = "Lv."..heroEntity.serverData.heroLevel
                    local heroStar2 = ui_hero_star_item.new()
                    heroStar2:Init(starTrans, heroEntity.serverData.heroStarLv)
                    Common_Util.SetActive(finishMark,math.floor(heroEntity.serverData.heroStarLv / 5) >= targetStar)
                else
                    Common_Util.SetActive(finishMark,false)
                end
                
                quality1:Switch(heroCfg.rarityType-1)
                quality2:Switch(heroCfg.rarityType-1)
                quality3:Switch(heroCfg.rarityType-1)
                raceIcon:Switch(heroCfg.type - 1)
                jobIcon:Switch(heroCfg.profession - 1)
                if heroBtn then
                    heroBtn.onClick:AddListener(function()
                        if heroData.isUnLock then
                            gw_hero_mgr.OpenHeroDetailUI(heroId)
                        else

                            --region 未解锁的英雄获取缺少的碎片
                            local costId = heroCfg.CompositeID
                            --local costHave = player_mgr.GetPlayerOwnNum(costId)
                            --local needHave = 18
                            --endregion

                            --弹窗资源补充界面
                            local evt_sourceSupply_define = require "evt_sourceSupply_define"
                            local TestData = {
                                list = {
                                    {
                                        goodsId = costId,
                                        needNum = 0, --如果数量<=0，则不会主动剔除该页签
                                    },
                                },
                                supplyType = nil --特殊参数，后续策划可能会用到
                            }
                            event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel, TestData)
                            
                            --local item_source_mgr = require "item_source_mgr"
                            --item_source_mgr.Show({
                            --    goodsID = {
                            --        heroID = heroId,
                            --        numProp =
                            --        {
                            --            starLv = 0,
                            --            lv = 0
                            --        }
                            --    },
                            --    isHero = true,
                            --})
                            --点击“前往获取”查看 打点
                            local reportMsg =
                            {
                                Activity_id = self.data.activityID, -- 活动ID
                            }
                            event.EventReport("HeroTrials_checkgetHero", reportMsg)
                        end
                    end)
                    table.insert(self.itemBtnList,heroBtn)
                end
                
            end
        end,false,self.tf_HeroList)
        if showParticle then
            flow_text.Add(lang.Get(1000405))
        end
        table.insert(self.objList,tempObj)
    end
    local reward_mgr = require "reward_mgr"
    local rewardCfg = reward_mgr.GetRewardGoodsList(data.targetTaskData.TaskReward)
    local dataList = {}
    for i,v in pairs(rewardCfg) do
        local item =
        {
            id = v.id,
            count = v.num,
            isFinish = data.finished,
        }
        table.insert(dataList,item)
    end
    self.srt_content.data = dataList;
    if data.finished then
        Common_Util.SetActive(self.btn_GetReward,false)
        Common_Util.SetActive(self.btn_GetRewardFalse,false)
        self.task_Desc.text = lang.Get(1000406)
    else
        Common_Util.SetActive(self.btn_GetReward,taskIsFinish)
        Common_Util.SetActive(self.btn_GetRewardFalse,not taskIsFinish)
        self.task_Desc.text = lang.Get(1000402)
    end
    redDot = not data.finished and taskIsFinish and 1 or 0
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.data.activityID)
end

---@private LoadHeroIcon 加载英雄Icon
---@param heroID number
---@param Img_hero userdata Image组件
function LoadHeroIcon(heroID, Img_hero)
    local cfg_hero = game_scheme:Hero_0(heroID)
    local modulId = cfg_hero.modelID.data[0]
    local modulCfg = game_scheme:Modul_0(modulId)

    if not modulCfg then
        log.Error("heroID", heroID, "starLv", modulCfg.starLv, "Modul未配置该modulId", modulId)
        return
    end
    local modelPath = model_res.GetResPath(modulCfg.modelPath)
    if not modelPath then
        return
    end
    local res = model_img_res.GetModelImgABName(modelPath)
    if not res then
        return
    end
    renderImg:GetSprite(res, function(sp)
        Img_hero.sprite = sp
        Img_hero.gameObject:SetActive(true)
    end)
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if  data  and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == "table" then
            local uiPath = data.uiPath or ui_path
            local uiParent = data.uiParent or nil
            window:LoadUIResource(uiPath, nil, uiParent, nil, nil, nil, nil, nil, nil, true)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, nil, nil, nil, nil, nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
