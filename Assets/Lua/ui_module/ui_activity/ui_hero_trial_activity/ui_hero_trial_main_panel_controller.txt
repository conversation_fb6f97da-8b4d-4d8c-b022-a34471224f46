local require = require
local pairs = pairs     
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local lang = require "lang"
local game_scheme = require "game_scheme"
local GWTaskMgr = require "gw_task_mgr"
local event_task_define = require "event_task_define"
local gw_hero_define = require "gw_hero_define"
local gw_hero_data = require "gw_hero_data"
local math = math
local flow_text = require "flow_text"
local gw_activity_red_mgr = require "gw_activity_red_mgr"

--region Controller Life
module("ui_hero_trial_main_panel_controller")
local controller = nil
local UIController = newClass("ui_hero_trial_main_panel_controller", controller_base)
--[[
{
    activityID = 活动ID,
    uiPath,
    uiParent,
    isNewbie,
}
]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = data
    self:GetTaskData(true)
    local value = GetRedDot(self.CData.activityID)
    if value > 0 then
        gw_activity_red_mgr.OnSetWeakRedPoint(self.CData.activityID,true)
    else
        gw_activity_red_mgr.OnSetWeakRedPoint(self.CData.activityID,false)
    end
    self.getReward = false
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.updateInfo = function(_)
        self:GetTaskData(false,self.getReward)
        self.getReward = false
        local value = GetRedDot(self.CData.activityID)
        if value > 0 then
            gw_activity_red_mgr.OnSetWeakRedPoint(self.CData.activityID,true)
        else
            gw_activity_red_mgr.OnSetWeakRedPoint(self.CData.activityID,false)
        end
    end
    self:RegisterEvent(event_task_define.REFRESH_TASK,self.updateInfo)
    self:RegisterEvent(gw_hero_define.HERO_AWAKEN_NEW,self.updateInfo)
    self:RegisterEvent(gw_hero_define.UPGRADE_HERO_NEW,self.updateInfo)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function GetRedDot(activity)
    local value = false
    local taskIsFinish = true
    --local activityData = festival_activity_mgr.GetAllActivityDataByActivityCodeType(festival_activity_cfg.ActivityCodeType.Universal)
    local tempData = game_scheme:festivalActivity_0(activity)
    if not tempData then
        return 0
    end
    local taskList = tempData.ctnID1.data;
    for i = 0,tempData.ctnID1.count - 1 do
        local v = taskList[i]
        local taskData = GWTaskMgr.GetTaskData(v)
        if taskData then
            if not taskData.status or i >= tempData.ctnID1.count - 1 then --遍历到最后一个时，必定赋值
                local taskCfg = game_scheme:TaskMain_0(v);
                if taskCfg.ConditionType == 108 then
                    for j = 0, taskCfg.ConditionValue2.count - 1 do
                        local k = taskCfg.ConditionValue2.data[j]
                        local childTask = game_scheme:TaskMain_0(k)
                        local heroId = childTask.ConditionValue2.data[0]
                        local targetStar = childTask.ConditionValue1
                        local heroData = gw_hero_data.GetAllHeroDataByCfgID(heroId)
                        local heroEntity = gw_hero_data.GetHeroCfgId2Entity(heroId)
                        if heroData and heroData.isUnLock then
                            if math.floor(heroEntity.serverData.heroStarLv / 5) < targetStar then
                                taskIsFinish = false
                            end
                        else
                            taskIsFinish = false
                        end
                    end
                    if i >= tempData.ctnID1.count - 1 and taskData.status then
                        value = true
                    else
                        value = false
                    end
                    break;
                end
            end
        end
    end
    return not value and taskIsFinish and 1 or 0
end

function UIController:GetTaskData(isInit,showParticle)
    self.targetTaskId = -1
    local tempData = game_scheme:ActivityMain_0(self.CData.activityID)
    local tempData2 = game_scheme:festivalActivity_0(self.CData.activityID)
    local taskList = tempData2.ctnID1.data;
    --TODO 获取任务部件
    local temp =
    {
        cutTask = {},
        targetTaskData = nil,
        uiTemplateID = tempData.uiTemplateID,
        activityID = self.CData.activityID
    }
    ---2024.11.26:和策划确认了这个数组是按顺序来的，所以从头开始遍历是可行的。但如果策划不按顺序配的话这里会有问题，所以留个备忘。
    for i = 0,tempData2.ctnID1.count - 1 do
        local v = taskList[i]
        local taskData = GWTaskMgr.GetTaskData(v)
        if taskData then
            if not taskData.status or i >= tempData2.ctnID1.count - 1 then --遍历到最后一个时，必定赋值
                local taskCfg = game_scheme:TaskMain_0(v);
                if taskCfg.ConditionType == 108 then
                    for j = 0, taskCfg.ConditionValue2.count - 1 do
                        local k = taskCfg.ConditionValue2.data[j]
                        local childTask = game_scheme:TaskMain_0(k)
                        table.insert(temp.cutTask,childTask)
                    end
                    temp.targetTaskData = taskCfg;
                    if i >= tempData2.ctnID1.count - 1 and taskData.status then
                        temp.finished = true
                    else
                        temp.finished = false
                    end
                    self.targetTaskId = v;
                    temp.endTime = taskData.endTime
                    break;
                end
            end
        end
    end
    if isInit then
        self:TriggerUIEvent( "SetPanelData",temp,false)
    else
        self:TriggerUIEvent( "UpdatePanel",temp,showParticle)
    end
end

function  UIController:OnBtnTipsBtnClickedProxy()
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(10028)
end

function  UIController:OnBtnGetRewardClickedProxy()
    local event = require "event"
    GWTaskMgr.ReceiveTaskReward(self.targetTaskId,self.CData.activityID,false)
    self.getReward = true
    --完成任务领取奖励 打点
    local reportMsg =
    {
        Activity_id = self.CData.activityID, -- 活动ID
        task_id = self.targetTaskId,--任务ID
    }
    event.EventReport("HeroTrials_TaskFinish", reportMsg)
end

function  UIController:OnBtnGetRewardFalseClickedProxy()
    flow_text.Add(lang.Get(1000407))
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
