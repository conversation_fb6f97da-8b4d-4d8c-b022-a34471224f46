--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: 主页Controller
---

--region Require
local require = require
local newclass = newclass

local controller_base = require "controller_base"
local flow_text = require "flow_text"
local allianceMgr = require "alliance_mgr"
local lang = require "lang"
local windowMgr = require "ui_window_mgr"
local mgr_personalInfo = require "mgr_personalInfo"
local const_personalInfo = require "const_personalInfo"
local data_personalInfo = require "data_personalInfo"
local event_personalInfo = require "event_personalInfo"
local custom_avatar_data = require "custom_avatar_data"
--endregion

--region Controller Life
module("ui_personal_homepage_controller")
local controller = nil
local UIController = newclass("ui_personal_homepage_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self:InitPersonalInfo()
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.onPlayerFaceEvent = function()
        self:OnBtnFaceEvent();
    end
    self.onPlayerBaseInfoChange = function()
        self:SetPlayerInfo()
        self:SetBattleInfo()
    end
    self.onPlayerSocialInfoChange = function()
        self:SetSocialInfo()
    end
    self.onPlayerSchlossInfoChange = function()
        self:SetPlayerInfo()
        --self:SetSchlossInfo()
    end

    self:RegisterEvent(event_personalInfo.UPDATE_BASE_INFO, self.onPlayerBaseInfoChange)
    self:RegisterEvent(event_personalInfo.UPDATE_SOCIALIZE_INFO, self.onPlayerSocialInfoChange)
    self:RegisterEvent(event_personalInfo.UPDATE_PERSONALISED_INFO, self.onPlayerSchlossInfoChange)
    self:RegisterEvent(event_personalInfo.CUSTOM_AVATAR_USE, self.onPlayerSchlossInfoChange)
end

function UIController:AutoUnsubscribeEvents()
    self.onPlayerFaceEvent = nil
    self.onPlayerBaseInfoChange = nil
    self.onPlayerSocialInfoChange = nil
    self.onPlayerSchlossInfoChange = nil
end
--endregion

--region 数据修改事件

function UIController:InitPersonalInfo()
    self:SetPlayerInfo()
    self:SetSocialInfo()
    self:SetBattleInfo()
    self:SetWorldInfo()
    --self:SetSchlossInfo()
end

function UIController:SetPlayerInfo()
    local baseData = {
        faceID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID),
        frameID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID),
        level = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel),
        name = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleName),
        customAvatarData = custom_avatar_data.GetMyAvatar(),
        faceClickEvent = self.onPlayerFaceEvent,
    }
    self:TriggerUIEvent("UpdateUserInfo", baseData)
end

function UIController:SetSocialInfo()
    local socialInfo = {
        sex = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex),
        praise = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePraise),
        roleID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID),
    }
    self:TriggerUIEvent("UpdateSocialInfo", socialInfo)
end

function UIController:SetBattleInfo()
    local battleInfo = {
        battlePower = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePower),
        enemyKill = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleKillNum),
    }
    self:TriggerUIEvent("UpdateBattleInfo", battleInfo)
end

function UIController:SetWorldInfo()
    local frayInfo = {
        worldId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.WorldId),
    }
    if allianceMgr.GetIsJoinAlliance() then
        frayInfo.allianceName = allianceMgr.GetUserAllianceShortName()
    end
    self:TriggerUIEvent("UpdateWorldInfo", frayInfo)
end

--[[打开个人信息修改界面]]
function UIController:OnBtnChangeEvent()
    mgr_personalInfo.ShowPersonalChangeView()
end

--[[打开头像修改界面]]
function UIController:OnBtnHeadChangeEvent()
    mgr_personalInfo.ShowPersonalised(const_personalInfo.PersonalisedTag.face)
end

--[[打开个性化城堡修改界面]]
function UIController:OnBtnFaceEvent()
    mgr_personalInfo.ShowPersonalised(const_personalInfo.PersonalisedTag.face)
end

--[[打开个性化城堡修改界面]]
function UIController:OnBtnSchlossChangeEvent()
    mgr_personalInfo.ShowPersonalised(const_personalInfo.PersonalisedTag.schloss)
end

--[[打开Rank界面]]
function UIController:OnBtnRankEvent()
    flow_text.Clear()
    local gw_all_rank_mgr = require "gw_all_rank_mgr"
    gw_all_rank_mgr.JumpToRankBase(function()
        windowMgr:UnloadModule("ui_personalInfo")
    end)
end

--[[打开个性化城堡修改界面]]
function UIController:OnBtnAllianceEvent()
    if allianceMgr.GetIsJoinAlliance() then
        mgr_personalInfo.ClosePersonalInfoView()
        windowMgr:ShowModule("ui_alliance_main")
    else
        flow_text.Clear()
        flow_text.Add(lang.Get(const_personalInfo.lang_JoinAlliance))
    end
end

--[[打开战区界面]]
function UIController:OnBtnWorldEvent()
    --flow_text.Clear()
    --flow_text.Add("战区官职功能，未解锁")
    windowMgr:ShowModule("ui_theater_position_panel");
    windowMgr:UnloadModule("ui_personalInfo")
end

--endregion

--region ModuleFunction
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

function Close()
    controller = nil;
end

--endregion
