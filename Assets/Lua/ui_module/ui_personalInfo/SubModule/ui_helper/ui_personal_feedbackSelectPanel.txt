-- author:  袁楠
-- date:    2024/8/24
-- ver:     1.0
-- desc:    玩家反馈当前选择问题分类详情页
--------------------------------------------

local require = require
local string = string
local typeof = typeof

local util = require "util"
local class = require "class"
local object = require "object"
local log = require "log"
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local InputField = CS.UnityEngine.UI.InputField
local LinkImageTextEx = CS.War.UI.LinkImageTextEx
local Application = CS.UnityEngine.Application

module("ui_personal_feedbackSelectPanel")

local M = {}

local TextType = {
    input = 0,
    normal = 1,
    link = 2
}

function M:ctor(selfType, UIRoot)
    self.UIRoot = UIRoot
    self:Init()
end

--[[
panelInfo =
{
    title = x,
    closeCallback = x,
}
]]
function M:Show(panelInfo)
    self.panelInfo = panelInfo
    self.isActived = true
    self.UIRoot.gameObject:SetActive(true)

    self:UpdateUI()
end

function M:Init()
    local rootTrans = self.UIRoot
    self.txtTitle = rootTrans:Find("Title/Text"):GetComponent(typeof(Text))
    self.inpDescription = rootTrans:Find("InputField"):GetComponent(typeof(InputField))
    self.btnTitle = rootTrans:Find("Title"):GetComponent(typeof(Button))
    self.normalText = rootTrans:Find("normalText"):GetComponent(typeof(Text))
    self.linkText = rootTrans:Find("linkText"):GetComponent(typeof(LinkImageTextEx))
    self.linkText_logout = rootTrans:Find("LinkText"):GetComponent(typeof(Text))
    self.richtextMessage = rootTrans:Find("linkText"):GetComponent(typeof(Text))
    self:SubscribeEvents()

    self.ClosePanel = function()
        --关闭问题类型详情页
        self:Close()
    end
    self.btnTitle.onClick:AddListener(self.ClosePanel)
end

function M:Close()
    self.UIRoot.gameObject:SetActive(false)
    self.isActived = false

    local closeCallback = self.panelInfo and self.panelInfo.closeCallback
    self.panelInfo = nil

    if closeCallback then
        closeCallback()
    end
    self:UnsubscribeEvents()
end

function M:IsActived()
    return (not util.IsObjNull(self.UIRoot)) and self.isActived
end

function M:IsShowSubBtn()
    return not self.panelInfo or not self.panelInfo.textType or (self.panelInfo.textType and self.panelInfo.textType == TextType.input)
end

function M:UpdateUI()
    self.txtTitle.text = self.panelInfo.title
    local util = require "util"
    local const = require "const"
    local channel_tag = util.GetChannelTag()
    local isQ1Sdk = channel_tag == const.package_name_set.com_wmzz2_q1 or channel_tag == const.package_name_set.com_cnsj_youyi or channel_tag == const.package_name_set.com_xgame_q1 or channel_tag == const.package_name_set.com_cnsj_q1 or channel_tag == const.package_name_set.com_xgame_hnsz

    if self.panelInfo.textType == TextType.normal and self.panelInfo.normalTip then
        self.normalText.text = self.panelInfo.normalTip
    elseif self.panelInfo.textType == TextType.link and self.panelInfo.normalTip then
        -- local txtMessage = self.linkText.gameObject.transform:GetComponent(typeof(LinkImageTextEx))
        -- if txtMessage then
        --     if txtMessage.colorHex then
        --         txtMessage.colorHex = "#47FF49"
        --     end
        -- end
        if isQ1Sdk and self.panelInfo.reportId == 5 then
            self.linkText.text = self.panelInfo.normalTip
        else
            self.linkText_logout.text = self.panelInfo.normalTip
        end
    end
    self.linkText.gameObject:SetActive(self.panelInfo.textType == TextType.link and isQ1Sdk and self.panelInfo.reportId == 5)
    self.linkText_logout.gameObject:SetActive(not self.linkText.gameObject.activeSelf and self.panelInfo.textType == TextType.link)
    self.normalText.gameObject:SetActive(self.panelInfo.textType == 1)
    self.inpDescription.gameObject:SetActive(not self.panelInfo.textType or self.panelInfo.textType == TextType.input)
end

function M:GetDescription()
    return self.inpDescription.text
end

function M:SubscribeEvents()
    self.linkTextEvent = function(url)
        local util = require "util"
        local const = require "const"
        local channel_tag = util.GetChannelTag()
        local isQ1Sdk = channel_tag == const.package_name_set.com_wmzz2_q1 or channel_tag == const.package_name_set.com_cnsj_youyi or channel_tag == const.package_name_set.com_xgame_q1 or channel_tag == const.package_name_set.com_cnsj_q1 or channel_tag == const.package_name_set.com_xgame_hnsz
        if isQ1Sdk then
            local _url = url
            if url and self.panelInfo.reportId and self.panelInfo.reportId == 5 then
                -- 人工客服
                local net_login_module = require "net_login_module"
                local token = net_login_module.GetLoginSession() or "testSession"
                _url = string.format(url, token)
                log.Warning(" 人工客服超链接 ", _url)
            end
            local q1sdk = require "q1sdk"
            q1sdk.ApplicationOpenURL(_url)
        end
    end
    self.richtextMessage.onHrefClick:AddListener(self.linkTextEvent)
end

function M:UnsubscribeEvents()
    self.richtextMessage.onHrefClick:RemoveListener(self.linkTextEvent)
end

CFeedbackSelectPanel = class(object, nil, M)