-- author:  袁楠
-- date:    2024/8/24
-- ver:     1.0
-- desc:    玩家反馈问题分类按钮
--------------------------------------------------------------
local require = require
local typeof = typeof
local lang = require "lang"
local com_class = require "com_class"
local mudule_scroll_list  = require "scroll_list"
local CScrollListItemBase = mudule_scroll_list.CScrollListItemBase
local Text = CS.UnityEngine.UI.Text

module("ui_personal_feedbackItem")

--问题列表
CFeedbackItem = com_class.CreateClass(CScrollListItemBase)
local M = CFeedbackItem

function M:Create(goSkin)
    M.__super.Create(self, goSkin)
    self.titleText = self:FindChild("Text"):GetComponent(typeof(Text))
end

function M:Destroy()
    M.__super.Destroy(self)
end

function M:Draw(data,i) --data就是一行的内容 ,i和list 一般不用
    --设置每个item
    local title = lang.Get(data.id)
    if data.textType == 2 and (data.id == 1692 or data.id == 470012) then
        title = data.reportId.."."..title
    end
    self.strTitle = title
    self.titleText.text = title
    self.textType = data.textType
    if data.normalTip then
        -- self.normalStr = lang.Get(data.normalID)
        self.normalStr = data.normalTip
    end
end

function M:OnSelected()
    --每个标题的点击事件
    local ui_setting_help = require "ui_personal_helper"
    ui_setting_help.OnFeedbackTypeSelect(self.nDataIndex, self.strTitle, self.textType, self.normalStr)
end
