-- author:  袁楠
-- date:    2024/8/24
-- ver:     1.0
-- desc:   帮助/FAQ详情
--------------------------------------------------------------
local print = print
local require = require
local string = string
local lang = require "lang"
local game_scheme = require "game_scheme"
local ui_base = require "ui_base"
local class = require "class"
local event = require "event"
local Button = CS.UnityEngine.UI.Button
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local RectTransform = CS.UnityEngine.RectTransform

module("ui_personal_helpDetail")

local window = nil
local oParent = nil
local SettingHelpDetail = {}
local CSettingHelpDetail = nil
local infoID = nil

SettingHelpDetail.widget_table = {
    curPanelBtn = { path = "Title", type = Button }, --展开问题的标题按钮
    curPanelTitle = { path = "Title/Text", type = "Text" }, --展开问题的标题
    curPanelContent = { path = "Info/Viewport/Content", type = RectTransform },
    curPanelInfo = { path = "Info/Viewport/Content/Text", type = "Text" }, --展开问题内容
    richtextMessage = { path = "Info/Viewport/Content/LinkText", type = "Text" },
    scrollRect = { path = "Info", type = ScrollRect },


    TitleText = { path = "Title/Text", type = "Text", fitArabic = true },
    InfoText = { path = "Info/Viewport/Content/Text", type = "Text", fitArabic = true },
    LinkText = { path = "Info/Viewport/Content/LinkText", type = "Text", fitArabic = true },
}
function SettingHelpDetail:FixMultiLang()
    if lang.USE_LANG == lang.AR then
        local titleTextRT = self.TitleText:GetComponent("RectTransform")
        titleTextRT.anchoredPosition = { x = -28.14, y = 2 }
        titleTextRT.sizeDelta = { x = 595, y = 55.2 }
    end
end
function SettingHelpDetail:SubscribeEvents()
    self.CloseTitleBtn = function()
        --关闭问题详情页
        self.curPanelInfo.text = nil
        self.curPanelTitle.text = nil
        self:Close()

    end
    self.curPanelBtn.onClick:AddListener(self.CloseTitleBtn)
    self.onClickHrefEvent = function(url)
        local q1sdk = require "q1sdk"
        local _url = url
        if self and self.useRichText and url and q1sdk.OpenUrl and infoID then
            if infoID == 4 then
                -- 人工客服
                local net_login_module = require "net_login_module"
                local token = net_login_module.GetLoginSession() or "testSession"
                _url = string.format(url, token)
            end
        end
        q1sdk.OpenUrl(_url)
        -- q1sdk.ApplicationOpenURL(_url)
    end
    self.richtextMessage.onHrefClick:AddListener(self.onClickHrefEvent)

    self.CloseTitleBtn = function()
        --关闭问题详情页
        self.curPanelInfo.text = nil
        self.curPanelTitle.text = nil
        self:Close()

    end
    self.curPanelBtn.onClick:AddListener(self.CloseTitleBtn)
end

function SettingHelpDetail:UnsubscribeEvents()
    self.curPanelBtn.onClick:RemoveListener(self.CloseTitleBtn)
    self.richtextMessage.onHrefClick:RemoveListener(self.onClickHrefEvent)
end

function SettingHelpDetail:Update()
    if infoID == nil then
        ------ --print("infoID传入值为空")
        Close()
    end
    local uiSettingHelp = require "ui_personal_helper"

    local data = uiSettingHelp.SetInfoData()
    --  --print("infoID:",infoID)
    local infoData = infoID and data[infoID].contentID
    local game_config = require "game_config"
    if infoID == 4 and game_config.ENABLE_Q1SDK_CHANNEL then
        infoData = game_scheme:HelpTips_0(160).contentID
    end
    self.curPanelTitle.text = infoID and data[infoID] and lang.Get(data[infoID].titleID)
    local info = ""
    for i = 0, infoData.count - 1 do
        --  --print("infoData.data[i]:",infoData.data[i])
        local texts = lang.Get(infoData.data[i])
        info = info .. texts .. "\n\n"
    end

    if info and infoID == 1 then
        local url_mgr = require "url_mgr"
        info = string.gsub(info, "%%s", url_mgr.FACEBOOK_URL)
    end

    -- local start, urlEnd = string.find(info, "<a.+href=[%w/:%-%.]+>")
    local start, urlEnd = string.find(info, "<a.+href=.+>")
    self.useRichText = (start or 0) > 0
    if self.useRichText then
        self.richtextMessage.text = info
        self.curPanelContent.sizeDelta = { x = self.curPanelContent.rect.width, y = self.richtextMessage.preferredHeight } --调整宽高
    else
        self.curPanelInfo.text = info
        self.scrollRect.enabled = self.curPanelInfo.preferredHeight > 668
        self.curPanelContent.sizeDelta = { x = self.curPanelContent.rect.width, y = self.curPanelInfo.preferredHeight } --调整宽高
    end
    self.curPanelInfo.gameObject:SetActive(not self.useRichText)
    self.richtextMessage.gameObject:SetActive(self.useRichText)
end

function SettingHelpDetail:Init()
    self:SubscribeEvents()
    self:Update()
end

--外部接口
function GetInfoID(id)
    infoID = id
    if window and window.UIRoot then
        --传入id
        --window:Update()  
    end
end

function SettingHelpDetail:Close()
    if self.UIRoot and self:IsValid() then
        self:UnsubscribeEvents()
    end
    self.__base:Close()
    self.useRichText = false
    window = nil
end

function Create(objParent)
    oParent = objParent
    CSettingHelpDetail = class(ui_base, nil, SettingHelpDetail)
end

function Show()
    if window == nil then
        window = CSettingHelpDetail()
        window._NAME = _NAME;
        window:LoadUIResource("ui/prefabs/gw/gw_personalinfo/uihelpcurpanel.prefab", nil, oParent, nil, false)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function OnSceneDestroy()
    Close()
end

event.Register(event.SCENE_DESTROY, OnSceneDestroy)