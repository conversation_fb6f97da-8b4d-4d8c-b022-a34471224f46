--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: FAQ/帮助/反馈页
---

local print = print
local require = require
local ipairs = ipairs
local table = table
local typeof = typeof
local string = string
local os = os
local util = require "util"
local lang = require "lang"
local game_scheme = require "game_scheme"
local player_mgr = require "player_mgr"
local com_class = require "com_class"
local ui_base = require "ui_base"
local class = require "class"
local event = require "event"
local ui_personal_helpDetail = require "ui_personal_helpDetail"
local ui_personal_feedbackItem = require "ui_personal_feedbackItem"
local ui_personal_feedbackSelectPanel = require "ui_personal_feedbackSelectPanel"
local windowMgr = require "ui_window_mgr"
local flow_text = require "flow_text"
local net_feedback_module = require "net_feedback_module"
local mudule_scroll_list = require "scroll_list"
local log = require "log"
local const = require "const"
local q1sdk = require "q1sdk"
local CScrollList = mudule_scroll_list.CScrollList
local CScrollListItemBase = mudule_scroll_list.CScrollListItemBase
local _G = _G
local json = require "dkjson"

local Transform = CS.UnityEngine.Transform
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local Toggle = CS.UnityEngine.UI.Toggle
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRect = CS.UnityEngine.UI.ScrollRect
--local GameObject = CS.UnityEngine.GameObject
local InputField = CS.UnityEngine.UI.InputField
local Time = CS.UnityEngine.Time
local Application = CS.UnityEngine.Application
local Quaternion = CS.UnityEngine.Quaternion
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local WWW = CS.UnityEngine.WWW
local UIUtil = CS.Common_Util.UIUtil
local Q1SDK = CS.Q1.Q1SDK
local net_email_subscribe_module = require "net_email_subscribe_module"

-- 200200: 点击选择想要咨询的问题
local TextType = {
    input = 0,
    normal = 1,
    link = 2
}
local FeedbackTypeID = {
    { id = 200201, reportId = "1", textType = TextType.input }, -- 1.登录卡顿、加载缓慢
    { id = 200202, reportId = "2", textType = TextType.input }, -- 2.充值失败、未收到商品
    { id = 200203, reportId = "3", textType = TextType.input }, -- 3.游戏内Bug反馈
    { id = 200204, reportId = "4", textType = TextType.input }, -- 4.账户丢失、找回、绑定等相关问题
    { id = 200205, reportId = "5", textType = TextType.input }, -- 5.本地化语言问题
    { id = 200206, reportId = "6", textType = TextType.input }, -- 6.其他问题
    { id = 200207, reportId = "5", textType = TextType.link, normalID = 200208 }, -- 5.人工客服
}

module("ui_personal_helper")
local ui_path = "ui/prefabs/gw/gw_personalinfo/uihelper.prefab"

local window = nil
local SettingHelp = {}
local CSettingHelp = nil

local logouturl = nil

--最大的时间差
local maxTimeDiff = 10
local lastTimeSec = 0

SettingHelp.widget_table = {
    togHelp = { path = "TogList/TogHelp", type = Toggle },
    togFAQ = { path = "TogList/TogFAQ", type = Toggle },
    togFeedback = { path = "TogList/TogFeedback", type = Toggle },

    togHelpText = { path = "TogList/TogHelp", type = Text },
    togFAQText = { path = "TogList/TogFAQ", type = Text },
    togFeedbackText = { path = "TogList/TogFeedback", type = Text },

    togHelpSelect = { path = "TogList/TogHelp/select", type = Transform },
    togFAQSelect = { path = "TogList/TogFAQ/select", type = Transform },
    togFeedbackSelect = { path = "TogList/TogFeedback/select", type = Transform },

    helpPanel = { path = "HelpPanel", type = Transform },
    helpPanelContent = { path = "HelpPanel/bg/Viewport/Content", type = RectTransform },
    itemList = { path = "HelpPanel/bg", type = ScrollRect },
    feedbackPanel = { path = "Feedback", type = Transform },
    submitBtn = { path = "Feedback/SubmitBtn", type = Button }, --提交按钮
    -- inputText = {path = "Feedback/InputField", type = InputField},  --提交内容

    feedbackTypePanel = { path = "Feedback/feedbackTypeTips/feedbackTypePanel", type = Transform }, --问题类型选择列表
    feedbackItemList = { path = "Feedback/feedbackTypeTips/feedbackTypePanel/bg", type = ScrollRect },
    feedbackTypeTips = { path = "Feedback/feedbackTypeTips", type = RectTransform },
    btnFeedbackType = { path = "Feedback/feedbackTypeTips/btn", type = Button, event_name = "OnBtnFeedbackType" },
    imgFeedbackTypeArrow = { path = "Feedback/feedbackTypeTips/btn/Arrow", type = RectTransform },

    feedbackSelectPanel = { path = "Feedback/feedbackSelectPanel", type = RectTransform },
    inputText = { path = "Feedback/feedbackSelectPanel/InputField", type = InputField }, --提交内容
    normalText = { path = "Feedback/feedbackSelectPanel/normalText", type = Text },
    linkText = { path = "Feedback/feedbackSelectPanel/LinkText", type = Text, fitArabic = true },
    fbpBtn = { path = "Feedback/FaceBookPage/btn", type = Button },
    fbp = { path = "Feedback/FaceBookPage", type = RectTransform },
    ---新增日本法律条文
    togJapLegal = { path = "TogList/TogJapLegal", type = Toggle },
    togJapLegalText = { path = "TogList/TogJapLegal", type = Text },
    togJapLegalSelect = { path = "TogList/TogJapLegal/select", type = Transform },
    japLegalPanel = { path = "JapLegal", type = Transform },
    legalBtn1 = { path = "JapLegal/legalBtn1", type = Button, event_name = "OnBtnlLegalBtn1" },
    legalBtn2 = { path = "JapLegal/legalBtn2", type = Button, event_name = "OnBtnlLegalBtn2" },

    helpPanelListItemText = { path = "HelpPanel/bg/Viewport/Content/ListItem/Text", type = "Text", fitArabic = true },
    feedbackTypeTipsComName = { path = "Feedback/feedbackTypeTips/btn/comName", type = "Text", fitArabic = true },
    FaceBookPageComName = { path = "Feedback/FaceBookPage/btn/comName", type = "Text", fitArabic = true },
    title = { path = "Title", type = "Text", fitArabic = true },
    feedbackTypePanelListItemText = { path = "Feedback/feedbackTypeTips/feedbackTypePanel/bg/Viewport/Content/ListItem/Text", type = "Text", fitArabic = true },
    feedbackSelectPanelText = { path = "Feedback/feedbackSelectPanel/Title/Text", type = "Text", fitArabic = true },
    feedbackSelectPanelInputText = { path = "Feedback/feedbackSelectPanel/InputField/Text", type = "Text", fitArabic = true },
    feedbackSelectPanelPlaceholder = { path = "Feedback/feedbackSelectPanel/InputField/Placeholder", type = "Text", fitArabic = true },

}

local subModule = {}    --子模块
local curSubMoudle = nil  --当前子模块
local curIndex = nil
local dataInfo = nil
local curPageSelectId = nil
function SettingHelp:FixMultiLang()
    if self:IsRTLEnvironment() then
        self.helpPanelListItemText.rectTransform.anchoredPosition = { x = 148, y = 2 }
    end
end
function SettingHelp:LoadSubModule()
    subModule[1] = self.helpPanel
    subModule[2] = self.helpPanel
    subModule[3] = self.feedbackPanel
    subModule[4] = self.japLegalPanel
end

function SettingHelp:RefreshCurSubMoudle(index)
    if self.UIRoot == nil then
        return
    end
    curSubMoudle = index
    for i, v in ipairs(self.toggleData) do
        if i == index then
            self.toggleData[i].obj.isOn = true
            -- self.toggleData[i].texts.color = {r=1, g=1, b=1, a=1}
            self.toggleData[i].select.gameObject:SetActive(true)
            subModule[i].gameObject:SetActive(true)
        else
            self.toggleData[i].obj.isOn = false
            -- self.toggleData[i].texts.color = {r=76/255, g=177/255, b=1, a=1}
            self.toggleData[i].select.gameObject:SetActive(false)
            if i ~= 2 then
                subModule[i].gameObject:SetActive(false)
            end
        end
    end
end

--问题列表
CListItem = com_class.CreateClass(CScrollListItemBase)

function CListItem:Create(goSkin)
    CListItem.__super.Create(self, goSkin)
    self.titleText = self:FindChild("Text"):GetComponent(typeof(Text))
    self.arrowRect = self:FindChild("Arrow"):GetComponent(typeof(RectTransform))
end

function CListItem:Destroy()
    CListItem.__super.Destroy(self)
end

function CListItem:Draw(data, i)
    --data就是一行的内容 ,i和list 一般不用
    --设置每个item
    local title = lang.Get(data.titleID)
    self.titleText.text = title
    self.arrowRect.gameObject:SetActive(not data.isLink)
end

function CListItem:OnSelected()
    --每个标题的点击事件
    local tempData = dataInfo[self.nDataIndex]
    if tempData and tempData.isLink then
        local url_mgr = require("url_mgr")
        local url = url_mgr.GetUserAgreementUrlForReviewing()
        if url then
            if Application.isEditor then
                q1sdk.ApplicationOpenURL(url)
            else
                local q1sdk = require "q1sdk"
                q1sdk.OpenWebUrl(url)
            end

        end
        return
    end
    ui_personal_helpDetail.GetInfoID(self.nDataIndex)
    windowMgr:ShowModule("ui_personal_helpDetail")
end

function SettingHelp:SubscribeEvents()

    self.OnInputing = function(content)
        ------ --print("sssssss")
        --local  warn_str_func = require("warn_str_func")
        --self.inputText.text = warn_str_func.WarningStrGsub(content)
        self.inputText.text = content
    end
    self.inputText.onValueChanged:AddListener(self.OnInputing)

    self.OnSubmitBtn = function()
        --反馈内容提交按钮
        -- local inputData = self.inputText.gameObject:GetComponent(typeof(InputField))
        local feedbackText = self.inputText.text
        --冷区时间10s
        local curTimeSec = Time.time
        if feedbackText == "" then
            flow_text.Add(lang.Get(787)) --反馈为空
        elseif (curTimeSec - lastTimeSec) < maxTimeDiff then
            flow_text.Add(lang.Get(789)) --反馈太频繁
        elseif self.strFeedbackType == "" then
            flow_text.Add(lang.Get(200200)) -- 点击选择想要咨询的问题
        else
            event.Trigger(event.BEFORE_SEND_BUG_REPORT)
            local strFeedbackType = self.strFeedbackType
            local intFeedbackType = self.intFeedbackType
            -- 发送日志文件
            local net_url_post_module = require "net_url_post_module"
            net_url_post_module.SendBugReport(
                    function(result, logDownloadUri)
                        local logUri = "send log file failed"
                        if logDownloadUri ~= nil and not string.empty(logDownloadUri) then
                            logUri = logDownloadUri
                        else
                            if result ~= nil then
                                --[[
                            e.g. 1|url|上传成功
                            如果上传成功则返回下载链接。否则返回失败原因
                            ]]
                                local params = string.split(result, "|")
                                if #params >= 3 and params[1] == "1" then
                                    logUri = params[2]
                                else
                                    logUri = result
                                end
                            end
                        end

                        -- 发送玩家反馈内容到冰川管理后台
                        --flow_text.Add(lang.Get(788))        --反馈成功 刷新界面,清空文字
                        local uptoServer = util.BuildPostData(intFeedbackType, strFeedbackType, feedbackText, logUri)

                        local now = os.server_time() --获取当前日期
                        local timeStr = os.date("%Y_%m_%d_%H_%M_%S", now)

                        local setting_server_data = require "setting_server_data"
                        local curServerID = setting_server_data.GetLoginWorldID()

                        local playerProp = player_mgr.GetPlayerProp()
                        local playerName = ""
                        if playerProp then
                            playerName = playerProp.roleName --玩家名字
                        end

                        local palyerID = player_mgr.GetPlayerRoleID()
                        --反馈编号(未约定) + 反馈玩家ID + 反馈内容 + 反馈时间 + 反馈区服
                        -- net_feedback_module.ReportPost(playerName, palyerID, timeStr, uptoServer)
                        
                        net_feedback_module.GameBugReport(feedbackText,self.title, logUri)
                    end
            )
            self.inputText.text = nil
            lastTimeSec = curTimeSec

            self.feedbackSelectUIPanel:Close()
        end
    end
    self.submitBtn.onClick:AddListener(self.OnSubmitBtn)

    local url_mgr = require "url_mgr"
    self.fbpBtnEvent = function()
        -- local data = self:GetCommunityByID(2)
        local url = url_mgr.FACEBOOK_ROOT_URL --data and data.strWebsite or url_mgr.FACEBOOK_ROOT_URL
        q1sdk.ApplicationOpenURL(url)
    end
    self.fbpBtn.onClick:AddListener(self.fbpBtnEvent)

    self.OnBtnlLegalBtn1 = function()
        if Application.platform == RuntimePlatform.IPhonePlayer then
            local url = "https://protocol.9z-play.com/GlaciersGame/enPrivacyPolicy.html"--url_mgr.JAPLEGAL_IOS_URL
            q1sdk.ApplicationOpenURL(url)
        else
            local url = "https://protocol.9z-play.com/GlaciersGame/enPrivacyPolicy.html"--url_mgr.JAPLEGAL_AND_URL
            q1sdk.ApplicationOpenURL(url)
        end
    end

    self.OnBtnlLegalBtn2 = function()
        local url = url_mgr.JAPLEGAL2_URL
        q1sdk.ApplicationOpenURL(url)
    end

    -- 2021/1/22 添加反馈问题分类，屏蔽此按钮	
    -- self.fbgBtnEvent = function()
    -- 	-- local data = self:GetCommunityByID(3)
    -- 	local url = url_mgr.FACEBOOK_SHARE_URL --data and data.strWebsite or url_mgr.FACEBOOK_SHARE_URL
    -- 	q1sdk.ApplicationOpenURL(url)
    -- end

    local game_config = require "game_config"

    if game_config.Q1SDK_DOMESTIC then
        self.logouturl = "https://kefu.hnsz168.cn/account/logout.html?hidePrivacyPolicy=1&hideBackBtn=1&token="
        if game_config.ENABLE_Q1_DEBUG_MODE then
            self.logouturl = "http://kefu.dev.q1.com/account/logout.html?hidePrivacyPolicy=1&hideBackBtn=1&token="
        end
    else
        --内网测试：http://www.cdh.dev.q1.com/h5/logout.html?token=xxxx
        --国外：https://login-ea.q1.com/h5/logout.html?token=xxxx
        --繁中：https://login-ea.q1.com/h5/logout-gat.html?token=xxxx
        self.logouturl = "https://login-ea.q1.com/h5/logout.html?token="
        if game_config.ENABLE_Q1_DEBUG_MODE then
            self.logouturl = "http://www.cdh.dev.q1.com/h5/logout.html?token="
        end
    end

    local q1sdk = require "q1sdk"
    -- print("token = ",PlayerPrefs.GetString('current_token'));
    print("zzd ___token =", PlayerPrefs.GetString('current_token'))
    local token = PlayerPrefs.GetString('current_token')
    --新包url不转码
    if token then
        --iOS中OpenLink的老包需要token转码，Android没有OpenLink的c#接口
        if Q1SDK.Instance and Q1SDK.Instance.OpenLink then
            token = WWW.EscapeURL(token)
        end
        self.logouturl = self.logouturl .. token
    else
        self.logouturl = nil
    end
    -- self.logouturl = "https://login-ea.q1.com/h5/logout.html?token="
    -- local t = "RC|bJCdL/ilU4h6tadyPb8+nkMkzq46qxaTrpKPVhKGv2sjg7uKNdlpd4yWYuJwqs4vAyWHYbYKzsdSVKjAPLg0C5RLRAw+gAbZ9XHrTRI/NGLwrD/UkcjJHdSqiXHfmOCIJ+c0bOhHB0kvCtGrU2Qx/cIUWUEK8uc9Qe7W0J7mNSM8iXEw0Cqh7h/RzB+KziVc3b1Hg+REJc+ixCbz/tYjrxSepnGUaLYxPGRlAg7FC4tf3ZsRBFukfkKkOBAuK9hDuyrLPDMyROGOChdv+aB7QW4Qq5uiRbvJ8I9AwvrJZs2TtKyJ6nXR0nD8RZNOUF3f"
    self.onClickHrefEvent = function(url)
        if url == "unbind" then
            --解绑邮箱
            local message_box = require "message_box"
            message_box.Open(lang.Get(470014), message_box.STYLE_YESNO, function(d, yesOrNo)
                if yesOrNo == message_box.RESULT_YES then
                    net_email_subscribe_module.RequestEmailUnbind()
                end
            end, nil, lang.KEY_OK, lang.KEY_CANCEL)
        else
            --注销账号
            print("zzd______Open URL")
            if q1sdk.LogoutAccountAction() == true then
                print("zzd______Open URL___by sdk")
                return
            end
            if self and self.logouturl then
                log.Warning("zzd---------logouturl ", self.logouturl)
                --增加点击注销账号上报
                local json_str = json.encode({
                    token = token, --转码之前token
                    logout_url_after = self.logouturl, --转码之后的注销链接
                })
                event.Trigger(event.GAME_EVENT_REPORT, "LogOutAccount", json_str)
                q1sdk.OpenWebUrl(self.logouturl)
            else
                log.Error("logouturl Error", self.logouturl)
            end
        end
    end
    self.linkText.onHrefClick:AddListener(self.onClickHrefEvent)
end

function SettingHelp:GetCommunityByID(id)
    if not id then
        return
    end
    local tempData = game_scheme:Community_0(id)
    if tempData then
        -- and game_config.VERSION_TYPE == tempData.Region - 1 and tempData.showType == 2
        return tempData
    end
end

function SettingHelp:BuildListData()
    --读表
    local helpListData = {}
    local FAQListData = {}
    local helpData = {}
    local FAQData = {}
    --直接按配置表数字读,如果表id变化会读错
    for i = 17, 29 do
        local tempData = game_scheme:HelpTips_0(i)
        if tempData then
            if i < 26 then
                table.insert(helpListData, tempData)
            else
                table.insert(FAQListData, tempData)
            end

        end
    end

    --新增的帮助（先按照原来的逻辑新增，待后续优化）
    for i = 300, 330 do
        local tempData = game_scheme:HelpTips_0(i)
        if tempData then
            table.insert(helpListData, tempData)
        end
    end

    if curSubMoudle == 1 then
        return helpListData
    else
        --iOS审核服添加用户协议
        local ReviewingUtil = require "ReviewingUtil"
        local url_mgr = require("url_mgr")
        local url = url_mgr.GetUserAgreementUrlForReviewing()
        if Application.isEditor then
            if ReviewingUtil.IsReviewing() and url then
                table.insert(FAQListData, { titleID = 1659, isLink = true })
            end
        else
            if ReviewingUtil.IsReviewing() and Application.platform == RuntimePlatform.IPhonePlayer and url then
                table.insert(FAQListData, { titleID = 1659, isLink = true })
            end
        end
        return FAQListData
    end
end

function SettingHelp:UpdateListData()
    if self.ListInstance == nil then
        return
    end
    dataInfo = self:BuildListData()
    self.itemList.enabled = #dataInfo >= 10
    self.helpPanelContent.anchoredPosition = { x = 0, y = 0 }
    self.ListInstance:SetListData(dataInfo)
end

function SettingHelp:UpdateFeedbackListData()
    if self.feedbackListInstance == nil then
        return
    end
    self.feedbackListInstance:SetListData(FeedbackTypeID)
end

function SettingHelp:UnsubscribeEvents()
    --注销事件
    self.submitBtn.onClick:RemoveListener(self.OnSubmitBtn)
    self.fbpBtn.onClick:RemoveListener(self.fbpBtnEvent)
    self.linkText.onHrefClick:RemoveListener(self.onClickHrefEvent)
end

function SettingHelp:Init()
    --页面初始化
    window:LoadSubModule()
    ui_personal_helpDetail.Create(window.helpPanel)
    --国内特殊处理
    local game_config = require "game_config"
    local util = require "util"
    local channel_tag = util.GetChannelTag()
    if not util.ShouldUseCustomSDKUI() then
        FeedbackTypeID = {
            { id = 200201, reportId = "1" }, -- 1.登录卡顿、加载缓慢
            { id = 200202, reportId = "2" }, -- 2.充值失败、未收到商品
            { id = 200203, reportId = "3" }, -- 3.游戏内Bug反馈
            { id = 200204, reportId = "4" }, -- 4.账户丢失、找回、绑定等相关问题
            --{id=200206, reportId = "5"}, -- 5.其他问题
        }
        local isQ1Sdk = channel_tag == const.package_name_set.com_wmzz2_q1 or channel_tag == const.package_name_set.com_cnsj_youyi or channel_tag == const.package_name_set.com_xgame_q1 or channel_tag == const.package_name_set.com_cnsj_q1 or channel_tag == const.package_name_set.com_cnsj_bcwl
        if isQ1Sdk then
            local chatData = _G["_CHAT_EXTPARAMS_DATA"]
            local _normalTip = lang.Get(200208)
            if chatData then
                local net_vip_module = require "net_vip_module"
                local totalNum = net_vip_module.GetRechargeNum() or 0
                if totalNum >= 1000 then
                end
                _normalTip = totalNum >= 1000 and chatData.CustomerServiceContactVip or chatData.CustomerServiceContact
            end
            table.insert(FeedbackTypeID, { id = 200207, reportId = "5", textType = TextType.link, normalTip = _normalTip }) -- 5.人工客服
            if Application.platform == RuntimePlatform.IPhonePlayer then
                local _normalTipStr = lang.Get(1693)
                table.insert(
                        FeedbackTypeID,
                        { id = 1692, reportId = "6", textType = TextType.link, normalTip = _normalTipStr }
                ) -- 6.注销账号
                --table.insert(FeedbackTypeID, { id = 470012, reportId = "7", textType = TextType.link, normalTip = lang.Get(470013) }) -- 7.解绑订阅邮箱 
            else
                --table.insert(FeedbackTypeID, { id = 470012, reportId = "6", textType = TextType.link, normalTip = lang.Get(470013) }) -- 6.解绑订阅邮箱 
            end
        else
            -- E74E47
            -- 您可点击<a href=w>【账号注销】</a>申请账号注销，申请后您将推出游戏并无法使用此账号登录，请谨慎操作。
            if Application.platform == RuntimePlatform.IPhonePlayer then
                table.insert(
                        FeedbackTypeID,
                        { id = 1692, reportId = "5", textType = TextType.link, normalTip = lang.Get(1693) }
                ) -- 5.注销账号
                --table.insert(FeedbackTypeID, { id = 470012, reportId = "6", textType = TextType.link, normalTip = lang.Get(470013) }) -- 6.解绑订阅邮箱 
            else
                --table.insert(FeedbackTypeID, { id = 470012, reportId = "5", textType = TextType.link, normalTip = lang.Get(470013) }) -- 5.解绑订阅邮箱 
            end
        end
    else
        FeedbackTypeID = {
            { id = 200201, reportId = "1" }, -- 1.登录卡顿、加载缓慢
            { id = 200202, reportId = "2" }, -- 2.充值失败、未收到商品
            { id = 200203, reportId = "3" }, -- 3.游戏内Bug反馈
            { id = 200204, reportId = "4" }, -- 4.账户丢失、找回、绑定等相关问题
            { id = 200205, reportId = "5" }, -- 5.本地化语言问题
            { id = 200206, reportId = "6" }, -- 6.其他问题
        }
        table.insert(
                FeedbackTypeID,
                { id = 1692, reportId = "7", textType = TextType.link, normalTip = lang.Get(1694) }
        ) -- 7.注销账号
        --table.insert(FeedbackTypeID, { id = 470012, reportId = "8", textType = TextType.link, normalTip = lang.Get(470013) }) -- 8.解绑订阅邮箱 
    end

    self.toggleData = {
        { moduleNum = 1, obj = self.togHelp, texts = self.togHelpText, select = self.togHelpSelect },
        { moduleNum = 2, obj = self.togFAQ, texts = self.togFAQText, select = self.togFAQSelect },
        { moduleNum = 3, obj = self.togFeedback, texts = self.togFeedbackText, select = self.togFeedbackSelect, OnEnable = self.OnFeedbackEnable },
        { moduleNum = 4, obj = self.togJapLegal, texts = self.togJapLegalText, select = self.togJapLegalSelect },
    }
    
    if curIndex == nil then
        curIndex = 2
    end

    self:RefreshCurSubMoudle(curIndex)

    self:SubscribeEvents()

    if self.itemList then
        self.ListInstance = CScrollList:CreateInstance({})
        self.ListInstance:Create(self.itemList, CListItem)
        self:UpdateListData()
    end

    self.OnToggleChange = function()
        for i, v in ipairs(self.toggleData) do
            if i > 1 then
                if v.obj.isOn then
                    subModule[i].gameObject:SetActive(true)
                    curSubMoudle = i
                    -- v.texts.color = {r=1, g=1, b=1, a=1}
                    v.select.gameObject:SetActive(true)
                    if v.OnEnable then
                        v.OnEnable(self)
                    end
                else
                    -- v.texts.color = {r=76/255, g=177/255, b=1, a=1}
                    v.select.gameObject:SetActive(false)
                    if (i == 2 and curSubMoudle == 1) or (i == 1 and curSubMoudle == 2) then
                        ------ --print("curSubMoudle = "..curSubMoudle)
                    else
                        subModule[i].gameObject:SetActive(false)
                    end
                end
            end
        end
        self:UpdateListData() --每次按下刷新帮助或者FAQ界面
        windowMgr:HideModule("ui_personal_helpDetail")
    end
    for i, v in ipairs(self.toggleData) do
        v.obj.onValueChanged:AddListener(self.OnToggleChange)
    end

    -- 初始化反馈问题类型
    self.strFeedbackType = ""
    self.intFeedbackType = 0

    if self.feedbackItemList then
        self.feedbackListInstance = CScrollList:CreateInstance({})
        self.feedbackListInstance:Create(self.feedbackItemList, ui_personal_feedbackItem.CFeedbackItem)
        self:UpdateFeedbackListData()
    end

    self.feedbackListExpand = true
    self.OnBtnFeedbackType = function()
        self.feedbackListExpand = not self.feedbackListExpand
        local arrayIdx = 0
        if not self.feedbackListExpand then
            arrayIdx = 90
            self.feedbackTypePanel.gameObject:SetActive(false)
        else
            self.feedbackTypePanel.gameObject:SetActive(true)
        end
        -- self.imgFeedbackTypeArrow:Switch(arrayIdx)
        local rotation = { x = 0, y = 0, z = arrayIdx }
        self.imgFeedbackTypeArrow.localRotation = Quaternion.Euler(rotation)

    end
    self.feedbackSelectUIPanel = ui_personal_feedbackSelectPanel.CFeedbackSelectPanel(self.feedbackSelectPanel)
    self:UpdateFeedbackPanelSelect()

    local ReviewingUtil = require "ReviewingUtil"
    if not util.ShouldUseCustomSDKUI() or ReviewingUtil.IsReviewing() or const.IsKoreaChannel() or const.IsJapanChannel() then
        self.fbp.gameObject:SetActive(false)
    end

    --审核服开启
    self.togJapLegal.gameObject:SetActive(ReviewingUtil.IsReviewing())
    UIUtil.SetActive(self.legalBtn1,ReviewingUtil.IsReviewing())
    UIUtil.SetActive(self.legalBtn2,false)
end

-- 问题类型选择变化时更新界面
function SettingHelp:UpdateFeedbackPanelSelect()
    local ReviewingUtil = require "ReviewingUtil"
    local isSelectType = self.feedbackSelectUIPanel:IsActived()
    self.submitBtn.interactable = isSelectType
    self.submitBtn.gameObject:SetActive(self.feedbackSelectUIPanel:IsShowSubBtn() and isSelectType)
    if not util.ShouldUseCustomSDKUI() or ReviewingUtil.IsReviewing() or const.IsKoreaChannel() or const.IsJapanChannel() then
        self.fbp.gameObject:SetActive(false)
    else
        self.fbp.gameObject:SetActive(not isSelectType)
    end
    self.feedbackTypePanel.gameObject:SetActive(not isSelectType)
    self.feedbackTypeTips.gameObject:SetActive(not isSelectType)
end

function OnFeedbackTypeSelect(index, strDes, textType, normalTip)
    print("12233")
    if window and window:IsValid() then
        window:OnFeedbackTypeSelect(index, strDes, textType, normalTip)
    end
end

function SettingHelp:OnFeedbackTypeSelect(index, strDes, textType, normalTip)
    print("45566")
    self.feedbackSelectUIPanel:Show(
            {
                title = strDes,
                closeCallback = function()
                    self.submitBtn.gameObject:SetActive(true)
                    -- 关闭回调
                    self:UpdateFeedbackPanelSelect()
                end,
                textType = textType,
                normalTip = normalTip,
                reportId = index,
            })
    local strFeedbackType = strDes
    self.title = strDes
    self.strFeedbackType = strFeedbackType
    -- 如果游戏设置不是中文，多语言会未初始化，无法完成将语言翻译成中文
    -- 选择上报 问题 id
    self.intFeedbackType = index
    local feedbackTypeInfo = FeedbackTypeID[index]
    if feedbackTypeInfo then
        self.intFeedbackType = feedbackTypeInfo.reportId
    end
    self:UpdateFeedbackPanelSelect()
    if textType == TextType.link then
        self.submitBtn.gameObject:SetActive(false)
        -- print("zzd_________self.linkText.m_OutputText",self.linkText.m_OutputText)
        self.linkText.color = { r = 231 / 255, g = 78 / 255, b = 71 / 255, a = 1 }
    end
end

function SettingHelp:OnFeedbackEnable()
    local isSelectType = self.feedbackSelectUIPanel:IsActived()
    if isSelectType then
        -- 界面可见时，如果选择了具体的问题分类，则恢复到主界面
        self.feedbackSelectUIPanel:Close()
    end
end

function SettingHelp:Close()
    if self.UIRoot and self:IsValid() then
        self:UnsubscribeEvents()
        for i, v in ipairs(self.toggleData) do
            v.obj.onValueChanged:RemoveListener(self.OnToggleChange)
        end
    end
    if self.ListInstance then
        self.ListInstance:ReleaseInstance()
        self.ListInstance = nil
    end

    if self.feedbackListInstance then
        self.feedbackListInstance:ReleaseInstance()
        self.feedbackListInstance = nil
    end

    self.__base:Close()
    window = nil
end

function SetInfoData()
    return dataInfo
end

function SetCurPage(index)
    if window and window:IsValid() then
        window:RefreshCurSubMoudle(index)
    end
end

--获取当前页,从大厅直接进反馈页
function GetCurIndex(curNum)
    curIndex = curNum
end

function SetCurPageSelectId(id)
    curPageSelectId = id
    if window and window:IsValid() then
        window:RefreshFeedBackList()
    end
end

function SettingHelp:RefreshFeedBackList()
    if self.feedbackListInstance ~= nil then
        if curPageSelectId ~= nil then
            for i, v in ipairs(FeedbackTypeID) do
                if curPageSelectId == v.id then
                    self.feedbackListInstance:SetSelectIndex(i)
                end
            end
        end
    end
end

local CUIView = class(ui_base, nil, SettingHelp)

function Show(parentTransform)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME;
        window:LoadUIResource(ui_path, nil, parentTransform, nil, false)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        windowMgr:UnloadModule("ui_personal_helpDetail")
        window:Hide()
        curPageSelectId = nil
    end
end

function Close()
    if window ~= nil then
        curIndex = nil
        windowMgr:UnloadModule("ui_personal_helpDetail")
        window:Close()
        window = nil
    end
end

function OnSceneDestroy()
    Close()
end

event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function ReportCallBack(isSuccessful)
    local flow_text = require "flow_text"
    if isSuccessful then
        local game_config = require "game_config"
        if game_config.ENABLE_Q1SDK_CHANNEL then
            flow_text.Add(lang.Get(502))
        else
            flow_text.Add(lang.Get(788))
        end
    else
        flow_text.Add(lang.Get(789))
    end
end

--刷新当前列表，在语言修改时调用，否则语言不会刷新
function Refresh()
    if window and window:IsValid() then
        window:UpdateListData()
    end
end