--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: 个人信息修改界面View
---

--region Require
local require = require
local tostring = tostring

local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local time_util = require "time_util"
local color_palette = require "color_palette"
local const_personalInfo = require "const_personalInfo"
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
--endregion 

--region View Life
module("ui_personal_change")
local ui_path = "ui/prefabs/gw/gw_personalinfo/uipersonalchange.prefab"
local window = nil
local UIView = {}

UIView.widget_table = {
    Btn_Close = { path = "Auto_closeBtn", type = "Button", event_name = "OnCloseBtnClick", backEvent = true },

    IF_name = { path = "content/nameLab/Auto_name", type = "InputField", value_changed_event = "OnNameValueChanged" },

    Btn_changeName = { path = "content/nameLab/Auto_changeNameBtn", type = "Button", event_name = "OnBtnChangeNameEvent" },
    SpSwitch_changeName = { path = "content/nameLab/Auto_changeNameBtn", type = SpriteSwitcher },

    RTan_diamond = { path = "content/nameLab/Auto_changeNameBtn/Auto_diamond", type = "RectTransform" },
    Text_diamond = { path = "content/nameLab/Auto_changeNameBtn/Auto_diamond/Auto_diamondText", type = "Text" },
    Text_free = { path = "content/nameLab/Auto_changeNameBtn/Auto_freeText", type = "Text" },
    Text_changeCD = { path = "content/nameLab/Auto_changeNameBtn/Auto_changeCD", type = "Text" },

    tog_man = { path = "content/sexLab/togGroup/Auto_man", type = "Toggle", value_changed_event = "OnTogManValueChange" },
    tog_woman = { path = "content/sexLab/togGroup/Auto_woman", type = "Toggle", value_changed_event = "OnTogWomanValueChange" },
    tog_null = { path = "content/sexLab/togGroup/Auto_null", type = "Toggle", value_changed_event = "OnTogNullValueChange" },

    Btn_save = { path = "content/sexLab/saveBg/Auto_saveBtn", type = "Button", event_name = "OnBtnSaveEvent" },
    SpSwitch_save = { path = "content/sexLab/saveBg/Auto_saveBtn", type = SpriteSwitcher },

    Text_save = { path = "content/sexLab/saveBg/Auto_saveBtn/Auto_saveText", type = "Text" },
    Text_saveCD = { path = "content/sexLab/saveBg/Auto_saveBtn/Auto_saveCD", type = "Text" },
}

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self.toggleGroup = {
        [const_personalInfo.PersonalSexTag.null] = self.tog_null,
        [const_personalInfo.PersonalSexTag.man] = self.tog_man,
        [const_personalInfo.PersonalSexTag.woman] = self.tog_woman,
    }
end

--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end

--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()
    self.__base:Close()
    self.toggleGroup = nil
end
--endregion

--region 功能函数区
function UIView:UpdateNameChangeState(freeChangeName, diamonds, diamondEnough, changNameCD)
    self:ResetNameChangeState()
    if freeChangeName then
        self.Text_free.gameObject:SetActive(true)
    elseif changNameCD and changNameCD > 0 then
        self.SpSwitch_changeName:Switch(1)
        self.Text_changeCD.gameObject:SetActive(true)
        self:UpdateChangeNameCDTimer(changNameCD)
    else
        self.RTan_diamond.gameObject:SetActive(true)
        self.Text_diamond.text = tostring(diamonds)
        if diamondEnough then
            self.SpSwitch_changeName:Switch(0)
            self.Text_diamond.color = color_palette.HexToColor(const_personalInfo.color_diamondEnough)
        else
            self.SpSwitch_changeName:Switch(1)
            self.Text_diamond.color = color_palette.HexToColor(const_personalInfo.color_diamondNotEnough)
        end
    end
end

function UIView:ResetNameChangeState()
    self.SpSwitch_changeName:Switch(0)
    self.Text_diamond.color = color_palette.HexToColor(const_personalInfo.color_diamondEnough)
    self.Text_free.gameObject:SetActive(false)
    self.Text_changeCD.gameObject:SetActive(false)
    self.RTan_diamond.gameObject:SetActive(false)
end

function UIView:UpdateSexChangeState(roleSexCD, roleSex)
    self:ResetSexChangeState()
    self.roleSex = roleSex
    if roleSexCD and roleSexCD > 0 then
        self.SpSwitch_save:Switch(1)
        self.Text_saveCD.gameObject:SetActive(true)
        self:UpdateChangeSexCDTimer(roleSexCD)
    else
        self.Text_save.gameObject:SetActive(true)
    end
end

function UIView:ResetSexChangeState()
    self.SpSwitch_save:Switch(0)
    self.Text_save.gameObject:SetActive(false)
    self.Text_saveCD.gameObject:SetActive(false)
end

function UIView:UpdateChangeNameCDTimer(remainingTime)
    self.Text_changeCD.text = time_util.FormatTimeHHMMSS(remainingTime)
end

function UIView:UpdateChangeSexCDTimer(remainingTime)
    self.Text_saveCD.text = time_util.FormatTimeHHMMSS(remainingTime)
end

function UIView:SwitchSexToggle(index)
    local selectTog = self.toggleGroup[index]
    if not selectTog then
        selectTog = self.toggleGroup[const_personalInfo.PersonalSexTag.null]
    end
    if self.roleSex == index then
        self.SpSwitch_save:Switch(1)
    elseif not self.Text_saveCD.gameObject.activeSelf then
        self.SpSwitch_save:Switch(0)
    end
    selectTog.isOn = true
end

function UIView:OnChangeInputFiledText(text)
    if text and text ~= "" then
        self.IF_name.text = text
    end
end

--endregion

--region View Static
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.isBlurBg = true
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
