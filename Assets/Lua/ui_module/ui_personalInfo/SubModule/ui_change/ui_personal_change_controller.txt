--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: 个人信息修改界面Controller
--- todo 有一些重复的逻辑，后续有时间尝试修改一下
---

local require = require
local newClass = newclass
local pairs = pairs
local os = os

local lang = require "lang"
local message_box = require "message_box"
local item_data = require "item_data"
local controller_base = require "controller_base"
local util = require "util"
local player_mgr = require "player_mgr"
local mgr_personalInfo = require "mgr_personalInfo"
local const_personalInfo = require "const_personalInfo"
local event_personalInfo = require "event_personalInfo"
local data_personalInfo = require "data_personalInfo"
local net_personalInfo = require "net_personalInfo"
local windowMgr = require "ui_window_mgr"

module("ui_personal_change_controller")
local controller = nil
local UIController = newClass("ui_personal_change_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)

    self:SetNameChangeState()
    self:SetSexChangeState()
    self:CreateCDTimer()
end

function UIController:Close()
    self.__base.Close(self)
    self.cdTimer = nil
    self.timerData = nil
    self.nameText = nil
    self.isFree = false
end

function UIController:AutoSubscribeEvents()
    self.onPlayerDetails = function()
        self:SetNameChangeState()
        self:CreateCDTimer()
    end

    self.onRoleSexChange = function()
        self:SetSexChangeState()
        self:CreateCDTimer()
    end

    self:RegisterEvent(event_personalInfo.UPDATE_BASE_INFO, self.onPlayerDetails)
    self:RegisterEvent(event_personalInfo.UPDATE_SOCIALIZE_INFO, self.onRoleSexChange)
end

function UIController:AutoUnsubscribeEvents()
    self.onPlayerDetails = nil
    self.onRoleSexChange = nil
end

---********************功能函数区**********---
function UIController:SetNameChangeState()
    self.isFree = player_mgr.GetGoodsCountByType(item_data.Item_Type_Enum.ChangeNameCard) > 0
    local diamonds = mgr_personalInfo.GetChangeNameDiamonds()
    local diamondEnough = diamonds <= player_mgr.GetPlayerAllDiamond()
    local changNameCD = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameCD) - os.server_time()
    if changNameCD > 0 then
        self.timerData = self.timerData or {}
        self.timerData["UpdateChangeNameCDTimer"] = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameCD)
    end
    self:TriggerUIEvent("UpdateNameChangeState", self.isFree, diamonds, diamondEnough, changNameCD)
end

function UIController:SetSexChangeState()
    local roleSexCD = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSexCD) - os.server_time()
    if roleSexCD > 0 then
        self.timerData = self.timerData or {}
        self.timerData["UpdateChangeSexCDTimer"] = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSexCD)
    end
    self.sexId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex)
    self:TriggerUIEvent("UpdateSexChangeState", roleSexCD, self.sexId)
    self:TriggerUIEvent("SwitchSexToggle", self.sexId)
end

function UIController:CreateCDTimer()
    if not self.cdTimer and self.timerData then
        self.cdTimer = self:CreateTimer(1,
                function()
                    for funcName, cd in pairs(self.timerData) do
                        if cd and cd > 0 then
                            local time = cd - os.server_time()
                            self.timerData[funcName] = time
                            self:TriggerUIEvent(funcName, time)
                            if time <= 0 then
                                self:CDTimerCallback(funcName)
                            end
                        end
                    end
                end
        )
    end
end

function UIController:CDTimerCallback(funcName)
    if not funcName then
        return
    end
    if funcName == "UpdateChangeNameCDTimer" then
        self:SetNameChangeState()
    elseif funcName == "UpdateChangeSexCDTimer" then
        self:SetSexChangeState()
    end
end

function UIController:OnNameValueChanged(text)
    if text and text ~= "" then
        if util.GetStringByteLen(text) > const_personalInfo.value_NameMaxLimit then
            self:TriggerUIEvent("OnChangeInputFiledText", self.nameText)
        else
            self.nameText = text
        end
    end
end

function UIController:OnBtnChangeNameEvent()
    local firstChangeName = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameFree)
    if self.isFree and not firstChangeName then
        local nameText = self.nameText
        message_box.Open(lang.Get(650077), message_box.STYLE_YESNO,
                function(callbackData, nRet)
                    if message_box.RESULT_YES == nRet then
                        message_box.Close(true)
                        net_personalInfo.MSG_ZONE_ROLE_UPDATE_NAME_REQ(nameText)
                    end
                end)
    else
        net_personalInfo.MSG_ZONE_ROLE_UPDATE_NAME_REQ(self.nameText)
    end
end

function UIController:OnTogManValueChange(state)
    if state then
        self.sexId = const_personalInfo.PersonalSexTag.man
        self:TriggerUIEvent("SwitchSexToggle", self.sexId)
    end
end

function UIController:OnTogWomanValueChange(state)
    if state then
        self.sexId = const_personalInfo.PersonalSexTag.woman
        self:TriggerUIEvent("SwitchSexToggle", self.sexId)
    end
end

function UIController:OnTogNullValueChange(state)
    if state then
        self.sexId = const_personalInfo.PersonalSexTag.null
        self:TriggerUIEvent("SwitchSexToggle", self.sexId)
    end
end

function UIController:OnBtnSaveEvent()
    net_personalInfo.MSG_ZONE_ROLE_UPDATE_SEX_REQ(self.sexId)
end

function UIController:OnCloseBtnClick()
    windowMgr:UnloadModule(self.view_name)
end
---********************end功能函数区**********---

--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
