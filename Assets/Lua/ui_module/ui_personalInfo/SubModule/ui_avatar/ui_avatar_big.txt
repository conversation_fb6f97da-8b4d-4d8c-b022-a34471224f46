--region FileHead
--- ui_avatar_big.txt
-- author:  author
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local log = log
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local http_inst = require "http_inst"
local game_scheme = require "game_scheme"
local card_assets = require "card_sprite_asset"
--endregion 

--region ModuleDeclare
module("ui_avatar_big")
local ui_path = "ui/prefabs/gw/gw_personalinfo/uiavatarbig.prefab"
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = {
      Btn_closeBtn = { path = "Auto_closeBtn", type = "Button", event_name = "OnCloseBtnClick" , backEvent = true},
    Img_content = { path = "Auto_content", type = "Image", event_name = "" },
 
}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    self.faceSpriteAsset = self.faceSpriteAsset or card_assets.CreateHeroAsset()
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents()
    if self.faceSpriteAsset then
        self.faceSpriteAsset:Dispose()
        self.faceSpriteAsset = nil
    end
    window = nil   
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
function UIView:UnsubscribeEvents()    
    
end

--endregion

--region 功能函数区
---********************功能函数区**********---

--刷新头像图片
function UIView:RefreshAvatarImgByUrl(avatarUrl)
    local custom_avatar_mgr = require "custom_avatar_mgr"
    custom_avatar_mgr.SetAvatarIcon(self.Img_content, avatarUrl)
end

--刷新头像图片
function UIView:RefreshAvatarImg(avatarID)
    local faceImage = nil
    if avatarID and avatarID > 0 then
        local cfg_face = game_scheme:RoleFace_0(avatarID)
        
        if not cfg_face then
            log.Error("请检查头像配置,找不到9201的默认头像配置")
            return
        end
        faceImage = cfg_face.rivalType
    end

    self.faceSpriteAsset:GetSprite(faceImage, function(sprite)
        if self and self.Img_content and not self.Img_content:IsNull() and faceImage then
            self.Img_content.sprite = sprite
        end
    end)
end

---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == "table" then
            local uiPath = data.uiPath or ui_path
            local uiParent = data.uiParent or nil
            window:LoadUIResource(uiPath, nil, uiParent, nil)
        else
            window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
