
--- ui_avatar_big_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local event                   = require "event"
local newclass = newclass
local tonumber = tonumber
local controller_base = require "controller_base"
local windowMgr = require "ui_window_mgr"
module("ui_avatar_big_controller")
local controller = nil
local UIController = newclass("ui_avatar_big_controller", controller_base)
local avatarID
local avatarUrl
local ownerId
--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    avatarID = tonumber(data.avatarID) 
    avatarUrl = data.avatarUrl
    local ToID = tonumber(data.avatarUrl)
    ownerId = data.ownerId
    if ToID then 
        self:TriggerUIEvent("RefreshAvatarImg", ToID)
    else
        self:RefreshUI()
    end
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
    event.EventReport("CustomizeHead_Check", {Target_Role_ID = ownerId})
end
function  UIController:OnBtn_closeBtnClickedProxy()
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()   

end
---********************功能函数区**********---

function UIController:RefreshUI()
    if avatarUrl~=nil then
        self:TriggerUIEvent("RefreshAvatarImgByUrl", avatarUrl)
    else
        self:TriggerUIEvent("RefreshAvatarImg", avatarID)
    end

end

function UIController:OnCloseBtnClick()
    windowMgr:UnloadModule(self.view_name)
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
