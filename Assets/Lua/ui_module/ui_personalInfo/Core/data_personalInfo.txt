---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/8/29.
--- Desc: 个性化数据模块
---

local require = require
local pairs = pairs
local tonumber = tonumber

local util = require "util"
local event = require "event"
local event_personalInfo = require "event_personalInfo"

module("data_personalInfo")

PropEnum = {
    --- [基础信息]
    WorldId = -10,
    RoleID = 1,
    RoleLevel = 2,
    RoleName = 3,
    RoleNameCD = 4,
    RoleNameFree = 5,
    RoleCreateTime = 6,
    RoleOpenSvrTime = 7,
    RolePower = 8,
    RoleKillNum = 9,

    --- [社交信息]
    RoleSex = 100,
    RoleSexCD = 101,
    RolePraise = 102,
    RolePraiseCount = 103,

    --- [个性化装扮信息]
    FaceID = 200,
    CityID = 201,
    FrameID = 202,
    TitleID = 203,
    AnimalsID = 204
}

--region 事件通知
local EventFunction = nil

local function GetEventKey(propKey)
    local key = tonumber(propKey)
    if key < 100 then
        return event_personalInfo.UPDATE_BASE_INFO
    end
    if key >= 100 and key < 200 then
        return event_personalInfo.UPDATE_SOCIALIZE_INFO
    end
    if key >= 200 and key < 300 then
        return event_personalInfo.UPDATE_PERSONALISED_INFO
    end
    return nil
end

local function EventTrigger()
    if EventFunction then
        for k, v in pairs(EventFunction) do
            event.Trigger(k, v)
        end
        EventFunction = nil
    end
end

local function PreEventTrigger(key, value, oldValue)
    if not EventFunction then
        -- 延迟调用，避免一帧内触发多次刷新
        EventFunction = {}
        util.YieldFrame(function()
            EventTrigger()
        end)
    end
    local eventKey = GetEventKey(key)
    if eventKey then
        EventFunction[eventKey] = EventFunction[eventKey] or {}
        EventFunction[eventKey][key] = {
            newValue = value,
            oldValue = oldValue
        }
    end
    -- 立刻发送通用响应事件,发送太多好像会崩溃
    -- event.Trigger(event_personalInfo.UPDATE_PERSONAL_INFO, key, value, oldValue)
end
--endregion

--region 个性化数据
local personalInfoProp = {}

function InitPersonalInfoValue()
    personalInfoProp = {}
    for _, data in pairs(PropEnum) do
        if data == PropEnum.RoleName then
            personalInfoProp[data] = ""
        elseif data == PropEnum.RoleNameFree then
            personalInfoProp[data] = false
        else
            personalInfoProp[data] = 0
        end
    end
end

function GetPersonalInfoValue(key)
    return personalInfoProp[key]
end

function SetPersonalInfoValue(key, value)
    local oldValue = personalInfoProp[key]
    if oldValue ~= value then
        SetPersonalInfoValueNotEvent(key, value)
        PreEventTrigger(key, value, oldValue)
    end
end

function SetPersonalInfoValueNotEvent(key, value)
    if key then
        personalInfoProp[key] = value
    end
end

function ClearPersonalInfoValue(key, value)
    personalInfoProp = {}
    EventFunction = nil

end
--endregion

event.Register(event.USER_DATA_RESET, ClearPersonalInfoValue)