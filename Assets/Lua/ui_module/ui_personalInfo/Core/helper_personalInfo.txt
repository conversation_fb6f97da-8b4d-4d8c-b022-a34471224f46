---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/8/30.
--- Desc: 个性化帮助类，主要处理策划统一的一些读表操作，统一数据处理之类的,避免每个脚本都写一遍逻辑，不方便修改
---

local print = print
local require = require
local table = table
local stringFormat = string.format
local tonumber = tonumber
local type = type
local tostring = tostring

local game_scheme = require "game_scheme"
local data_personalInfo = require "data_personalInfo"
local gameScheme = require "game_scheme"
local lang = require "lang"
local util = require "util"
local actor_face_data = require "actor_face_data"
local skep_mgr = require "skep_mgr"
local const = require "const"
local item_data = require "item_data"
local color_palette = require "color_palette"
local VertexGradient = CS.TMPro.VertexGradient
local custom_avatar_mgr = require "custom_avatar_mgr"
local custom_avatar_data = require "custom_avatar_data"
local string = string
local GWG = GWG

module("helper_personalInfo")

--region 获得个性化物品通用数据，对齐策划：代闯 修改日期：2024/8/30 
--- 统一规则的表：RoleFace RoleSchloss RoleFrame RoleTitle
--- 统一属性规则：
--- 1. 每张表的ID：对应Item表ID
--- 2. PieceParam：佩戴属性，读取GWMapEffect
--- 3. PieceParam2：拥有属性，读取GWMapEffect
--- 4. 品质：统一读取Item表的quality
--- 5. 名字：优先读取每张表的NameID，如果没有配置或者配置是0，则读取Item表的nameKey
local function getPersonalisedItemConfig(id, cfg, propId)
    --- 统一多张表的配置数据结构
    local config = {
        cfg = cfg,
        id = id,
        nameId = 0,
        iconId = 0,
        modelId = 0,
        quality = 1,

        propId = propId,
        priority = cfg.Priority,
        automaticEquipment = cfg.AutomaticEquipment,

        unlocked = false,
        expireTime = 0,
        useAttributes = {},
        haveAttributes = {}
    }

    local itemCfg = gameScheme:Item_0(id)
    if not itemCfg then
        print(stringFormat("Item表配置 %s 缺失", id))
        return
    end

    if cfg.NameID and cfg.NameID ~= 0 then
        config.nameId = cfg.NameID
    else
        config.nameId = itemCfg.nameKey
    end

    if cfg.rivalType and cfg.rivalType ~= 0 then
        config.iconId = cfg.rivalType
    else
        config.iconId = itemCfg.icon
    end
    -- 模型只在配置中设置
    if cfg.ModulID and cfg.ModulID ~= 0 then
        config.modelId = cfg.ModulID
    end
    config.quality = cfg.quality or itemCfg.quality
    -- 在背包中寻找数据
    local entity = skep_mgr.GetGoodsEntity(id)
    if entity then
        config.unlocked = true
        config.expireTime = entity:GetExpireTime()
    end

    config.useAttributes = { }
    if cfg.PieceParam and cfg.PieceParam.data then
        for i = 1, cfg.PieceParam.count do
            config.useAttributes[i] = cfg.PieceParam.data[i - 1]
        end
    end

    config.haveAttributes = { }
    if cfg.PieceParam2 and cfg.PieceParam2.data then
        for i = 1, cfg.PieceParam2.count do
            config.haveAttributes[i] = cfg.PieceParam2.data[i - 1]
        end
    end

    if propId == data_personalInfo.PropEnum.AnimalsID then
        config.unlocked = true
        config.expireTime = 0
        if GWG and GWG.GWHomeMgr and GWG.GWHomeMgr.droneData then
            config.adornID = GWG.GWHomeMgr.droneData.GetDroneId()
        end
    end
    return config
end

--- 获得个性化头像的显示配置
function GetFaceItemConfig(id, noDefault)
    local cfg = gameScheme:RoleFace_0(tonumber(id))
    if not cfg and not noDefault then
        local defaultFaceCfg = game_scheme:InitBattleProp_0(8127)
        if defaultFaceCfg and defaultFaceCfg.szParam and defaultFaceCfg.szParam.data then
            cfg = gameScheme:RoleFace_0(defaultFaceCfg.szParam.data[0])
        end
    end

    if cfg then
        -- 头像的解锁特殊处理
        local itemTable = getPersonalisedItemConfig(cfg.headID, cfg, data_personalInfo.PropEnum.FaceID)
        itemTable.unlocked = actor_face_data.isActiveRoleFace(itemTable.id)
        -- 自定义头像的特殊处理
        if itemTable.id < 10 then
            itemTable.nameId = 720106
            itemTable.unlocked = true
            itemTable.isCustomHead = true
            itemTable.customAvatarData = custom_avatar_data.GetMyAvatar()
            itemTable.delEvent = function()
                custom_avatar_mgr.DelCustomAvatar()
            end
            itemTable.clickFunc = function()
                --检查打开上传图片的先决条件，然后调用sdk的api打开上传图片的接口
                custom_avatar_mgr.CustomizeAvatar()
            end
            --自定义头像的开关
            if not const.ONOFF_CUSTOM_AVATAR then
                return nil
            end
        end

        return itemTable
    end
end

--- 获得个性化城堡的显示配置
function GetSchlossItemConfig(id, noDefault)
    local cfg = gameScheme:RoleSchloss_0(id)
    if not cfg and not noDefault then
        local defaultSchlossCfg = game_scheme:InitBattleProp_0(8226)
        if defaultSchlossCfg and defaultSchlossCfg.szParam and defaultSchlossCfg.szParam.data then
            cfg = gameScheme:RoleSchloss_0(defaultSchlossCfg.szParam.data[0])
        end
    end
    if cfg then
        return getPersonalisedItemConfig(cfg.CastleID, cfg, data_personalInfo.PropEnum.CityID)
    end
end

---获取个性化神兽装扮的显示配置
function GetAnimalItemConfig(id, Default)
    local cfg = gameScheme:AccessoryAdorn_0(id)
    if not cfg and Default then
        local defaultAnimalCfg = game_scheme:InitBattleProp_0(8234)
        if defaultAnimalCfg and defaultAnimalCfg.szParam and defaultAnimalCfg.szParam.data then
            cfg = gameScheme:AccessoryAdorn_0(defaultAnimalCfg.szParam.data[0])
        end
    end
    if cfg then
        local config = getPersonalisedItemConfig(cfg.adornID, cfg, data_personalInfo.PropEnum.AnimalsID)
        config.unlocked = IsActiveAnimalAdorn(cfg.adornID)
        return config
    end
end

function IsActiveAnimalAdorn(id)
    local defaultAnimalCfg = game_scheme:InitBattleProp_0(8234)
    if defaultAnimalCfg and defaultAnimalCfg.szParam and defaultAnimalCfg.szParam.data then
        local cfg = gameScheme:AccessoryAdorn_0(defaultAnimalCfg.szParam.data[0])
        if cfg and cfg.adornID == id then
            return true
        else
            local entity = skep_mgr.GetGoodsEntity(id)
            if entity then
                return true
            else
                return false
            end
        end
    end
    return false
end

--- 获得个性化头像框的显示配置
function GetFrameItemConfig(id, noDefault)
    local cfg = gameScheme:RoleFrame_0(id)
    if not cfg and not noDefault then
        local defaultFrameCfg = game_scheme:InitBattleProp_0(1228)
        if defaultFrameCfg and defaultFrameCfg.szParam and defaultFrameCfg.szParam.data then
            cfg = gameScheme:RoleFrame_0(defaultFrameCfg.szParam.data[0])
        end
    end
    if cfg then
        return getPersonalisedItemConfig(cfg.frameID, cfg, data_personalInfo.PropEnum.FrameID)
    end
end

--- 获得个性化称号的显示配置
function GetTitleItemConfig(id, noDefault)
    local cfg = gameScheme:RoleTitle_0(id)
    -- todo 默认称号
    if cfg then
        return getPersonalisedItemConfig(id, cfg, data_personalInfo.PropEnum.TitleID)
    end
end

--- 获得个性化信息的显示配置
function GetPersonalisedItemConfig(id)
    local faceCfg = GetFaceItemConfig(id, true)
    if faceCfg then
        return faceCfg
    end
    local schlossCfg = GetSchlossItemConfig(id, true)
    if schlossCfg then
        return schlossCfg
    end
    local frameCfg = GetFrameItemConfig(id, true)
    if frameCfg then
        return frameCfg
    end
    local titleCfg = GetTitleItemConfig(id, true)
    if titleCfg then
        return titleCfg
    end
    local animalCfg = GetAnimalItemConfig(id, true)
    if animalCfg then
        return animalCfg
    end
    return nil
end
--endregion

--region 获得属性描述和计算的值，对齐策划：代闯 修改日期：2024/8/29
--- 统一规则：所有属性统一在GWMapEffect表
--- 统一属性规则：
--- 名字：统一读取表的name，如果名字为nil 读proToLang的表
--- 值：统一读取表的strParam.data[0],目前的值使用万分比计算(固定)
function GetPersonalisedProperty(id)
    local cfg = gameScheme:GWMapEffect_0(id)
    if cfg then
        local groupId = cfg.nGroupID
        local nameId = cfg.name
        if nameId == 0 then
            local proToLangCfg = game_scheme:ProToLang_0(cfg.nGroupID)
            if proToLangCfg then
                nameId = proToLangCfg.iLangId
            end
        end
        local value = 0
        if cfg.strParam and cfg.strParam.count > 0 then
            value = cfg.strParam[0]
        end
        return groupId, nameId, value
    else
        return 0, 0, 0
    end
end

function GetPersonalisedPropertyArray(ids)
    local array = { }
    for i = 1, #ids do
        local groupId, nameId, value = GetPersonalisedProperty(ids[i])
        if groupId ~= 0 then
            array[groupId] = array[groupId] or {
                nameId = nameId,
                value = 0
            }
            array[groupId].value = array[groupId].value + value
        end
    end
    return array
end

local function toTenThousandthPercentage(number)
    if number and (type(number) == "number" or number % 1 == 0) then
        local percentage = (number / 10000) * 100
        return tostring(percentage) .. "%"  -- 将百分比取整并添加百分号
    end
end

function GetPersonalisedPropertyText(nameID, value)
    if nameID and value then
        local name = lang.Get(nameID)
        local valueStr = ""
        if value > 0 then
            valueStr = stringFormat("+%s", toTenThousandthPercentage(value))
        elseif value < 0 then
            valueStr = stringFormat("-%s", toTenThousandthPercentage(value))
        end
        return name, valueStr
    end
end
--endregion

--region 获得个性化配置的数组，对齐策划：代闯 修改日期：2024/8/30 
--- 统一规则的表：RoleFace RoleSchloss RoleFrame RoleTitle
--- 统一属性规则：取出对应的表信息,排序规则
--- 1. 已解锁 > 未解锁 unlocked
--- 2. 高优先级 > 低优先级,Priority
--- 3. 高品质 > 低品质,quality
--- 4. 所有获得的数组配置，都新增一个字段 unlocked
local function compareItems(a, b)
    if a.unlocked ~= b.unlocked then
        return a.unlocked and not b.unlocked  -- 已解锁的排在未解锁之前
    elseif a.priority ~= b.priority then
        return a.priority > b.priority  -- 高优先级排在低优先级之前
    else
        return a.quality > b.quality  -- 高品质排在低品质之前
    end
end

--- 获得头像配置数据集
function GetFaceConfigArray()
    local array = { }
    local count = gameScheme:RoleFace_nums()
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleFace(i)
        if util.compareServerTime(cfg.OpenTime) then
            local itemTable = GetFaceItemConfig(cfg.headID)
            if itemTable then
                table.insert(array, itemTable)
            end
        end
    end
    table.sort(array, compareItems)
    return array
end

--- 获得城堡配置数据集
--- 城堡的Icon特殊处理，需要读取Item表的icon
function GetSchlossConfigArray()
    local array = { }
    local count = gameScheme:RoleSchloss_nums()
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleSchloss(i)
        local itemTable = GetSchlossItemConfig(cfg.CastleID)
        if itemTable and (itemTable.unlocked or cfg.IsShow == 1) then
            table.insert(array, itemTable)
        end
    end
    table.sort(array, compareItems)
    return array
end

--- 获得个性化头像框的显示配置
function GetFrameConfigArray()
    local array = { }
    local count = gameScheme:RoleFrame_nums()
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleFrame(i)
        if cfg.isDelete == 0 then
            local itemTable = GetFrameItemConfig(cfg.frameID)
            if itemTable and (itemTable.unlocked or cfg.IsShow == 1) then
                table.insert(array, itemTable)
            end
        end
    end
    table.sort(array, compareItems)
    return array
end

--- 获得个性化称号的显示配置
function GetTitleConfigArray()
    local array = { }
    local count = gameScheme:RoleTitle_nums()
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleTitle(i)
        if cfg.LimitType ~= 6 then
            local itemTable = GetTitleItemConfig(cfg.TitleID)
            if itemTable and (itemTable.unlocked or cfg.IsShow == 1) then
                table.insert(array, itemTable)
            end
        end
    end
    table.sort(array, compareItems)
    return array
end

function GetAnimalConfigArray()
    local array = { }
    local count = gameScheme:AccessoryAdorn_nums()
    for i = 0, count - 1 do
        local cfg = gameScheme:AccessoryAdorn(i)
        local itemTable = GetAnimalItemConfig(cfg.adornID)
        if itemTable then
            table.insert(array, itemTable)
        end
    end
    return array
end
--endregion

--region 配置属性色，对齐美术：朱晓晶 修改日期：2024/8/30 
function SetQualityTMPColor(tmp, quality, useQualityColor2)
    if tmp and quality then
        local colorTable = useQualityColor2 and item_data.Item_Quality_ColorEnum2_GW[quality] or item_data.Item_Quality_ColorEnum1_GW[quality]
        if colorTable then
            tmp.color = color_palette.HexToColor(colorTable.vertexColor)
            tmp.enableVertexGradient = colorTable.gradient
            if colorTable.gradient then
                local gradient = VertexGradient(color_palette.HexToColor(colorTable.topColor), color_palette.HexToColor(colorTable.topColor), color_palette.HexToColor(colorTable.bottomColor), color_palette.HexToColor(colorTable.bottomColor));
                tmp.colorGradient = gradient;
            end
        end
    end
end

function SetQualityTextGradientColor(text, gradient, quality, useQualityColor2)
    if text and gradient and quality then
        local colorTable = useQualityColor2 and item_data.Item_Quality_ColorEnum2_GW[quality] or item_data.Item_Quality_ColorEnum1_GW[quality]
        if colorTable then
            text.color = color_palette.HexToColor(colorTable.vertexColor)
            gradient.enabled = colorTable.gradient
            if colorTable.gradient then
                gradient.TopColor = color_palette.HexToColor(colorTable.topColor)
                gradient.BottomColor = color_palette.HexToColor(colorTable.bottomColor)
            end
        end
    end
end
--endregion

--region 获得城堡模型的地址，对齐策划：代闯，修改日期：2024/10/21
SchlossPrefabType = {
    -- 沙盘
    sand = 1,
    -- 城建
    building = 2,
    -- 个性化
    personalised = 3,
}

function GetSchlossPrefabPath(prefabType, schlossId)
    local cityId = schlossId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.CityID)
    if cityId and cityId > 0 then
        local cfg = game_scheme:RoleSchloss_0(tonumber(cityId))
        if cfg then
            local helper_personalInfo = require "helper_personalInfo"
            if prefabType == helper_personalInfo.SchlossPrefabType.sand then
                return cfg.SandModuleRoute
            elseif prefabType == helper_personalInfo.SchlossPrefabType.building then
                return cfg.buildingModul
            else
                return cfg.ModulRoute
            end
        end
    end
    return nil
end
--endregion

--获取个性化神兽的模型地址
function GetAnimalPrefabPath(animalId)
    local animalId = animalId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID) --获取无人机装饰ID
    if animalId > 0 then
        local cfg = game_scheme:AccessoryAdorn_0(tonumber(animalId))
        if cfg then
            --print("GetAnimalPrefabPath",cfg.modelID)
            local modulCfg = game_scheme:Modul_0(cfg.modelID)
            local ModulePath = modulCfg.modelPath
            return ModulePath
        end
    end
    return nil
end