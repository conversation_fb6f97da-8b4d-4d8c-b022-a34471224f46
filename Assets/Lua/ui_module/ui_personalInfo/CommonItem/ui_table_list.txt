---
--- Created by: 袁楠.
--- DateTime: 2024/9/2.
--- Desc: 用于处理列表显示的功能类封装,目前主要用于一些通用的ScrollRectTable的逻辑操作
---

local print = print
local newClass = newclass
local typeof = typeof
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable

local sprite_asset = require "card_sprite_asset"
local http_inst = require "http_inst"
local flow_text = require "flow_text"
local lang = require "lang"
local const = require "const"
local custom_avatar_mgr = require "custom_avatar_mgr"

module("ui_table_list")
local tableList = newClass("ui_table_list")

function tableList:ctor()
    self.selectIndex = -1
    self.usedIndex = -1
    self.dataList = {}
    self.iconSprite = nil
    self.defaultSprite = nil
    self.OnClick = nil
end

function tableList:Init(gameObject, dataArray, onClickCallback, iconSpriteAsset)
    if gameObject then
        self.gameObject = gameObject
        self:Bind()

        self.OnClick = onClickCallback
        self.iconSprite = iconSpriteAsset
        self.defaultSprite = sprite_asset.CreateSpriteAsset()
        self.dataList = dataArray
        -- 绑定数据
        self.scrollRectTable:SetData(self.dataList, #self.dataList)
    else
        print("ui_table_list Init rectTransform is nil or not found scrollRectTable")
    end
    return self
end

function tableList:Bind()
    if self.gameObject then
        self.transform = self.gameObject.transform
        -- 找到子类的组件
        self.scrollRectTable = self.transform:GetComponentInChildren(typeof(ScrollRectTable))
        self.scrollRectTable.onItemRender = function(...)
            self:OnItemRender(...)
        end
        self.scrollRectTable.onItemDispose = function(...)
            self:OnItemDispose(...)
        end
    end
end

function tableList:SetActive(active)
    if self.gameObject then
        self.gameObject:SetActive(active)
    end
end

function tableList:SetTableDataList(arrayData)
    -- 刷新展示
    if self.scrollRectTable then
        self.dataList = arrayData
        self.scrollRectTable:SetData(self.dataList, #self.dataList)
        self.scrollRectTable:Refresh(0, -1)
    end
end

function tableList:SetTableIndex(usedId)
    if usedId and usedId ~= -1 then
        for i = 1, #self.dataList do
            local data = self.dataList[i]
            if data.isCustomHead and data.customAvatarData and data.customAvatarData.used==true then
                self.usedIndex = i
                self.selectIndex = i
                break
            end
            if data.id == usedId then
                if data.unlocked then
                    self.usedIndex = i
                end
                self.selectIndex = i
                break
            end
        end
    else
        self.usedIndex = -1
        self.selectIndex = -1
    end
    -- 刷新展示
    if self.scrollRectTable then
        self.scrollRectTable:Refresh(0, -1)
    end
end

function tableList:ClickSystemAvatar()
    local scroll_rect_item =  self.scrollRectTable:GetItem(2)
    if scroll_rect_item then
        local click = scroll_rect_item:Get("click")
        click.onClick:Invoke()
    end
end

function tableList:ClickCustomAvatar()
    local scroll_rect_item =  self.scrollRectTable:GetItem(0)
    if scroll_rect_item then
        self.selectIndex = scroll_rect_item.data[1]
        if self.selectItem then
            self:OnItemSetData(self.selectItem, self.selectItem.data[1], self.selectItem .data[2])
        end
        self.selectItem = scroll_rect_item
        self:OnItemSetData(self.selectItem, self.selectItem .data[1], self.selectItem .data[2])
        if self.OnClick then
            self.OnClick(scroll_rect_item.data[2], scroll_rect_item.rectTransform)
        end
    end
end

function tableList:RefreshCustomAvatar(customAvatarData)
    for i = 1, #self.dataList do
        local data = self.dataList[i]
        if data.isCustomHead then
            data.customAvatarData = customAvatarData
            break
        end
    end
    -- 刷新展示
    if self.scrollRectTable then
        self.scrollRectTable:Refresh(0, -1)
    end
end


function tableList:OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --if not scroll_rect_item.data.isFirst then
        local click = scroll_rect_item:Get("click")
        --自定义头像的按钮放在scroll里 需要特殊判断下
        if scroll_rect_item.data[2].isCustomHead then
            --如果已经获取到了头像的url 则点击是选中当前的头像效果
            if scroll_rect_item.data[2].customAvatarData ~= nil and scroll_rect_item.data[2].customAvatarData.remoteUrl ~=nil then
                --判断审核状态
                if scroll_rect_item.data[2].customAvatarData.status == const.Custom_Image_Enum.Reviewed or scroll_rect_item.data[2].customAvatarData.status == const.Custom_Image_Enum.MachineReviewedPass then
                    local function onItemClick()
                        if scroll_rect_item.data[2] and scroll_rect_item.data[2].customAvatarData == nil then
                            if scroll_rect_item.data[2].clickFunc then
                                scroll_rect_item.data[2].clickFunc()
                            end
                            return
                        end
                        if self.selectIndex ~= index then
                            self.selectIndex = scroll_rect_item.data[1]
                            if self.selectItem then
                                self:OnItemSetData(self.selectItem, self.selectItem.data[1], self.selectItem .data[2])
                            end
                            self.selectItem = scroll_rect_item
                            self:OnItemSetData(self.selectItem, self.selectItem .data[1], self.selectItem .data[2])
                        end
                        -- 執行邏輯
                        if self.OnClick then
                            self.OnClick(scroll_rect_item.data[2], scroll_rect_item.rectTransform)
                        end
                    end
                    click.onClick:AddListener(onItemClick)
                else

                    local function onItemClick()
                        if scroll_rect_item.data[2].customAvatarData.status == const.Custom_Image_Enum.Reviewed or scroll_rect_item.data[2].customAvatarData.status == const.Custom_Image_Enum.MachineReviewedPass then
                            return
                        end
                        flow_text.Add(lang.Get(650088))
                    end
                    click.onClick:RemoveAllListeners()
                    click.onClick:AddListener(onItemClick)
                end
                
            else
                --如果没有头像url 则打开sdk 请求上传一个
                local function onItemClick()
                    if scroll_rect_item.data[2] and scroll_rect_item.data[2].clickFunc then
                        scroll_rect_item.data[2].clickFunc()
                    end
                end
                click.onClick:RemoveAllListeners()
                click.onClick:AddListener(onItemClick)
            end
            
        else
            local function onItemClick()
                if self.selectIndex ~= index then
                    self.selectIndex = scroll_rect_item.data[1]
                    if self.selectItem then
                        self:OnItemSetData(self.selectItem, self.selectItem.data[1], self.selectItem .data[2])
                    end
                    self.selectItem = scroll_rect_item
                    self:OnItemSetData(self.selectItem, self.selectItem .data[1], self.selectItem .data[2])

                    -- 執行邏輯
                    if self.OnClick then
                        self.OnClick(scroll_rect_item.data[2], scroll_rect_item.rectTransform)
                    end
                end
            end
            click.onClick:RemoveAllListeners()
            click.onClick:AddListener(onItemClick)
        end
        
     
        --scroll_rect_item.data.isFirst = true
    --end
    if index == self.selectIndex then
        self.selectItem = scroll_rect_item
    end
    
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](dataItem)
        end
    end
    
    self:OnItemSetData(scroll_rect_item, index, dataItem)
end

function tableList:OnItemDispose(scroll_rect_item, index)
    if scroll_rect_item and scroll_rect_item.data then
        local click = scroll_rect_item:Get("click")
        click.onClick:RemoveAllListeners()
        local delBtn = scroll_rect_item:Get("delBtn")
        if delBtn then
            delBtn.onClick:RemoveAllListeners()
        end
 
        scroll_rect_item.data = nil
    end
end

function tableList:OnItemSetData(scroll_rect_item, index, dataItem)
    if scroll_rect_item then
        local selected = scroll_rect_item:Get("selected")
        if selected and not selected:IsNull() then
            selected.gameObject:SetActive(index == self.selectIndex);
        end

        local used = scroll_rect_item:Get("used")
        if used and not used:IsNull() then
            used.gameObject:SetActive(index == self.usedIndex);
        end

        local delBtn = scroll_rect_item:Get("delBtn")
        if delBtn and not delBtn:IsNull() then
            local isVerify = dataItem.customAvatarData ~=nil and (dataItem.customAvatarData.status == const.Custom_Image_Enum.Reviewed or dataItem.customAvatarData.status == const.Custom_Image_Enum.MachineReviewedPass)
            delBtn.gameObject:SetActive(dataItem.isCustomHead and dataItem.customAvatarData ~=nil and isVerify);
        end

        local locked = scroll_rect_item:Get("locked")
        if locked and not locked:IsNull() then
            locked.gameObject:SetActive(dataItem.unlocked == false);
        end

        local statusTxt = scroll_rect_item:Get("statusTxt")
        if statusTxt and not statusTxt:IsNull() then
            local isVerify = dataItem.customAvatarData ~=nil and (dataItem.customAvatarData.status == 1 or dataItem.customAvatarData.status == 2)
            statusTxt.text = lang.Get(650087)
            statusTxt.gameObject:SetActive(dataItem.isCustomHead and dataItem.customAvatarData ~=nil and not isVerify);
        end

        local icon = scroll_rect_item:Get("icon")
        if dataItem.customAvatarData ~=nil and dataItem.customAvatarData.localUrl ~= nil then
            custom_avatar_mgr.SetAvatarIcon(icon, dataItem.customAvatarData.localUrl)
        else
            self:LoadSpriteAsset(dataItem.iconId, icon, self.iconSprite)
        end

        local frame = scroll_rect_item:Get("frame")
        self:LoadSpriteAsset("newquality" .. dataItem.quality, frame)
    end
end

--- 设置背景图
function tableList:LoadSpriteAsset(spriteID, image, spriteAsset)
    if spriteID and image then
        local loadAsset = spriteAsset or self.defaultSprite
        if loadAsset then
            loadAsset:GetSprite(spriteID, function(sprite)
                if sprite and image and not image:IsNull() then
                    image.sprite = sprite
                end
            end)
        end
    end
end

function tableList:Dispose()
    if self.iconSprite then
        self.iconSprite:Dispose()
        self.iconSprite = nil
    end

    if self.defaultSprite then
        self.defaultSprite:Dispose()
        self.defaultSprite = nil
    end

    self.scrollRectTable.onItemRender = nil
    self.scrollRectTable.onItemDispose = nil

    self.selectItem = nil
    self.selectIndex = nil
    self.usedIndex = nil
    self.dataList = nil
    self.OnClick = nil
    self.gameObject = nil
    self.transform = nil
end

return tableList