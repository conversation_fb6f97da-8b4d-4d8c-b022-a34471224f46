-- author:  袁楠
-- date:    城池展示的RendererTexture数据
-- ver:     1.0
-- desc:    模型节点加载封装，用于需要有完成Camera、Mask和ActorNode的模型加载
--------------------------------------------------------------

local require = require
local typeof = typeof

local Camera = CS.UnityEngine.Camera
local RenderTexture = CS.UnityEngine.RenderTexture
local Transform = CS.UnityEngine.Transform
local Vector3 = CS.UnityEngine.Vector3
local LookAtConstraint = CS.UnityEngine.Animations.LookAtConstraint
local gw_gpu_animation_uitl = require "gw_gpu_animation_uitl"
local class = require "class"
local log = require "log"
local util = require "util"
local base_game_object = require "base_game_object"
local CardConfig = CS.War.Battle.CardConfig
local UIUtil = CS.Common_Util.UIUtil
module("ui_schloss_item")

local rootPath = "art/battleplayer/schlossview.prefab"

local SchlossItem = {}
local SchlossInstance = {}

local tag = "[ui_schloss_item]"

SchlossItem.widget_table = {
    modCamera = { path = "camera", type = Camera },
    lookAtConstraint = { path = "camera", type = LookAtConstraint },
    node = { path = "node", type = Transform },
}

function SchlossItem:ctor()
    self.__base:ctor(tag, tag)
    self.textureSize = { x = 1024, y = 1024 }
    self.position = { x = 0, y = 0, z = 0 }
    self.rotation = { x = 0, y = 0, z = 0 }
    self.lookAtConstraintIsOpen = true
    self.scale = 1

    self.height = 4.5
    self.modelPrefab = nil
    self.modelPath = nil
    self.rendererTexture = nil
    self.index = 0
    self.timer = nil
end

--param_1：string 模型组节点对象路径
--param_2：table<string> 模型对象路径list
--param_3：callback(texture) 加载完节点后回调（param:材质，用于赋值UI上的RawImage）
function SchlossItem:Init(modelPath, callback, textureSize,offsetPosition)
    if not rootPath then
        log.Error("Model Root assetBundle is null")
        return
    end

    if self.rendererTexture then
        log.Error("Model is Init finished ,Please use ChangeModel function")
        return
    end

    for i = 1, util.get_len(SchlossInstance) + 1 do
        if not SchlossInstance[i] then
            self.index = i
            SchlossInstance[i] = self
            break
        end
    end

    self.textureSize = textureSize or self.textureSize
    self.rendererTexture = RenderTexture.GetTemporary(self.textureSize.x, self.textureSize.y, 24)
    self.rendererTexture.name = "Schloss_Item_RT"

    self:LoadResource(rootPath, "", function(gameObject)
        self.modCamera.targetTexture = self.rendererTexture
        --载入模型
        self:SetNodeTransform(self.position, self.rotation, self.scale)
        self:SetLookAtConstraint(self.lookAtConstraintIsOpen)
        self:SetCameraDistance(self.height)
        self:ChangeModel(modelPath,offsetPosition)

        self.transform.position = Vector3(1000 * self.index, -2000, 0)
        if callback then
            callback(self.rendererTexture)
        end
    end, true)
    return self
end

function SchlossItem:ChangeModel(path,offsetPosition)
    if path and self.modelPath ~= path then
        self.modelPath = path
        if self.modelPrefab then
            self.modelPrefab:Dispose()
            self.modelPrefab = nil
        end

        self.modelPrefab = base_game_object(tag, tag)
        self.modelPrefab:LoadResource(self.modelPath, "", function(obj)
            if obj then 
                local CardConfigCom = obj:GetComponent(typeof(CardConfig))
                if CardConfigCom then 
                    local scale = CardConfigCom.size
                    obj.transform.localScale = {x=scale,y=scale,z=scale}
                end
                if offsetPosition then
                    UIUtil.SetLocalPos(obj.transform,offsetPosition.x or 0,offsetPosition.y or 0,offsetPosition.z or 0)
                end

                -- 重置动画播放
                gw_gpu_animation_uitl.ResetGpuAnimatorStandState(obj.transform)
            end
        end, true, self.node.transform)
    end
end

--设置LookAtConstraint
function SchlossItem:SetLookAtConstraint(open)
    if open == nil then
        open = true
    end
    self.lookAtConstraintIsOpen = open
    if self.lookAtConstraint then
        self.lookAtConstraint.enabled = open
    end
end

--设置LookAtConstraint
function SchlossItem:SetLookAtConstraintWeight(value)
    if self.timer then 
        util.RemoveDelayCall(self.timer)
        self.timer = nil
    end
    self.timer = util.DelayCallOnce(0.2,function() 
        if self.lookAtConstraint then 
            self.lookAtConstraint.weight=value
        end
    end)
end

-- 设置模型位置
function SchlossItem:SetNodeTransform(position, rotation, scale)
    if position then
        self.position = position
        if self.node then
            self.node.localPosition = position
        end
    end

    if rotation then
        self.rotation = rotation
        if self.node then
            self.node.eulerAngles = rotation
        end
    end

    if scale then
        self.scale = scale
        if self.node then
            self.node.localScale = { x = scale, y = scale, z = scale }
        end
    end
end

-- 设置摄像机高度
function SchlossItem:SetCameraDistance(height)
    if height then
        self.height = height
        if self.modCamera then
            self.modCamera.transform.localPosition = Vector3(0, height, -height)
        end
    end
end

--- 设置摄像机位置和大小
---@param cameraPos {x,y,z} @ 摄像机位置
---@param cameraSize number @ 摄像机大小
function SchlossItem:SetCameraInfo(cameraPos, cameraSize)
    if self.modCamera and not util.IsObjNull(self.modCamera) then
        if cameraPos then
            self.modCamera.transform.localPosition = cameraPos
        end

        if cameraSize then
            self.modCamera.orthographicSize = cameraSize
        end
    end
end

function SchlossItem:Hide()
    if self:IsValid() then
        self.gameObject:SetActive(false)
    end
end

function SchlossItem:Show()
    if self:IsValid() then
        self.gameObject:SetActive(true)
        return true
    end
end

function SchlossItem:Dispose()
    RenderTexture.ReleaseTemporary(self.rendererTexture) --释放渲染纹理，避免内存占e
    self.rendererTexture = nil
    self.modelPath = nil

    if self.modelPrefab then
        self.modelPrefab:Dispose()
        self.modelPrefab = nil
    end
    if self.timer then 
        util.RemoveDelayCall(self.timer)
        self.timer = nil
    end
    SchlossInstance[self.index] = nil
    self.__base:Dispose()
end

CSchlossItem = class(base_game_object, nil, SchlossItem)