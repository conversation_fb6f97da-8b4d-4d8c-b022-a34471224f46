---
--- Created by: 袁楠.
--- DateTime: 2024/9/2.
--- Desc: 模型绑定对象，并且展示在UI上
---

local newClass = newclass
local typeof = typeof
local RawImage = CS.UnityEngine.UI.RawImage
local Button = CS.UnityEngine.UI.Button
local UIUtil = CS.Common_Util.UIUtil

local log = require "log"
local schloss_item = require "ui_schloss_item"
local util = require "util"

module("ui_module_item")
local moduleItem = newClass("ui_module_item")

function moduleItem:ctor()
    self.OnClick = nil
    self.modelPath = nil
end

function moduleItem:Init(gameObject, onClickCallback)
    if gameObject then
        if onClickCallback then
            self.OnClick = function()
                if onClickCallback then
                    onClickCallback()
                end
            end
        end

        self.gameObject = gameObject
        self:Bind()
        self:Update()
    else
        log.LoginError("ui_module_item Init rectTransform is nil or not found rawImage")
    end
    return self
end

function moduleItem:Bind()
    if self.gameObject then
        self.transform = self.gameObject.transform

        -- 找到子类的组件
        self.rawImage = self.transform:GetComponentInChildren(typeof(RawImage))
        self.button = self.transform:GetComponentInChildren(typeof(Button))

        if self.button and self.OnClick then
            self.button.onClick:AddListener(self.OnClick)
        end
    end
end

--读取Mudul表的模型
function moduleItem:Update()
    if self.gameObject and self.modelPath then
        if self.model then
            self.model:ChangeModel(self.modelPath,self.offsetPos)
        else
            self.model = schloss_item.CSchlossItem():Init(self.modelPath, function(_rt)
                if not util.IsObjNull(self.gameObject) and not util.IsObjNull(self.rawImage) then
                    self.rawImage.texture = _rt
                    UIUtil.SetActive(self.rawImage, true)
                end
            end, false,self.offsetPos)
        end
    end
end

function moduleItem:SetActive(active)
    if self.gameObject and active then
        self.gameObject:SetActive(active)
    end
end

function moduleItem:SetChangeModel(modelPath,offsetPos)
    if modelPath and self.modelPath ~= modelPath then
        self.modelPath = modelPath
        self.offsetPos = offsetPos
        self:Update()
    end
end

-- 设置摄像机高度
function moduleItem:SetCameraDistance(height)
    if height and self.model then
        self.model:SetCameraDistance(height)
    end
end

-- 设置Node坐标
function moduleItem:SetNodePosition(x, y, z, needRotate)
    if self.model then
        local position = { x = x or self.model.position.x, y = y or self.model.position.y, z = z or self.model.position.z }
        if needRotate and self.model.rotation then
            position = UIUtil.GetPositionByEulerAngle(self.model.rotation.x, self.model.rotation.y, self.model.rotation.z, position.x, position.y, position.z)
        end
        self.model:SetNodeTransform(position)
    end
end

-- 设置Node旋转 
function moduleItem:SetNodeRotation(x, y, z)
    if self.model then
        local rotation = { x = x or self.model.rotation.x, y = y or self.model.rotation.y, z = z or self.model.rotation.z }
        self.model:SetNodeTransform(nil, rotation)
    end
end

function moduleItem:SetLookAtConstraint(open)
    if open == nil then
        open = true
    end
    if self.model then
        self.model:SetLookAtConstraint(open)
    end
end

-- 设置Node缩放
function moduleItem:SetNodeScale(nodeScale)
    if self.model then
        local scale = nodeScale or self.model.scale
        self.model:SetNodeTransform(nil, nil, scale)
    end
end

function moduleItem:SetLookAtConstraintWeight(value)
    if self.model then
        self.model:SetLookAtConstraintWeight(value)
    end
end

function moduleItem:Dispose()
    if self.model then
        self.model:Dispose()
        self.model = nil
    end

    self.offsetPos = nil
    self.button = nil
    self.OnClick = nil
    self.modelPath = nil
    self.gameObject = nil
    self.transform = nil
end

return moduleItem

