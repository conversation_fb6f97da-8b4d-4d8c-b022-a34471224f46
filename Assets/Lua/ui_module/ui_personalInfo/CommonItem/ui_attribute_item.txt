---
--- Created by: 袁楠.
--- DateTime: 2024/9/3
--- Desc: 属性Item对象,由于排列和显示逻辑各不相同，所以采用传递prefab过来的实例化方式,只提供功能的封装
---

local print_err = print_err
local require = require
local typeof = typeof
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local newclass = newclass

local helper_personalInfo = require "helper_personalInfo"

module("ui_attribute_item")
local attributeItem = newclass("ui_attribute_item")

function attributeItem:ctor()
    self.parent = nil
    self.attributeNameID = 0
    self.attributeValue = 0
end

function attributeItem:Init(prefab, parent)
    if prefab and parent then
        -- todo 如果后续有性能热点问题,需要封装对象池处理
        self.parent = parent
        self.gameObject = GameObject.Instantiate(prefab, parent, false);
        self:Bind()
        self:Update()
    else
        print_err("ui_attribute_item Init gameObject is nil")
    end
end

function attributeItem:Bind()
    if self.gameObject then
        self.transform = self.gameObject.transform

        -- 固定规则
        self.attributeNameText = self.transform:GetComponent(typeof(Text))
        self.attributeValueText = self.transform:Find("value"):GetComponent(typeof(Text))
    end
end

function attributeItem:Update()
    if self.gameObject then
        if self.attributeNameID and self.attributeNameID ~= 0 then
            local name, valueStr = helper_personalInfo.GetPersonalisedPropertyText(self.attributeNameID, self.attributeValue)
            self.gameObject:SetActive(true)
            self.attributeNameText.text = name
            self.attributeValueText.text = valueStr
        else
            self.gameObject:SetActive(false)
            self.attributeNameText.text = ""
            self.attributeValueText.text = ""
        end
    end
end

function attributeItem:SetActive(active)
    if self.gameObject then
        self.gameObject:SetActive(active)
    end
end

function attributeItem:SetAttribute(nameID, value)
    self.attributeNameID = nameID
    self.attributeValue = value
    self:Update()
end

function attributeItem:Dispose()
    self.gameObject = nil
    self.transform = nil
    self.parent = nil
    self.attributeNameID = 0
    self.attributeValue = 0
end

return attributeItem