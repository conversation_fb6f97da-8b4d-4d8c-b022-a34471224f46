---
--- Created by: 袁楠.
--- DateTime: 2024/8/29.
--- Desc: 属性模块组,默认规则
--- 1. 因为界面的变动很多,所以采用功能封装,View对象传递过来的方式
--- 2. 使用属性,在没有属性时显示：使用属性:无
--- 2. 拥有属性,在没有属性时显示：拥有属性:无
--- 3. todo 如果以后存在很多条的情况,考虑结合ScrollRectTable
---

local require = require
local typeof = typeof
local pairs = pairs
local Text = CS.UnityEngine.UI.Text
local newclass = newclass

local helper_personalInfo = require "helper_personalInfo"
local const_personalInfo = require "const_personalInfo"
local attribute_item = require "ui_attribute_item"
local lang = require "lang"

local function CreateAttributeItem(parent, prefab)
    if parent and prefab then
        local attributeItem = attribute_item:new()
        attributeItem:Init(prefab, parent)
        return attributeItem
    end
end

local function UpdateAttributeList(attributeIds, itemList, parent, prefabItem)
    local itemIndex = 1
    if attributeIds and #attributeIds > 0 then
        local attributeArray = helper_personalInfo.GetPersonalisedPropertyArray(attributeIds)
        for _, v in pairs(attributeArray) do
            itemList[itemIndex] = itemList[itemIndex] or CreateAttributeItem(parent, prefabItem)
            itemList[itemIndex]:SetAttribute(v.nameId, v.value)
            itemIndex = itemIndex + 1
        end
    end
    for i = itemIndex, #itemList do
        itemList[i]:SetAttribute(0, 0)
    end
    return itemIndex ~= 1  -- 为1 说明并未设置属性
end

module("ui_attribute_group")
local attributeGroup = newclass("ui_attribute_group")

function attributeGroup:ctor()
    self.usedIds = nil
    self.usedItems = {}
    self.haveIds = nil
    self.haveItems = {}
    self.attributePrefab = nil
end

function attributeGroup:Init(gameObject)
    if gameObject then
        self.gameObject = gameObject
        self:Bind()
        self:Update()
    end
    return self
end

function attributeGroup:Bind()
    if self.gameObject then
        self.transform = self.gameObject.transform
        -- 固定规则
        self.attributePrefab = self.transform:Find("itemPrefab")

        self.usedObj = self.transform:Find("usedAttribute").gameObject
        self.usedTitleText = self.transform:Find("usedAttribute/title"):GetComponent(typeof(Text))
        self.haveObj = self.transform:Find("haveAttribute").gameObject
        self.haveTitleText = self.transform:Find("haveAttribute/title"):GetComponent(typeof(Text))
    end
end

function attributeGroup:Update()
    if self.gameObject then
        local usedState = UpdateAttributeList(self.usedIds, self.usedItems, self.usedObj.transform, self.attributePrefab)
        local haveState = UpdateAttributeList(self.haveIds, self.haveItems, self.haveObj.transform, self.attributePrefab)
        --if not usedState and not haveState then -- todo 策划表示只对头像做隐藏处理
        --    self:SetActive(false)
        --else
        --    self:SetActive(true)
        if usedState then
            self.usedTitleText.text = lang.Get(const_personalInfo.lang_UseProp)
        else
            self.usedTitleText.text = lang.Get(const_personalInfo.lang_UseProp) .. " " .. lang.Get(const_personalInfo.lang_NA)
        end

        if haveState then
            self.haveTitleText.text = lang.Get(const_personalInfo.lang_HaveProp)
        else
            self.haveTitleText.text = lang.Get(const_personalInfo.lang_HaveProp) .. " " .. lang.Get(const_personalInfo.lang_NA)
        end
        --end
    end
end

function attributeGroup:SetActive(active)
    if self.gameObject and active ~= nil then
        self.gameObject:SetActive(active)
    end
end

function attributeGroup:SetAttributeIds(usedAttributes, haveAttributes, state)
    self.usedIds = usedAttributes
    self.haveIds = haveAttributes
    self:Update()
    self:SetActive(state)
end

function attributeGroup:Dispose()
    self.usedIds = nil
    if self.usedItems then
        for i = 1, #self.usedItems do
            self.usedItems[i]:Dispose()
        end
        self.usedItems = nil
    end

    self.haveIds = nil
    if self.haveItems then
        for i = 1, #self.haveItems do
            self.haveItems[i]:Dispose()
        end
        self.haveItems = nil
    end

    self.gameObject = nil
    self.transform = nil
    self.attributePrefab = nil
end

return attributeGroup
