using UnityEngine;

namespace CasualGame.lib_ChuagnYi.ImageEffects
{
    [ExecuteInEditMode, RequireComponent(typeof(Camera))]
    public class SetCameraDepth : MonoBehaviour
    {
        public bool             RenderDepth = true;
        public DepthTextureMode depthTextureMode;

        void OnEnable()
        {
            SetCameraDepthCore();
        }

        void OnValidate()
        {
            SetCameraDepthCore();
        }

        void SetCameraDepthCore()
        {
            var cam = GetComponent<Camera>();
            if (RenderDepth)
                cam.depthTextureMode |= DepthTextureMode.Depth;
            else
                cam.depthTextureMode = depthTextureMode;
            
            // Debug.Log($"cam.depthTextureMode: {cam.depthTextureMode}");
        }
    }
}