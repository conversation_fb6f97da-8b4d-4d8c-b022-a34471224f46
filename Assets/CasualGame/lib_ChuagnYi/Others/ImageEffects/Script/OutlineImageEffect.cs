using Sirenix.OdinInspector;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.ImageEffects
{
#if ChuangYiNoImageEffectAllowedInSceneView
#else
    [ImageEffectAllowedInSceneView]
#endif
    [ExecuteInEditMode, RequireComponent(typeof(Camera))]
    public class OutlineImageEffect : MonoBehaviour
    {
        public Color edgeColor       = Color.white;
        public Color edgeNormalColor = Color.white;

        public               bool  iScaleWithResolusion = true;
        [Range(0, 5)] public float thickness            = 1;
        [ShowInInspector]    float thickness2           = 1;

        [Space] public bool useDepth   = true;
        public         bool useNormals = false;

        [Header("Advanced settings")] [Space] public float minDepthThreshold   = 0f;
        public                                       float maxDepthThreshold   = 0.25f;
        [Space] public                               float minNormalsThreshold = 0f;
        public                                       float maxNormalsThreshold = 0.25f;

        public Material material;

        private Camera _camera;

        // public                  Shader shader;
        private static readonly int EdgeColorProperty         = Shader.PropertyToID("_EdgeColor");
        private static readonly int EdgeNormalColorProperty   = Shader.PropertyToID("_EdgeNormalColor");
        private static readonly int ThicknessProperty         = Shader.PropertyToID("_Thickness");
        private static readonly int DepthThresholdsProperty   = Shader.PropertyToID("_DepthThresholds");
        private static readonly int NormalsThresholdsProperty = Shader.PropertyToID("_NormalsThresholds");

        private void Start()
        {
            // material = new Material(shader);
            _camera = GetComponent<Camera>();
            UpdateShader();
        }

        void OnValidate()
        {
            // if (material == null) {
            //     material = new Material(shader);
            // }

            if (_camera == null)
            {
                _camera = GetComponent<Camera>();
            }

            UpdateShader();
        }

        [ImageEffectOpaque]
        void OnRenderImage(RenderTexture source, RenderTexture destination)
        {
            if (material == null)
            {
                // material = new Material(shader);
                return;
                UpdateShader();
            }

            if (_camera == null)
            {
                _camera = GetComponent<Camera>();
            }

#if UNITY_EDITOR
            minDepthThreshold   = Mathf.Clamp(minDepthThreshold, 0f, maxDepthThreshold);
            maxDepthThreshold   = Mathf.Max(0f, maxDepthThreshold);
            minNormalsThreshold = Mathf.Clamp(minNormalsThreshold, 0f, maxNormalsThreshold);
            maxNormalsThreshold = Mathf.Max(0f, maxNormalsThreshold);
            UpdateShader();
#endif

            Graphics.Blit(source, destination, material);
        }

        public void UpdateShader()
        {
            if (!material) return;
            const string depthKeyword = "OUTLINE_USE_DEPTH";
            if (useDepth)
            {
                material.EnableKeyword(depthKeyword);
                // _camera.depthTextureMode = DepthTextureMode.Depth;
            }
            else
            {
                material.DisableKeyword(depthKeyword);
            }

            const string normalsKeyword = "OUTLINE_USE_NORMALS";
            if (useNormals)
            {
                material.EnableKeyword(normalsKeyword);
                // _camera.depthTextureMode = DepthTextureMode.DepthNormals;
            }
            else
            {
                material.DisableKeyword(normalsKeyword);
            }

            material.SetColor(EdgeColorProperty,       edgeColor);
            material.SetColor(EdgeNormalColorProperty, edgeNormalColor);
            thickness2 = Mathf.Max(1f, thickness * Screen.height / 1280);
            material.SetFloat(ThicknessProperty, iScaleWithResolusion ? thickness2 : thickness);
            const float depthThresholdScale = 1e-3f;
            material.SetVector(DepthThresholdsProperty,
                    new Vector2(minDepthThreshold, maxDepthThreshold) * depthThresholdScale);
            material.SetVector(NormalsThresholdsProperty,
                    new Vector2(minNormalsThreshold, maxNormalsThreshold));
        }
    }
}