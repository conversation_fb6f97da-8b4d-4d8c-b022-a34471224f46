using Sirenix.OdinInspector;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.Helper
{
    public class SyncParameterMono : MonoBehaviour
    {
        [ShowInInspector]
        public ISync sync;
        public bool  autoUpdate;
        public bool  autoLateUpdate = true;

        private void Update()
        {
            if (autoUpdate)
                sync?.SyncFrom();
        }

        public void LateUpdate()
        {
            if (autoLateUpdate)
                sync?.SyncTo();
        }
    }
}