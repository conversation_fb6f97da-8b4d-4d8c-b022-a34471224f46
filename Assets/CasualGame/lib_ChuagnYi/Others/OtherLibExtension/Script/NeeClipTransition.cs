using System;
using System.Collections.Generic;
using Animancer;
using CasualGame.lib_ChuagnYi.NeeG;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.Helper
{
    [System.Serializable]
    public class ClipWithRootMotion
    {
        public ClipTransition clip;
        public Vector3        localPosition;
        public Vector3        localEulerAngles;
    }

    public class NeeClipTransition : MonoBehaviour
    {
        public UDictionary<string, ClipWithRootMotion> clipDict;

        public ClipWithRootMotion GetByName(string clipName)
        {
            if (clipDict.ContainsKey(clipName))
            {
                return clipDict[clipName];
            }

            return null;
        }
    }
}