using System;
using CasualGame.lib_ChuagnYi.NeeG;
using <PERSON>hapes;
using UnityEngine;
using XLua;

namespace CasualGame.lib_ChuagnYi
{
    [ExecuteAlways]
    public class LuaMonoEvent_ShapesDrawer : LuaMonoEvent_ImmediateModeShapeDrawer
    {
        public bool hasUICam;
        public bool hasMainCam;
        public bool hasAllCam;

        public Action<object, object> coreFuncMainCam;
        public Action<object, object> coreFuncUICam;
        public Action<object, object> coreFunc;
        public LuaTable               luaComp;

        public override void DrawShapes(Camera cam)
        {
            if (coreFuncMainCam != null && hasMainCam && cam == NeeGame.Instance.mainCam)
            {
                using (Draw.Command(cam))
                {
                    coreFuncMainCam?.Invoke(luaComp, cam);
                }
            }

            if (coreFuncUICam != null && hasUICam && cam == NeeGame.Instance.uiCam)
            {
                using (Draw.Command(cam))
                {
                    coreFuncUICam?.Invoke(luaComp, cam);
                }
            }

            if (coreFunc != null && hasAllCam)
            {
                using (Draw.Command(cam))
                {
                    coreFunc?.Invoke(luaComp, cam);
                }
            }
        }

        public override void Bind(LuaMono luaMono)
        {
            luaComp = luaMono.luaComp;
            if (hasAllCam)
                coreFunc = luaMono.luaComp.GetInPath<Action<object, object>>("OnDrawShapes");
            if (hasMainCam)
                coreFuncMainCam = luaMono.luaComp.GetInPath<Action<object, object>>("OnDrawShapesMainCam");
            if (hasUICam)
                coreFuncUICam = luaMono.luaComp.GetInPath<Action<object, object>>("OnDrawShapesUICam");
        }
    }
}