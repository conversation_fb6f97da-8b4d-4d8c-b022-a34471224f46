using System;
using System.Collections;
using CasualGame.lib_ChuagnYi.NeeG;
// using CasualGame.lib_ChuagnYi.NeeG.Fsm;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace CasualGame.lib_ChuagnYi
{
    [Serializable]
    public class PlayerData
    {
        public bool isCompletedTutorialOne;
    }

    public class UIMgr : NeeMonoSingleton<UIMgr>
    {
        [TabGroup("Canvas")] public Canvas worldCanvas;
        [TabGroup("Canvas")] public Canvas worldCanvasOverlay;
        [TabGroup("Canvas")] public Canvas screenCanvas;
        [TabGroup("Canvas")] public Canvas overlayCanvas;

        [TabGroup("Canvas")] public RectTransform worldCanvasUI3DParent;

        [TabGroup("Canvas")] public RectTransform screenCanvasParent;
        // [TabGroup("Canvas")] public Canvas worldCanvasOverlay;
        // [TabGroup("Canvas")] public Canvas screenCanvas;
        // [TabGroup("Canvas")] public Canvas overlayCanvas;

        private void Start()
        {


            NeeGame.RegisterOnAsyncInited(() =>
            {
                var cam = NeeGame.GetMgr<CameraMgr>();
                if (worldCanvas)
                    worldCanvas.worldCamera = NeeGame.Instance.mainCam;
                if (worldCanvasOverlay)
                    worldCanvasOverlay.worldCamera = NeeGame.Instance.mainCam;
                if (screenCanvas)
                    screenCanvas.worldCamera = NeeGame.Instance.mainCam;
                if (overlayCanvas)
                    overlayCanvas.worldCamera = NeeGame.Instance.mainCam;

            });
        }


        public IEnumerator OnGameRun()
        {

            yield return null;
        }

        public IEnumerator OnGameLoaded()
        {

            yield return null;
        }


        public void OnGameEndRun(bool isWin)
        {
           
        }
    }
}