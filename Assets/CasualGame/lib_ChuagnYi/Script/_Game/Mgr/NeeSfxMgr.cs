using System.Collections;
using System.Collections.Generic;
using System.Linq;
using CasualGame.lib_ChuagnYi.NeeG;
using UnityEngine;


namespace CasualGame.lib_ChuagnYi.UI
{
    public class NeeSfxMgr : NeeMonoSingleton<NeeSfxMgr>
    {
        public AudioSource prefab;
#if UNITY_EDITOR
        public List<AudioClip> clips;
#endif
        public UDictionary<string, AudioClip> dict;

        private bool isInited;

        private void Start()
        {
            if (prefab)
                NeeGame.RegisterOnAsyncInited(() =>
                {
                    NeeGame.WarmPool("PolySoundVfx", prefab.gameObject.GetComponent<AudioSource>(), 5);
                    isInited = true;
                });
            else
            {
                    isInited = true;
            }
        }

        private void OnValidate()
        {
#if UNITY_EDITOR
            clips = clips.Distinct().ToList();
            dict  = new UDictionary<string, AudioClip>();
            foreach (var kvp in clips)
                if (kvp != null)
                    dict.Add(kvp.name, kvp);
#endif
        }

        public AudioSource PlaySfxForAWhile(string soundName, float duration, float v = 1)
        {
            if (string.IsNullOrEmpty(soundName)) return null;
            if (!isInited) return null;
            if (dict.TryGetValue(soundName, out var clip))
            {
                var obj = NeeGame.PoolObject<AudioSource>("PolySoundVfx", transform);
                StartCoroutine(Core());
                obj.clip = clip;
                obj.loop = false;
                obj.Play();
                obj.name  = "as:" + soundName;
                obj.pitch = v;
                return obj;

                IEnumerator Core()
                {
                    yield return new WaitForSeconds(duration);
                    NeeGame.ReturnObject(obj);
                }
            }
            else
            {
                Debug.LogError("Sound not found: " + soundName);
                return null;
            }
        }

        public void BackSfx(AudioSource audio)
        {
            if (audio)
                NeeGame.ReturnObject(audio.gameObject);
        }

        public AudioSource PlaySfx(string soundName)
        {
            if (string.IsNullOrEmpty(soundName)) return null;
            if (!isInited) return null;
            if (dict.TryGetValue(soundName, out var clip))
            {
                var obj = NeeGame.PoolObject<AudioSource>("PolySoundVfx", transform);
                obj.clip = clip;
                obj.loop = true;
                obj.Play();
                obj.name = "as:" + soundName;
                return obj;
            }

            Debug.LogError("Sound not found: " + soundName);
            return null;
        }
    }
}