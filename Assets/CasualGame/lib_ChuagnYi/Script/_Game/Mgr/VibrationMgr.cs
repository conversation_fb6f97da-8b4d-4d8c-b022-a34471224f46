using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using bc.MiniGameBase;
using CasualGame.lib_ChuagnYi.NeeG;

namespace CasualGame.lib_ChuagnYi
{
    /// <summary>
    /// Enum that represents all the types of haptic presets available
    /// </summary>
    public enum HapticType
    {
        Selection    = 0,
        Success      = 1,
        Warning      = 2,
        Failure      = 3,
        LightImpact  = 4,
        MediumImpact = 5,
        HeavyImpact  = 6,
        RigidImpact  = 7,
        SoftImpact   = 8,
        None         = -1
    }

    public class VibrationMgr : NeeMonoSingleton<VibrationMgr>
    {
        public bool  log    = true;
        public float minFps = 35f;
        public bool  isOn   =true;
        // public bool   isVibrate;
        // public bool   isVersionSupported;        //是否支持简单震动
        // public bool   meetsAdvancedRequirements; //是否支持进阶震动
        public FpsMgr _fpsMgr;

        private void Awake()
        {
            // isVersionSupported        = DeviceCapabilities.isVersionSupported;
            // meetsAdvancedRequirements = DeviceCapabilities.meetsAdvancedRequirements;
        }

        /// <summary>
        /// 简单震动
        /// </summary>
        /// <param name="strengh">Range:0-1</param>
        /// <param name="frequency">Range:0-1</param>
        public void PlayEmphasis(float strengh, float frequency)
        {
            if (!isOn) return;
            if (NeeGame.Instance && !_fpsMgr)
            {
                _fpsMgr = NeeGame.GetMgr<FpsMgr>();
            }

            if (_fpsMgr && _fpsMgr.fps < minFps)
            {
                return;
            }

            MiniVibration.PlayEmphasis(strengh, frequency);
        }

        public void PlayConstant(float strengh, float frequency, float duration)
        {
            if (!isOn) return;
            if (NeeGame.Instance && !_fpsMgr)
            {
                _fpsMgr = NeeGame.GetMgr<FpsMgr>();
            }

            if (_fpsMgr && _fpsMgr.fps < minFps)
            {
                return;
            }

            MiniVibration.PlayConstant(duration,strengh, frequency);
        }

        /// <summary>
        /// 进阶震动
        /// </summary>
        /// <param name="presetType"></param>
        public void PlayPreset(HapticType presetType)
        {
            if (!isOn) return;
            // Vibrate(0.5f, 0.5f);
            // return;
            if (NeeGame.Instance)
            {
                _fpsMgr = NeeGame.GetMgr<FpsMgr>();
            }

            if (_fpsMgr && _fpsMgr.fps < minFps)
            {
                return;
            }

#if UNITY_EDITOR
            if (log)
                Debug.Log($"Vib: {presetType}");
#endif
            MiniVibration.PlayPreset((MiniVibration.PresetType) (int) presetType);
        }
    }
}