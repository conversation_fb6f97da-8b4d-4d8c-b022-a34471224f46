using System;
using System.Linq;
using CasualGame.lib_ChuagnYi.NeeG;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using UnityEngine.Events;

namespace CasualGame.lib_ChuagnYi
{
    public class NeeLevel : NeeMono
    {
        [TabGroup("config")] public string levelPrefix = "level";
        [TabGroup("config")] public int    levelIndex  = 0;

        protected virtual void OnValidate()
        {
#if UNITY_EDITOR
            // groups = GetComponentsInChildren<NeeLevelGroup>(true).ToList();
            var temp = levelPrefix;
            var name = gameObject.name;
            if (name.Length > temp.Length)
            {
                var indexStr = name.Substring(temp.Length, name.Length - temp.Length);
                if (int.TryParse(indexStr, out var index)) levelIndex = index;
            }
#endif
        }

#if UNITY_EDITOR
        [Button("设为唯一", ButtonSizes.Large)]
        public virtual void SetTheOnlyActive()
        {
            var all = FindObjectsOfType<NeeLevel>(true);
            foreach (var level in all)
            {
                level.transform.position = Vector3.zero;
                if (level != this)
                    level.gameObject.SetActive(false);
                else
                    level.gameObject.SetActive(true);
            }
        }
#endif
    }
}