using System;
using System.IO;
using System.Text.RegularExpressions;
using Sirenix.OdinInspector;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;

#endif

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public class NeePlugin : ScriptableObject
    {
        [InfoBox("所有Plugin在接入时候，请把压缩包中Editor代码删除，自己项目中的Editor不需要")]
        [Multiline] public string description;

        [ReadOnly]              public string pluginName;
        [FolderPath]            public string pluginPath;
        [Space] [FolderPath]    public string artFolderCur;
        [FolderPath] [ReadOnly] public string artFolderTar;
        [Space] [FolderPath]    public string dataFolderCur;
        [FolderPath] [ReadOnly] public string dataFolderTar;
        [Space] [FolderPath]    public string luaFolderCur;
        [FolderPath] [ReadOnly] public string luaFolderTar;
        [Space] [FolderPath]    public string matFolderCur;
        [FolderPath] [ReadOnly] public string matFolderTar;
        [Space] [FolderPath]    public string otherFolderCur;
        [FolderPath] [ReadOnly] public string otherFolderTar;
        [Space] [FolderPath]    public string prefabFolderCur;
        [FolderPath] [ReadOnly] public string prefabFolderTar;
        [Space] [FolderPath]    public string scriptFolderCur;
        [FolderPath] [ReadOnly] public string scriptFolderTar;
        [Space] [FolderPath]    public string shaderFolderCur;
        [FolderPath] [ReadOnly] public string shaderFolderTar;

#if UNITY_EDITOR
        [Button]
        public void OnValidate()
        {
            try
            {
                var path = AssetDatabase.GetAssetPath(this);
                // string dir = Application.dataPath.Remove(Application.dataPath.Length - 6, 6) + Path.GetDirectoryName(path).Replace("\\", "/");
                pluginPath = Path.GetDirectoryName(path);
                pluginName = Path.GetFileName(pluginPath);
                var match = Regex.Matches(pluginPath, @"([A-Z])\w+");
                // gameDir = match[2].ToString();

                // var scriptFolderCurTemp = $"{pluginPath}\\{NeeGamePath.Script}";
                // if (Directory.Exists(scriptFolderCurTemp))
                // {
                //     scriptFolderCur = scriptFolderCurTemp;
                //     scriptFolderTar = $"{NeeGamePath.Instance.scriptPath}\\{pluginName}\\{NeeGamePath.Script}";
                // }
                InitPathPair(NeeGamePath.Art,    ref artFolderCur,    ref artFolderTar);
                InitPathPair(NeeGamePath.Data,   ref dataFolderCur,   ref dataFolderTar);
                InitPathPair(NeeGamePath.Lua,    ref luaFolderCur,    ref luaFolderTar);
                InitPathPair(NeeGamePath.Mat,    ref matFolderCur,    ref matFolderTar);
                InitPathPair(NeeGamePath.Other,  ref otherFolderCur,  ref otherFolderTar);
                InitPathPair(NeeGamePath.Prefab, ref prefabFolderCur, ref prefabFolderTar);
                InitPathPair(NeeGamePath.Script, ref scriptFolderCur, ref scriptFolderTar);
                InitPathPair(NeeGamePath.Shader, ref shaderFolderCur, ref shaderFolderTar);
            }
            catch (Exception e)
            {
            }

            void InitPathPair(string s1, ref string s2, ref string s3)
            {
                s2 = null;
                s3 = null;
                var temp = $"{pluginPath}\\{s1}";
                if (Directory.Exists(temp))
                {
                    s2 = temp;
                    s3 = $"{NeeGamePath.Instance.gamePath}\\{s1}\\{pluginName}";
                }
            }
        }

        [Button("把除了代码以外的移出去")]
        public void MoveExceptCode()
        {
            MovePath(artFolderCur,    artFolderTar);
            MovePath(dataFolderCur,   dataFolderTar);
            MovePath(luaFolderCur,    luaFolderTar);
            MovePath(matFolderCur,    matFolderTar);
            MovePath(otherFolderCur,  otherFolderTar);
            MovePath(prefabFolderCur, prefabFolderTar);
            MovePath(shaderFolderCur, shaderFolderTar);
        }

        [Button("把除了代码以外的移回来")]
        public void MoveExceptCodeRevert()
        {
            MovePathRevert(artFolderCur,    artFolderTar);
            MovePathRevert(dataFolderCur,   dataFolderTar);
            MovePathRevert(luaFolderCur,    luaFolderTar);
            MovePathRevert(matFolderCur,    matFolderTar);
            MovePathRevert(otherFolderCur,  otherFolderTar);
            MovePathRevert(prefabFolderCur, prefabFolderTar);
            MovePathRevert(shaderFolderCur, shaderFolderTar);
        }

        [Button("把代码移出去")]
        public void MoveCode()
        {
            MovePath(scriptFolderCur, scriptFolderTar);
        }

        [Button("把代码移回来")]
        public void MoveCodeRevert()
        {
            MovePathRevert(scriptFolderCur, scriptFolderTar);
        }

        [Button("给shader改路径")]
        public void ShaderPathChange()
        {
            NeeFileContentChangeHelper.ShaderPathChange(shaderFolderCur);
            NeeFileContentChangeHelper.ShaderPathChange(shaderFolderTar);
        }

        [Button("！！刷插件guid，请做好备份！！")]
        public void GuidRegen()
        {
            NeeGuidRegeneratorMenu.RegenerateGuids(pluginPath);
        }

        private void MovePathRevert(string s2, string s3)
        {
            MovePath(s3, s2);
        }

        private void MovePath(string s2, string s3)
        {
            if (string.IsNullOrEmpty(s2) || string.IsNullOrEmpty(s3))
            {
                // Debug.Log($"有空路径");
                return;
            }

            if (Directory.Exists(s3))
            {
                Directory.Delete(s3, true);
            }

            Directory.Move(s2, s3);

            if (Directory.Exists(s2))
            {
                Directory.Delete(s2, true);
            }

            if (!Directory.Exists(s2))
            {
                Directory.CreateDirectory(s2);
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
#endif
    }
}