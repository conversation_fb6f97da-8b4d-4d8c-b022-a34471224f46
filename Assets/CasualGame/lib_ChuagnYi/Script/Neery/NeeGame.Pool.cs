using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public partial class NeeGame
    {
        public static void DestroyNee(GameObject obj)
        {
            if (Application.isPlaying)
                    // if (!Addressables.ReleaseInstance(obj))
                    // {
                Destroy(obj);
            // }
            // else
            // {
            // }
            else
                DestroyImmediate(obj);
        }

        public static void WarmPool(string poolName, Object prefab, int size = 2)
        {
            if (!ApplicationIsQuitting)
                GetMgr<NeePoolMgr>()?.warmPool(poolName, prefab, size);
        }

        public static T PoolObject<T>(string poolName, Transform parent = null) where T : Object
        {
            return GetMgr<NeePoolMgr>()?.spawnObject<T>(poolName, parent);
        }

        public static Object PoolObject(string poolName, Transform parent = null, bool normalize = true)
        {
            return GetMgr<NeePoolMgr>()?.spawnObject<Object>(poolName, parent, normalize);
        }

        public static void AddNewObjectPool(string poolName,GameObject newGo,int size=1)
        {
             GetMgr<NeePoolMgr>()?.AddObjectPool(poolName, newGo,size);
        }

        public static T PoolObject<T>(string    poolName, Vector3 position, Quaternion rotation,
                                      Transform parent = null) where T : Object
        {
            return GetMgr<NeePoolMgr>()?.spawnObject<T>(poolName, position, rotation, parent);
        }

        public static Object PoolObject(string    poolName, Vector3 position, Quaternion rotation,
                                        Transform parent = null)
        {
            return GetMgr<NeePoolMgr>()?.spawnObject<Object>(poolName, position, rotation, parent);
        }

        public static void ReturnObject(Object clone, bool returnToPool = true, bool reparent = true)
        {
            if (!ApplicationIsQuitting && clone)
                GetMgr<NeePoolMgr>()?.releaseObject(clone, returnToPool, reparent);
        }

        public static bool InPool(string poolName)
        {
            return GetMgr<NeePoolMgr>().InPool(poolName);
        }
    }
}