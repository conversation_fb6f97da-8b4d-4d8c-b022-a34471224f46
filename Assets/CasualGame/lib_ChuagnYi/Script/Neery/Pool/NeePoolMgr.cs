using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public class NeePoolMgr : NeeMonoSingleton<NeePoolMgr>
    {
        [Space] public NeeReferSo            poolRefer;
        public         List<NeePooledObject> poolOjectList;
        [Space] public bool                  logStatus;
        public         Transform             root;
        public         Transform             poolDefaultParent;

        private                          bool                                      dirty;
        [ShowInInspector]         public Dictionary<int, NeeObjectPool<Object>> instanceLookup;
        [Space] [ShowInInspector] public Dictionary<string, NeeObjectPool<Object>> prefabLookup;

        public override bool needAysncInit => true;
        // public Dictionary<string, IDisposable> poolByName;

        private void Awake()
        {
            poolOjectList = new List<NeePooledObject>();
            // poolByName    = new Dictionary<string, IDisposable>();
            prefabLookup   = new Dictionary<string, NeeObjectPool<Object>>();
            instanceLookup = new Dictionary<int, NeeObjectPool<Object>>();

            for (var i = 0; i < poolRefer.data.Count; i++)
                if (poolRefer.data[i].gameObject is GameObject || poolRefer.data[i].gameObject is Component)
                {
                    // var newRefer = _neeLoadingHelper.NewLoadingRefer();
                    var x = poolRefer.data[i].gameObject;
                    // {
                    poolOjectList.Add(new NeePooledObject
                    {
                            go         = x,
                            prefabName = poolRefer.data[i].key
                    });
                    // });
                }

            foreach (var poolObject in poolOjectList) warmPool(poolObject.prefabName, poolObject.go,10);
            SetAysncInitBool(true);
            // StartCoroutine(WaitAllLoaded());
        }

        public void AddObjectPool(string key,GameObject newGo,int size)
        {
            if(poolOjectList!=null)
            {
                if (prefabLookup.ContainsKey(key))
                    return;
                poolOjectList.Add(new NeePooledObject
                {
                    go = newGo,
                    prefabName = key
                });
                warmPool(key, newGo,size);
            }
        }

        private void Update()
        {
            if (logStatus && dirty)
            {
                PrintStatus();
                dirty = false;
            }
        }

        protected override void OnDestroy()
        {
            foreach (var pool in prefabLookup.Values) pool.Dispose();

            base.OnDestroy();
        }

        // private void Start()
        // {
        //     NeeGame.RegisterOnAsyncInited(() => { this.gameObject.SetActive(false); });
        // }


        private IEnumerator WaitAllLoaded()
        {
            yield return null;
            // yield return _neeLoadingHelper.WaitAllLoaded(GetType().Name);
            // _neeLoadingHelper = null;
            // foreach (var poolObject in poolOjectList) warmPool(poolObject.prefabName, poolObject.go);
            // SetAysncInitBool(true);
        }

        public void warmPool(string poolName, Object prefab, int size = 2)
        {
            if (!prefab) return;
            if (prefabLookup.ContainsKey(poolName))
            {
                // Debug.LogWarning("Pool for prefab " + poolName + " has already been created");
                return;
            }

            var pool = new NeeObjectPool<Object>(() => { return InstantiatePrefab(prefab); }, size);
            foreach (var p in pool.list)
            {
                switch (p.Item)
                {
                    case GameObject go2:
                        go2?.gameObject.SetActive(false);
                        break;

                    case Component comp2:
                        comp2?.gameObject.SetActive(false);
                        break;
                }
            }

            prefabLookup[poolName] = pool;

            dirty = true;
        }

        public T spawnObject<T>(string poolName, Transform parent = null,bool normalize = true) where T : Object
        {
            var obj = spawnObject<T>(poolName, Vector3.zero, Quaternion.identity, parent, normalize);
            switch (obj)
            {
                case GameObject go2:
                    go2.SetActive(true);
                    break;

                case Component comp2:
                    comp2.gameObject.SetActive(true);
                    break;
            }

            return obj;
        }

        // public GameObject spawnObject(string poolName, Vector3 position, Quaternion rotation)
        // {
        //     if (!prefabLookup.ContainsKey(poolName))
        //     {
        //         Debug.LogError("no pool");
        //         return null;
        //     }
        //
        //     var pool = prefabLookup[poolName];
        //
        //     var clone = pool.GetItem();
        //     clone.transform.SetPositionAndRotation(position, rotation);
        //     clone.SetActive(true);
        //
        //     instanceLookup.Add(clone, pool);
        //     dirty = true;
        //     return clone;
        // }

        public bool InPool(string poolName)
        {
            return prefabLookup.ContainsKey(poolName);
        }

        public T spawnObject<T>(string poolName, Vector3 position, Quaternion rotation, Transform parent = null,bool normalize = true)
                where T : Object
        {
            if (!prefabLookup.ContainsKey(poolName))
            {
                Debug.LogError($"no pool : {poolName}");
                return null;
            }

            var pool = prefabLookup[poolName];

            var clone                    = pool.GetItem();
            while (clone == false) clone = pool.GetItem();

            switch (clone)
            {
                case GameObject go2:
                    if (parent != null)
                        go2.transform.SetParent(parent);
                    else
                        go2.transform.SetParent(root);

                    if(normalize)
                    {
                        go2.transform.SetPositionAndRotation(position, rotation);
                    }
                    go2.SetActive(true);

                    instanceLookup.Add(go2.GetInstanceID(), pool);
                    dirty = true;
                    if (go2 is T)
                    {
                        return go2 as T;
                    }
                    else
                    {
                        return go2.GetComponent<T>();
                    }

                    break;

                case Component comp2:
                    if (parent != null)
                        comp2.transform.SetParent(parent);
                    else
                        comp2.transform.SetParent(root);

                    comp2.transform.SetPositionAndRotation(position, rotation);
                    comp2.gameObject.SetActive(true);

                    instanceLookup.Add(comp2.gameObject.GetInstanceID(), pool);
                    dirty = true;
                    if (comp2 is T)
                    {
                        return comp2 as T;
                    }
                    else
                    {
                        return comp2.GetComponent<T>();
                    }

                    break;
            }

            Debug.LogError($"clone [{clone.name}] is not go or comp");
            return null;
        }

        public void releaseObject(Object clone, bool returnToPool = true, bool reparent = true)
        {
            int id = -1;
            switch (clone)
            {
                case GameObject go2:
                    id = go2.GetInstanceID();
                    break;

                case Component comp2:
                    id = comp2.gameObject.GetInstanceID();
                    break;
            }
            if (instanceLookup.ContainsKey(id))
            {
                instanceLookup[id].ReleaseItem(clone);
                instanceLookup.Remove(id);
                dirty = true;
                if (returnToPool)
                {
                    if (poolDefaultParent != null)
                    {
                        switch (clone)
                        {
                            case GameObject go2:
                                if (reparent)
                                {
                                    go2.transform.SetParent(poolDefaultParent);
                                }
                                else
                                {
                                    go2.SetActive(false);
                                }
                                break;

                            case Component comp2:
                                if (reparent)
                                {
                                    comp2.transform.SetParent(poolDefaultParent);
                                }
                                else
                                {
                                    comp2.gameObject.SetActive(false);
                                }
                                break;
                        }
                    }

                    // clone.SetActive(false);
                }
                else
                {
                    switch (clone)
                    {
                        case GameObject go2:
                            NeeGame.DestroyNee(go2);
                            break;

                        case Component comp2:
                            NeeGame.DestroyNee(comp2.gameObject);
                            break;
                    }
                }
            }
            else
            {
                switch (clone)
                {
                    case GameObject go2:
                        NeeGame.DestroyNee(go2);
                        break;

                    case Component comp2:
                        NeeGame.DestroyNee(comp2.gameObject);
                        break;
                }

#if UNITY_EDITOR
                //Debug.LogWarning("No pool contains the object: " + clone.name);
#endif
            }
        }

        private Object InstantiatePrefab(Object prefab)
        {
            var go = Instantiate(prefab);
            if (root != null)
            {
                switch (go)
                {
                    case GameObject go2:
                        go2.transform.SetParent(root);
                        break;

                    case Component comp2:
                        comp2.transform.SetParent(root);
                        break;
                }
            }

            return go;
        }

        public void PrintStatus()
        {
            foreach (var keyVal in prefabLookup)
                Debug.Log(string.Format("Object Pool for Prefab: {0} In Use: {1} Total {2}", keyVal.Key,
                        keyVal.Value.CountUsedItems, keyVal.Value.Count));
        }
    }
}