using System.Collections.Generic;
using System.Linq;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public class NeeReferSo : ScriptableObject
    {
        public List<NeeReferCollectionData> data = new List<NeeReferCollectionData>();

        private readonly Dictionary<string, Object> dict = new Dictionary<string, Object>();

#if UNITY_EDITOR
        public void Add(string key, Object obj)
        {
            SerializedObject   serializedObject = new SerializedObject(this);
            SerializedProperty dataProperty     = serializedObject.FindProperty("data");
            int                i;
            for (i = 0; i < data.Count; i++)
            {
                if (data[i].key == key)
                {
                    break;
                }
            }

            if (i != data.Count)
            {
                SerializedProperty element = dataProperty.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("gameObject").objectReferenceValue = obj;
            }
            else
            {
                dataProperty.InsertArrayElementAtIndex(i);
                SerializedProperty element = dataProperty.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("key").stringValue                 = key;
                element.FindPropertyRelative("gameObject").objectReferenceValue = obj;
            }

            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        public void Remove(string key)
        {
            SerializedObject   serializedObject = new SerializedObject(this);
            SerializedProperty dataProperty     = serializedObject.FindProperty("data");
            int                i;
            for (i = 0; i < data.Count; i++)
            {
                if (data[i].key == key)
                {
                    break;
                }
            }

            if (i != data.Count)
            {
                dataProperty.DeleteArrayElementAtIndex(i);
            }

            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        public void Clear()
        {
            SerializedObject serializedObject = new SerializedObject(this);
            var              dataProperty     = serializedObject.FindProperty("data");
            dataProperty.ClearArray();
            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        public void Sort()
        {
            SerializedObject serializedObject = new SerializedObject(this);
            data.Sort(new NeeReferCollectionDataComparer());
            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }
#endif

        public T Get<T>(string key) where T : class
        {
            Object dictGo;
            if (!dict.TryGetValue(key, out dictGo))
            {
                return null;
            }

            return dictGo as T;
        }

        public Object Get(string key)
        {
            Object dictGo;
            if (!dict.TryGetValue(key, out dictGo))
            {
                return null;
            }

            return dictGo;
        }

        public void OnBeforeSerialize()
        {
        }

        public void OnAfterDeserialize()
        {
            dict.Clear();
            foreach (NeeReferCollectionData referenceCollectorData in data)
            {
                if (!dict.ContainsKey(referenceCollectorData.key))
                {
                    dict.Add(referenceCollectorData.key, referenceCollectorData.gameObject);
                }
            }
        }
    }
}