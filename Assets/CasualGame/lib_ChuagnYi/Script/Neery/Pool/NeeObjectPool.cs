using System;
using System.Collections.Generic;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public class NeeObjectPool<T> : IDisposable
    {
        private readonly Func<T>                                  factoryFunc;
        private          int                                      lastIndex;
        public           List<NeeObjectPoolContainer<T>>          list;
        private readonly Dictionary<T, NeeObjectPoolContainer<T>> lookup;

        public NeeObjectPool(Func<T> factoryFunc, int initialSize)
        {
            this.factoryFunc = factoryFunc;

            list   = new List<NeeObjectPoolContainer<T>>(initialSize);
            lookup = new Dictionary<T, NeeObjectPoolContainer<T>>(initialSize);

            Warm(initialSize);
        }

        public int Count => list.Count;

        public int CountUsedItems => lookup.Count;

        public void Dispose()
        {
            foreach (var item in list) ReleaseItem(item.Item);
        }

        private void Warm(int capacity)
        {
            for (var i = 0; i < capacity; i++)
            {
                var go = CreateContainer();
            }
        }

        private NeeObjectPoolContainer<T> CreateContainer()
        {
            var container = new NeeObjectPoolContainer<T>();
            container.Item = factoryFunc();
            list.Add(container);
            return container;
        }

        public T GetItem()
        {
            NeeObjectPoolContainer<T> container = null;
            for (var i = 0; i < list.Count; i++)
            {
                lastIndex++;
                if (lastIndex > list.Count - 1) lastIndex = 0;

                if (list[lastIndex].Used)
                {
                }
                else
                {
                    container = list[lastIndex];
                    break;
                }
            }

            if (container == null) container = CreateContainer();

            container.Consume();
            lookup.Add(container.Item, container);
            return container.Item;
        }

        public void ReleaseItem(object item)
        {
            ReleaseItem((T) item);
        }

        public void ReleaseItem(T item)
        {
            if (lookup.ContainsKey(item))
            {
                var container = lookup[item];
                container.Release();
                lookup.Remove(item);
            }
        }
    }
}