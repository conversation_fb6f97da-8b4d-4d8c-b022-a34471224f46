using System;
using System.Collections.Generic;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public partial class NeeGame
    {
        [SerializeField] private UDictionary<string, INeeSingleton> mgrDict;
        [SerializeField] private UDictionary<INeeSingleton, bool>   initDict;
        private readonly         List<string>                       mgrNameList = new List<string>();


        public static T AddMgr<T>() where T : MonoBehaviour, INeeSingleton, INeeAsync
        {
            Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
            Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();

            var t = typeof(T);
            if (Instance.mgrDict.ContainsKey(t.Name))
            {
                if (DebugMgr)
                    Debug.Log($"mgr exist : {t}");
                return (T) Instance.mgrDict[t.Name];
            }

            if (DebugMgr)
                Debug.Log($"add mgr : {t}");
            var go = new GameObject();
            go.transform.SetParent(Instance.gameObject.transform);
            var obj = go.AddComponent<T>();
            //在Awake里添加了
            if (obj.needAysncInit) Instance.initDict.Add(obj, obj.isAsyncInited);
            Instance.mgrDict.Add(t.Name, obj);
            Instance.mgrNameList.Add(t.Name);

            go.name = $"{Instance.mgrNameList.Count}:{t.Name}";
            return obj;
        }

        public static T AddMgr<T>(T tt) where T : MonoBehaviour, INeeSingleton, INeeAsync
        {
            Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
            Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();

            var t = typeof(T);
            if (Instance.mgrDict.ContainsKey(t.Name))
            {
                // if (DebugMgr)
                Debug.LogWarning($"mgr exist : {t}");
                return (T) Instance.mgrDict[t.Name];
            }

            if (DebugMgr)
                Debug.Log($"add mgr : {t}");
            var obj = tt;
            //在Awake里添加了
            if (obj.needAysncInit) Instance.initDict.Add(obj, obj.isAsyncInited);
            Instance.mgrDict.Add(t.Name, obj);
            Instance.mgrNameList.Add(t.Name);

            tt.name = $"{Instance.mgrNameList.Count}:{t.Name}";
            return obj;
        }
        
        public static T AddMgrByName<T>(T tt,string name) where T : MonoBehaviour, INeeSingleton, INeeAsync
        {
            Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
            Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();

            if (Instance.mgrDict.ContainsKey(name))
            {
                // if (DebugMgr)
                Debug.LogWarning($"mgr exist : {name}");
                return (T) Instance.mgrDict[name];
            }

            if (DebugMgr)
                Debug.Log($"add mgr : {name}");
            var obj = tt;
            //在Awake里添加了
            if (obj.needAysncInit) Instance.initDict.Add(obj, obj.isAsyncInited);
            Instance.mgrDict.Add(name, obj);
            Instance.mgrNameList.Add(name);

            tt.name = $"{Instance.mgrNameList.Count}:{name}";
            return obj;
        }

        public static bool RemoveMgr<T>() where T : MonoBehaviour, INeeSingleton, INeeAsync
        {
            Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
            Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();

            var t = typeof(T);
            if (Instance.mgrDict.ContainsKey(t.Name))
            {
                var temp = Instance.mgrDict[t.Name];
                Instance.mgrDict.Remove(t.Name);
                Instance.mgrNameList.Remove(t.Name);
                Destroy((temp as MonoBehaviour).gameObject);
                return true;
            }

            return false;
        }

        // public static T AddMgr<T>(bool isUpdate = false) where T : NeeManager, new()
        // {
        //     // if (applicationIsQuitting) return default;
        //     Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
        //     Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();
        //
        //     var t = typeof(T);
        //     if (Instance.mgrDict.ContainsKey(t.Name))
        //     {
        //         if (DebugMgr)
        //             Debug.Log($"mgr exist : {t}");
        //         return (T) Instance.mgrDict[t.Name];
        //     }
        //     else
        //     {
        //         if (DebugMgr)
        //             Debug.Log($"add mgr : {t}");
        //         var obj = new T();
        //         //obj.IsUpdate = isUpdate;
        //         //obj.Enabled = true;
        //         if (obj.needAysncInit) Instance.initDict.Add(obj, obj.isAsyncInited);
        //         Instance.mgrDict.Add(t.Name, obj);
        //         Instance.mgrNameList.Add(t.Name);
        //         return (T) obj;
        //     }
        // }

        // public static T AddMgr<T>(T obj) where T : Component, INeeSingleton, INeeAsync
        // {
        //     // if (applicationIsQuitting) return default;
        //     Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
        //     Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();
        //
        //     var t = typeof(T);
        //     if (Instance.mgrDict.ContainsKey(t.Name))
        //     {
        //         if (DebugMgr)
        //             Debug.Log($"mgr exist : {t}");
        //         return (T) Instance.mgrDict[t.Name];
        //     }
        //     else
        //     {
        //         if (DebugMgr)
        //             Debug.Log($"add mgr : {t}");
        //
        //         if (obj.needAysncInit) Instance.initDict.Add(obj, obj.isAsyncInited);
        //         Instance.mgrDict.Add(t.Name, obj);
        //         Instance.mgrNameList.Add(t.Name);
        //         return obj;
        //     }
        // }

        public static void AddMgr(Type t, INeeSingleton obj)
        {
            // if (applicationIsQuitting) return;
            Instance.mgrDict  = Instance.mgrDict  ?? new UDictionary<string, INeeSingleton>();
            Instance.initDict = Instance.initDict ?? new UDictionary<INeeSingleton, bool>();

            if (Instance.mgrDict.ContainsKey(t.Name))
            {
                if (DebugMgr)
                    Debug.Log($"mgr exist : {t}");
            }
            else
            {
                if (DebugMgr)
                    Debug.Log($"add mgr : {t}");

                if (obj.needAysncInit)
                {
                    var temp = obj as INeeAsync;
                    if (temp != null)
                        Instance.initDict.Add(obj, temp.isAsyncInited);
                }

                Instance.mgrDict.Add(t.Name, obj);
                Instance.mgrNameList.Add(t.Name);
            }
        }

        public static T GetMgr<T>() where T : Component, INeeSingleton
        {
            if (!Application.isPlaying) return FindObjectOfType<T>();

            if (Instance == null) return default;
            // if (applicationIsQuitting) return default;
            Instance.mgrDict  ??= new UDictionary<string, INeeSingleton>();
            Instance.initDict ??= new UDictionary<INeeSingleton, bool>();

            var t = typeof(T);
            if (Instance.mgrDict.TryGetValue(t.Name, out var v))
            {
                return (T) v;
            }

            var tt = NeeGame.Instance.GetComponentInChildren<T>(true);
            if (tt)
            {
                AddMgr(typeof(T), tt);
                return tt;
            }

#if UNITY_EDITOR
            if (!ApplicationIsQuitting)
                Debug.LogError($"mgr not exist : {t}");
#endif
            return default;
        }
        
        public Component GetMgr(string name)
        {
            if (!Application.isPlaying)
            {
#if UNITY_EDITOR
                Debug.LogError($"mgr not exist : {name}");
#endif
                return default;
            }

            if (Instance == null) return default;
            // if (applicationIsQuitting) return default;
            Instance.mgrDict  ??= new UDictionary<string, INeeSingleton>();
            Instance.initDict ??= new UDictionary<INeeSingleton, bool>();

            if (Instance.mgrDict.TryGetValue(name, out var v))
            {
                return v as Component;
            }
#if UNITY_EDITOR
            Debug.LogError($"mgr not exist : {name}");
#endif
            return default;
        }

        public static Component GetMgrByName(string name)
        {
            if (!Application.isPlaying)
            {
#if UNITY_EDITOR
                Debug.LogError($"mgr not exist : {name}");
#endif
                return default;
            }

            if (Instance == null) return default;
            // if (applicationIsQuitting) return default;
            Instance.mgrDict  ??= new UDictionary<string, INeeSingleton>();
            Instance.initDict ??= new UDictionary<INeeSingleton, bool>();

            if (Instance.mgrDict.TryGetValue(name, out var v))
            {
                return v as Component;
            }
#if UNITY_EDITOR
            Debug.LogError($"mgr not exist : {name}");
#endif
            return default;
        }
    }
}