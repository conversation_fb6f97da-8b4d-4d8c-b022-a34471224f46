using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using CasualGame.lib_ChuagnYi.NeeG;
using Sirenix.OdinInspector;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;

#endif

namespace CasualGame.lib_ChuagnYi
{
    public class NeeGamePath : ScriptableObject
    {
        /// <summary>
        /// 需要手动替换Sample，与项目文件夹名，命名空间一致
        /// </summary>
        // public static string GameName = "lib_ChuagnYi";

        /// <summary>
        /// 需要手动替换Sample，与项目文件夹名，命名空间一致
        /// </summary>
        // public static string EOPOutlineFill = "Hidden/CasualGame/lib_ChuagnYi/EPO/Fill/";
        private static NeeGamePath _Instance;

        public static string GameNameStatic;

        [ShowInInspector, GUIColor(0.75f, 0.5f, 0.9f)]
        public static NeeGamePath instance;

        [ShowInInspector, GUIColor(0.75f, 0.5f, 0.9f)]
        public static NeeGamePath Instance
        {
            get
            {
                if (Application.isPlaying && instance != null)
                {
                    return instance;
                }

                if (!NeeGame.Instance)
                {
                    Debug.Log("NeeGame.Instance null, 打包运行时可能会有问题");
                }

#if UNITY_EDITOR
                if (NeeGame.Instance)
                {
                    GameNameStatic = NeeGame.Instance.gameName;
                }
                if (GameNameStatic != null)
                {
                    if (GameNameStatic == "cysoldierssortie")
                    {
                        _Instance = AssetDatabase.LoadAssetAtPath<NeeGamePath>(
                         $"Assets\\{GameNameStatic}\\Data\\{GameNameStatic}.asset");
                        return _Instance;
                    }
                }

                _Instance = AssetDatabase.LoadAssetAtPath<NeeGamePath>(
                        $"Assets\\CasualGame\\{GameNameStatic}\\Data\\{GameNameStatic}.asset");
#else
                _Instance = NeeGame.Instance.NeeGamePath;
#endif
                // if (!_Instance)
                // {
                //     Debug.LogError($"null Assets\\CasualGame\\{GameName}");
                // }

                return _Instance;
            }
            set { _Instance = value; }
        }

        public const                           string Art    = "Art";
        public const                           string Data   = "Data";
        public const                           string Lua    = "Lua";
        public const                           string Mat    = "Material";
        public const                           string Other  = "Other";
        public const                           string Prefab = "Prefab";
        public const                           string Script = "Script";
        public const                           string Shader = "Shader";
        public                                 string gameName;
        [ReadOnly] [FolderPath]         public string gamePath;
        [Space] [ReadOnly] [FolderPath] public string artPath;
        [ReadOnly] [FolderPath]         public string dataPath;
        [ReadOnly] [FolderPath]         public string luaPath;
        [ReadOnly] [FolderPath]         public string matPath;
        [ReadOnly] [FolderPath]         public string otherPath;
        [ReadOnly] [FolderPath]         public string prefabPath;
        [ReadOnly] [FolderPath]         public string scriptPath;
        [ReadOnly] [FolderPath]         public string shaderPath;

        [Space] [ReadOnly] [Sirenix.OdinInspector.FilePath]
        public string PoolReferSo = "Assets/CasualGame/Sample/Data/PoolReferSo.asset";

        public string savePath          = "Sample_save";
        public string FolderPath        = "Assets/CasualGame/Sample";
        public string AssetBundlePrefix = "casualgame/Sample/";

        public string TrueShadowHidden = "CasualGame/Sample";
        public string TrueShadowUI     = "CasualGame/Sample";

        [Space] public List<NeePlugin> plugins;

#if UNITY_EDITOR
        [Button]
        public void OnValidate2()
        {
            var path    = AssetDatabase.GetAssetPath(this);
            var dirPath = Path.GetDirectoryName(path);
            var match   = Regex.Matches(dirPath, @"([A-Z])\w+");
            // gameName   = match[2].ToString();
            gamePath   = $"Assets\\CasualGame\\{gameName}";
            artPath    = $"{gamePath}\\{Art}";
            dataPath   = $"{gamePath}\\{Data}";
            luaPath    = $"{gamePath}\\{Lua}";
            matPath    = $"{gamePath}\\{Mat}";
            otherPath  = $"{gamePath}\\{Other}";
            prefabPath = $"{gamePath}\\{Prefab}";
            scriptPath = $"{gamePath}\\{Script}";
            shaderPath = $"{gamePath}\\{Shader}";

            savePath          = $"{gameName}_save_";
            PoolReferSo       = $"Assets/CasualGame/{gameName}/Data/PoolReferSo.asset";
            FolderPath        = gamePath;
            AssetBundlePrefix = $"casualgame/{gameName}/".ToLower();
            // EOPOutlineFill    = $"Hidden/CasualGame/{gameName}/EPO/Fill/";
            TrueShadowHidden = $"CasualGame/{gameName}";
            TrueShadowUI     = $"CasualGame/{gameName}";

            // var scriptFolderCurTemp = $"{dirPath}\\Script";
            // if (Directory.Exists(scriptFolderCurTemp))
            // {
            //     scriptFolderCur = scriptFolderCurTemp;
            //     scriptFolderTar = 
            // }
            plugins = new List<NeePlugin>();
            var p      = $"{otherPath}\\Plugins";
            var assets = AssetDatabase.FindAssets("t:NeePlugin", new[] {p});
            foreach (var guid in assets)
            {
                var asset = AssetDatabase.LoadAssetAtPath<NeePlugin>(AssetDatabase.GUIDToAssetPath(guid));
                plugins.Add(asset);
            }
        }

        [Button("给Lua改名")]
        public void LuaNameChange()
        {
            NeeFileContentChangeHelper.LuaNameChange(luaPath);
            Debug.Log("改名完成，请用vscode等ide替换lua内容");
        }

        [Button("给Shader改名")]
        public void ShaderNameChange()
        {
            NeeFileContentChangeHelper.ShaderNameChange(shaderPath);
            Debug.Log("改名完成，请用vscode等ide替换lua内容");
        }

        [Button("给shader改路径")]
        public void ShaderPathChange()
        {
            NeeFileContentChangeHelper.ShaderPathChange(shaderPath);
        }

        [Button("！！刷guid，请做好备份！！")]
        public void GuidRegen()
        {
            NeeGuidRegeneratorMenu.RegenerateGuids(gamePath);
        }
#endif
    }
}