using UnityEngine;

namespace CasualGame.lib_ChuagnYi
{
    /// <summary>
    ///     用于方便的定义代码顺序
    /// </summary>
    public class ScrptsOrderAttribute : DefaultExecutionOrder
    {
        public ScrptsOrderAttribute(EScriptsOrder order, int plus = 0) : base((int) order + plus)
        {
        }
    }

    /// <summary>
    ///     用于方便的定义代码顺序
    /// </summary>
    public enum EScriptsOrder
    {
        TestScene     = -9999,
        GizmoHelper   = -100,
        Init          = -50,
        MonoSingleton = -40,
        Default       = 0
    }
    //#endif
}