using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using UnityEngine;
using Random = UnityEngine.Random;

namespace CasualGame.lib_ChuagnYi
{
    public class RandomHelper
    {
        /// <summary>
        ///     权重随机
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="items"></param>
        /// <param name="weightSelector"></param>
        /// <returns></returns>
        public static T WeightRandom<T>(T[] items, Func<T, int> weightSelector, int add = 0)
        {
            var tempList                 = new List<T>();
            var sum                      = 0;
            foreach (var i in items) sum += Mathf.Max(weightSelector.Invoke(i) + add, 0);

            var rnd     = Random.Range(0, sum);
            var sumTemp = 0;

            for (var i = 0; i < items.Length; i++)
            {
                sumTemp += Mathf.Max(weightSelector.Invoke(items[i]) + add, 0);
                if (rnd < sumTemp) return items[i];
            }

            return default;
        }

        public static T WeightRandom<T>(IEnumerable<T> items, Func<T, int> weightSelector, int add = 0)
        {
            var tempList                 = new List<T>();
            var sum                      = 0;
            foreach (var i in items) sum += Mathf.Max(weightSelector.Invoke(i) + add, 0);

            var rnd     = Random.Range(0, sum);
            var sumTemp = 0;

            foreach (var i in items)
            {
                sumTemp += Mathf.Max(weightSelector.Invoke(i) + add, 0);
                if (rnd < sumTemp) return i;
            }
            //for (var i = 0; i < items.Count(); i++)
            //{
            //    sumTemp += Mathf.Max(weightSelector.Invoke(items[i]) + add, 0);
            //    if (rnd < sumTemp)
            //    {
            //        return items[i];
            //    }
            //}

            return default;
        }

        public static List<T> GetRandomList<T>(List<T> list, int count) where T : IRandomObject
        {
            if (list == null || list.Count <= count || count <= 0) return list;

            //计算权重总和
            var totalWeights                                  = 0;
            for (var i = 0; i < list.Count; i++) totalWeights += list[i].Weight + 1; //权重+1，防止为0情况。

            //随机赋值权重
            var ran   = new System.Random(GetRandomSeed()); //GetRandomSeed()随机种子，防止快速频繁调用导致随机一样的问题 
            var wlist = new List<KeyValuePair<int, int>>(); //第一个int为list下标索引、第一个int为权重排序值
            for (var i = 0; i < list.Count; i++)
            {
                var w = list[i].Weight + 1 + ran.Next(0, totalWeights); // （权重+1） + 从0到（总权重-1）的随机数
                wlist.Add(new KeyValuePair<int, int>(i, w));
            }

            //排序
            wlist.Sort(
                    delegate(KeyValuePair<int, int> kvp1, KeyValuePair<int, int> kvp2)
                    {
                        return kvp2.Value - kvp1.Value;
                    });

            //根据实际情况取排在最前面的几个
            var newList = new List<T>();
            for (var i = 0; i < count; i++)
            {
                var entiy = list[wlist[i].Key];
                newList.Add(entiy);
            }

            //随机法则
            return newList;
        }

        public static List<T> GetRandomList<T>(List<T> list, int count, Func<T, int> weightFunc)
        {
            if (list == null || count <= 0) return null;
            if (list.Count <= count) return list;

            //计算权重总和
            var totalWeights                                  = 0;
            for (var i = 0; i < list.Count; i++) totalWeights += weightFunc.Invoke(list[i]) + 1; //权重+1，防止为0情况。

            //随机赋值权重
            var ran   = new System.Random(GetRandomSeed()); //GetRandomSeed()随机种子，防止快速频繁调用导致随机一样的问题 
            var wlist = new List<KeyValuePair<int, int>>(); //第一个int为list下标索引、第一个int为权重排序值
            for (var i = 0; i < list.Count; i++)
            {
                var w = weightFunc.Invoke(list[i]) + 1 + ran.Next(0, totalWeights); // （权重+1） + 从0到（总权重-1）的随机数
                wlist.Add(new KeyValuePair<int, int>(i, w));
            }

            //排序
            wlist.Sort(
                    delegate(KeyValuePair<int, int> kvp1, KeyValuePair<int, int> kvp2)
                    {
                        return kvp2.Value - kvp1.Value;
                    });

            //根据实际情况取排在最前面的几个
            var newList = new List<T>();
            for (var i = 0; i < count; i++)
            {
                var entiy = list[wlist[i].Key];
                newList.Add(entiy);
            }

            //随机法则
            return newList;
        }

        private static int GetRandomSeed()
        {
            var bytes = new byte[4];
            var rng =
                    new RNGCryptoServiceProvider();
            rng.GetBytes(bytes);
            return BitConverter.ToInt32(bytes, 0);
        }

        public interface IRandomObject
        {
            int Weight { get; set; }
        }
    }
}