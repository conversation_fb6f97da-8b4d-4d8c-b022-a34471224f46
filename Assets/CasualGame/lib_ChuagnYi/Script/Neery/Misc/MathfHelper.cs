using UnityEngine;

namespace CasualGame.lib_ChuagnYi
{
    public static class MathfHelper
    {
        public static string KMFormart2Vriant(float value)
        {
            string str;
            if (value > 1000000)
                str = Mathf.Floor(value / 1000) / 100 + " M";
            else if (value > 1000)
                str = Mathf.Floor(value / 10) / 100 + " K";
            else
                str = value.ToString();
            str = str.Replace('.', ',');
            return str;
        }

        public static string KMFormart2(float value)
        {
            string str;
            if (value > 1000000)
                str = Mathf.Floor(value / 1000) / 100 + "M";
            else if (value > 1000)
                str = Mathf.Floor(value / 10) / 100 + "K";
            else
                str = value.ToString();
            str = str.Replace('.', ',');
            return str;
        }

        public static string KMFormart1(float value)
        {
            string str;
            if (value > 1000000)
                str = Mathf.Floor(value / 10000) / 10 + "M";
            else if (value > 1000)
                str = Mathf.Floor(value / 100) / 10 + "K";
            else
                str = value.ToString();
            str = str.Replace('.', ',');
            return str;
        }

        public static string KMFormart0(float value)
        {
            string str;
            if (value > 1000000)
                str = Mathf.Floor(value / 100000) + "M";
            else if (value > 1000)
                str = Mathf.Floor(value / 1000) + "K";
            else
                str = value.ToString();
            str = str.Replace('.', ',');
            return str;
        }

        public static int NumFormat(float value, float mult, int Section)
        {
            var v = Mathf.FloorToInt(value * mult / Section) * Section;
            return v;
        }

        public static float Root(float d, float intensity)
        {
            if (d < 0.0f)
                return -Mathf.Pow(-d, 1f / intensity);

            return Mathf.Pow(d, 1f / intensity);
        }

        //public static float RootX(float f, int degree)
        //{
        //    if (degree < 1) return float.NaN;
        //    if (f < 0)
        //    {
        //        if (degree % 2 != 1) return float.NaN;
        //        return -UnityEngine.Mathf.Pow(Mathf.Abs(f), 1f / degree);
        //    }
        //    else
        //        return UnityEngine.Mathf.Pow(f, 1f / degree);
        //}
    }
}