using System;
using Sirenix.OdinInspector;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public partial class NeeGame
    {
        public enum NeePlayModeStateChange
        {
            EnteredEditMode = 0,
            ExitingEditMode = 1,
            EnteredPlayMode = 2,
            ExitingPlayMode = 3
        }

        public static event Action<NeePlayModeStateChange> onPlayModeStateChange;

// #if UNITY_EDITOR
//         private void OnEnable()
//         {
//             EditorApplication.playModeStateChanged += OnPlayModeChanged;
//         }
//
//         private void OnDisable()
//         {
//             EditorApplication.playModeStateChanged -= OnPlayModeChanged;
//         }
//
//         private void OnPlayModeChanged(PlayModeStateChange obj)
//         {
//             onPlayModeStateChange?.Invoke((NeePlayModeStateChange) obj);
//         }
// #endif
        
        
        //     public static void DestroyGameobject(UnityEngine.Component component) {
        // #if Use_AA
        //         if (Game.GetMgr<LoaderMgr>().ReleaseInstance(component.gameObject) == false) {
        //             UnityEngine.Object.Destroy(component.gameObject);
        //         }
        // #else
        //         GameObject.Destroy(component.gameObject);
        // #endif
        // }

        // public static void Close() {
        //     Instance.mgrDict.Clear();
        //     Instance.mgrNameList.Clear();
        //     _instance = null;
        // }

        // InputMgr _inputMgr;
        // public static InputMgr inputMgr {
        //     get {
        //         if (!Instance) return null;
        //         if (Instance._inputMgr == null) {
        //             Instance._inputMgr = GetMgr<InputMgr>();
        //         }
        //         return Instance._inputMgr;
        //     }
        // }
        //
        // NeeSelectMgr _selMgr;
        // public static NeeSelectMgr selMgr {
        //     get {
        //         if (!Instance) return null;
        //         if (Instance._selMgr == null) {
        //             Instance._selMgr = GetMgr<NeeSelectMgr>();
        //         }
        //         return Instance._selMgr;
        //     }
        // }
        //
        // NeeSelectLogic _selLogic;
        // public static NeeSelectLogic selLogic {
        //     get {
        //         if (!Instance) return null;
        //         if (Instance._selLogic == null) {
        //             Instance._selLogic = GetMgr<NeeSelectLogic>();
        //         }
        //         return Instance._selLogic;
        //     }
        // }
    }
}