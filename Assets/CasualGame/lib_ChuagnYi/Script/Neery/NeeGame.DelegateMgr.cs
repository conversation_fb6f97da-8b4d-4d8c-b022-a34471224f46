using System;
using UnityEngine;
using Object = UnityEngine.Object;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public partial class NeeGame
    {
        private DelegateMgr delegateMgr;
        private bool        triedGetDelegateMgr;

        private bool TryGetDelegateMgr()
        {
            if (triedGetDelegateMgr)
            {
                Debug.Log<PERSON>arning("no delegate Mgr ,Tried");
                return false;
            }
            else
            {
                triedGetDelegateMgr       = true;
                delegateMgr = GetMgr<DelegateMgr>();
                if (!delegateMgr)
                {
                    Debug.LogWarning("no delegate Mgr ,Tried");
                    return false;
                }

                return true;
            }
        }

        public static void UnSubAll(IEventSuber suber)
        {
            if (Instance.TryGetDelegateMgr())
                Instance.delegateMgr.UnSubAll(suber);
        }

        public static void UnSubAction(IEventSuber suber, Action action, string key, Object targerId = null)
        {
            if (Instance.TryGetDelegateMgr())
                Instance.delegateMgr.UnSubAction(suber, action, key, targerId);
        }

        public static void FireAction(string key, Object targerId = null)
        {
            if (Instance.TryGetDelegateMgr())
                Instance.delegateMgr.FireAction(key, targerId);
        }

        public static void SubAcion(IEventSuber suber, Action action, string key, Object targerId = null)
        {
            if (Instance.TryGetDelegateMgr())
                Instance.delegateMgr.SubAcion(suber, action, key, targerId);
        }
    }
}