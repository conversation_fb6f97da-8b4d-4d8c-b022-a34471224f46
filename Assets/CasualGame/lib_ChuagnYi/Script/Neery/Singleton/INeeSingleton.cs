using System;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public interface INeeSingleton
    {
        bool needAysncInit { get; }
        // bool isAsyncInited { get; set; }
        // void SetAysncInitBool(bool bo);
        // void RegisterOnAsyncInited(System.Action);
    }

    public interface INeeAsync
    {
        bool isAsyncInited { get; set; }
        void SetAysncInitBool(bool        bo);
        void RegisterOnAsyncInited(Action a);
    }
}