// **********************************************************************
//  文件信息
// 文件名(File Name):                SyncText.cs
// 作者(Author):                     huangxujie
// 创建时间(CreateTime):             2022-09-09 06:03
// 脚本描述(Module description):
// **********************************************************************

using System;
using System.Collections;
using System.Collections.Generic;
using CasualGame.lib_ChuagnYi.NeeG;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace CasualGame.lib_ChuagnYi
{
    public class NeeLocalizer : MonoBehaviour
    {
#if UNITY_EDITOR
        [ShowInInspector]
        public string preview
        {
            get { return NeeGame.GetMgr<NeeLocalizeMgr>().GetTranslateEditor(key); }
        }
#endif
        public string key;
        public Text   text;


        private void Start()
        {
            text ??= GetComponent<Text>();
            NeeGame.RegisterOnAsyncInited(() =>
            {
                NeeGame.GetMgr<NeeLocalizeMgr>().onLanguageCsvLoaded += SetTextFromKey;
                NeeGame.GetMgr<NeeLocalizeMgr>().RegisterOnAsyncInited(SetTextFromKey);
            });
        }

        private void OnValidate()
        {
            SyncText();
        }

        [Button]
        public void SyncText()
        {
            text ??= GetComponent<Text>();
            if (text)
            {
                key = text.text;
            }
        }

        /// <summary>
        /// 动态文本
        /// </summary>
        /// <param name="str"></param>
        public void SetTextFromCS(string str)
        {
            text.text = str;
        }

        /// <summary>
        /// 静态文本
        /// </summary>
        public void SetTextFromKey()
        {
            if (!string.IsNullOrEmpty(key))
            {
                text.text = NeeGame.GetMgr<NeeLocalizeMgr>().GetTranslate(key);
            }
        }
    }
}