using System;
using System.Collections;
using CasualGame.lib_ChuagnYi.NeeG;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi
{
    [ScrptsOrder(EScriptsOrder.Init)]
    public class NeeGameEntry : NeeMono
    {
        private void Awake()
        {
            NeeGame.Instance = null;
            GetComponent<NeeGame>().SetAsInstance();
        }

        private IEnumerator Start()
        {
#if UNITY_STANDALONE
            Screen.SetResolution(192 * 7, 108 * 7, false);
#endif
            GetComponent<NeeGame>().SetAsInstance();
            NeeGame.Instance._applicationIsQuitting = false;

            Debug.Log("Add mgr start!");
            // NeeGame.AddMgr<DelegateMgr>();

            var arr = NeeGame.Instance.GetComponentsInChildren<NeeMonoSingleton>();
            foreach (var i in arr)
            {
                if (i.GetType() == typeof(NeeGame)) continue;
                // if (i.GetType() == typeof(ReferMgr)) continue;
                NeeGame.AddMgr(i.GetType(), i as INeeSingleton);
            }

            Debug.Log("wait all mgr inited!");

            while (NeeGame.IsAllAysncInited() == false)
            {
                Debug.Log($"NeeGame.IsAllAysncInited() : {NeeGame.IsAllAysncInited()}");
                yield return null;
            }

            yield return null;
            Debug.Log("Add mgr done!");
            NeeGame.SetInited();
            //
        }
    }
}