using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;

#endif

namespace CasualGame.lib_ChuagnYi.NeeG
{
    [Serializable]
    public class NeeReferCollectionData
    {
        public string key;
        public Object gameObject;
    }

    public class NeeReferCollectionDataComparer : IComparer<NeeReferCollectionData>
    {
        public int Compare(NeeReferCollectionData x, NeeReferCollectionData y)
        {
            return string.Compare(x.key, y.key, StringComparison.Ordinal);
        }
    }

    /// <summary>
    /// 配合Peek插件（Ludiq.Peek），可以很方便的拖动想要的Object到面板上
    /// </summary>
    public class NeeReferCollection : MonoBehaviour, ISerializationCallbackReceiver
    {
        public List<NeeReferCollectionData> data = new List<NeeReferCollectionData>();

        private readonly Dictionary<string, Object> dict = new Dictionary<string, Object>();

        public void Bind(LuaTable luaInstance)
        {
            //注入其他类型
            foreach (var kv in dict)
            {
                luaInstance.Set(kv.Key, kv.Value);
            }
        }

#if UNITY_EDITOR
        public void Add(string key, Object obj)
        {
            SerializedObject   serializedObject = new SerializedObject(this);
            SerializedProperty dataProperty     = serializedObject.FindProperty("data");
            int                i;
            for (i = 0; i < data.Count; i++)
            {
                if (data[i].key == key)
                {
                    break;
                }
            }

            if (i != data.Count)
            {
                SerializedProperty element = dataProperty.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("gameObject").objectReferenceValue = obj;
            }
            else
            {
                dataProperty.InsertArrayElementAtIndex(i);
                SerializedProperty element = dataProperty.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("key").stringValue                 = key;
                element.FindPropertyRelative("gameObject").objectReferenceValue = obj;
            }

            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        public void Remove(string key)
        {
            SerializedObject   serializedObject = new SerializedObject(this);
            SerializedProperty dataProperty     = serializedObject.FindProperty("data");
            int                i;
            for (i = 0; i < data.Count; i++)
            {
                if (data[i].key == key)
                {
                    break;
                }
            }

            if (i != data.Count)
            {
                dataProperty.DeleteArrayElementAtIndex(i);
            }

            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        public void Clear()
        {
            SerializedObject serializedObject = new SerializedObject(this);
            var              dataProperty     = serializedObject.FindProperty("data");
            dataProperty.ClearArray();
            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        public void Sort()
        {
            SerializedObject serializedObject = new SerializedObject(this);
            data.Sort(new NeeReferCollectionDataComparer());
            EditorUtility.SetDirty(this);
            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }
#endif

        public T Get<T>(string key) where T : class
        {
            Object dictGo;
            if (!dict.TryGetValue(key, out dictGo))
            {
                return null;
            }

            return dictGo as T;
        }

        public Object Get(string key)
        {
            Object dictGo;
            if (!dict.TryGetValue(key, out dictGo))
            {
                return null;
            }

            return dictGo;
        }

        public void OnBeforeSerialize()
        {
        }

        public void OnAfterDeserialize()
        {
            dict.Clear();
            foreach (NeeReferCollectionData referenceCollectorData in data)
            {
                if (!dict.ContainsKey(referenceCollectorData.key))
                {
                    dict.Add(referenceCollectorData.key, referenceCollectorData.gameObject);
                }
            }
        }
    }
}