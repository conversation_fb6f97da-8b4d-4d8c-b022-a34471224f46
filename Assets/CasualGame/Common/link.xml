<linker>
       <assembly fullname="Unity.2D.SpriteShape.Runtime">
	    <type fullname="UnityEngine.U2D.Spline" preserve="all"/>
        <type fullname="UnityEngine.U2D.SpriteShapeController" preserve="all"/>
	   </assembly>
    <assembly fullname="Unity.Animation.Rigging"  preserve="all"/>
    <assembly fullname="Obi">
        <type fullname="Oni" preserve="all"/>
    </assembly>
    <assembly fullname="UnityEngine">
      <type fullname="UnityEngine.TerrainLayer" preserve="all"/>
      <type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="all"/>
      <type fullname="UnityEngine.Light" preserve="all"/>
      <type fullname="UnityEngine.FixedJoint2D" preserve="all"/>
      <type fullname="UnityEngine.EdgeCollider2D" preserve="all"/>
      <type fullname="UnityEngine.TextGenerator" preserve="all"/>
    </assembly>
        <assembly fullname="SonicBloom.Koreo">
        <type fullname="SonicBloom.Koreo.Koreographer" preserve="all"/>
        <type fullname="SonicBloom.Koreo.KoreographyTrack" preserve="all"/>
        <type fullname="SonicBloom.Koreo.KoreographyEvent" preserve="all"/>
        <type fullname="SonicBloom.Koreo.IPayload" preserve="all"/>
    </assembly>
    <assembly fullname="Assembly-CSharp">
        <type fullname="bc.MiniGameBase.GameHelp" preserve="all"/>
    </assembly>
    <assembly fullname="UnityEngine.CoreModule">
        <type fullname="UnityEngine.ComputeShader" preserve="all"/>
    </assembly>
</linker>