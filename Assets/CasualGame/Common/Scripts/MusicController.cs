using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using XLua;
using System;
using UnityEngine.Events;
using UnityEngine.Audio;

namespace bc.MiniGameBase
{
    [System.Serializable]
    public class SoundItem
    {
        public string Name;
        public AudioClip audioClip;
    }
    public class MusicController : MonoBehaviour
    {
        public static MusicController Instance;

        public bool AutoPlayFirstOne = true;

        public List<SoundItem> BGM;
        public List<SoundItem> UI;
        public List<SoundItem> FX;

        [HideInInspector]
        public AudioSource BGM_AS;//背景音乐
        [HideInInspector]
        public AudioSource UI_AS;//UI音效
        [HideInInspector]
        public List<AudioSource> FX_AS;//游戏音效

        private AudioMixerGroup bgm_AM;
        private AudioMixerGroup ui_AM;
        private AudioMixerGroup fx_AM;

        void Awake()
        {
            Instance = this;
        }
        void Start()
        {

            gameObject.AddComponent<AudioListener>();

            BGM_AS = gameObject.AddComponent<AudioSource>();
            BGM_AS.playOnAwake = false;
            UI_AS = gameObject.AddComponent<AudioSource>();
            FX_AS = new List<AudioSource>();

            for (int i = 0; i < FX.Count; i++)
            {
                AudioSource audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.clip = FX[i].audioClip;
                FX_AS.Add(audioSource);
            }

            InitVolume();

            if (AutoPlayFirstOne && BGM.Count > 0)
            {
                BGM_AS.clip = BGM.First().audioClip;
                BGM_AS.loop = true;
                BGM_AS.playOnAwake = true;
                BGM_AS.Play();
            }
        }

        private void InitVolume() 
        {
#if !BC_TEST
            AudioListener audioListener = War.Script.MusicController.AS.gameObject.GetComponent<AudioListener>();
            audioListener.enabled = false;

            bgm_AM = War.Script.SoundEngine.Instance.mixer.FindMatchingGroups("Master/BGM")[0];
            ui_AM = War.Script.SoundEngine.Instance.mixer.FindMatchingGroups("Master/UI")[0];
            fx_AM = War.Script.SoundEngine.Instance.mixer.FindMatchingGroups("Master/SFX")[0];

            BGM_AS.outputAudioMixerGroup = bgm_AM;
            UI_AS.outputAudioMixerGroup = ui_AM;
            for (int i = 0; i < FX_AS.Count; i++)
            {
                FX_AS[i].outputAudioMixerGroup = fx_AM;
                FX_AS[i].playOnAwake = false;
            }
            War.Script.MusicController.AS.mute = true;
#endif

        }

#if !BC_TEST
        public AudioMixerGroup GetBGMAudioMixer() 
        {
            return bgm_AM;
        }          

        public AudioMixerGroup GetUIAudioMixer()
        {
            return ui_AM;
        }          

        public AudioMixerGroup GetFXAudioMixer()
        {
            return fx_AM;
        }          
#endif              

        public void SetBackgroundMusic(string key, bool auto = true,bool loop = true) 
        {
            int index = 0;
            if (!Contains(BGM,key,ref index))
            {
                Debug.LogError($"bgm -- {key} == null");
                return;
            }

            BGM_AS.clip = BGM[index].audioClip;
            if (auto)
                BGM_AS.Play();
            BGM_AS.loop = loop;
        }


        public void PlayUI(string key) 
        {
            int index = 0;
            if (!Contains(FX, key,ref index))
            {
                Debug.LogError($"FX -- {key} == null");
                return;
            }
            UI_AS.PlayOneShot(FX[index].audioClip);
        }
        public void PlayFX(string key)
        {
            SoundItem audio = FX.Find(delegate (SoundItem s) { return s.Name == key; });
            FX_AS.First((s) => s.clip == audio.audioClip).Play();
        }
        //弃用
        public void PlayFX(string key, Vector3 vector3 = default, Func<float,object> callback = null)
        {
            SoundItem audio = FX.Find(delegate (SoundItem s) { return s.Name == key; });
            AudioSource.PlayClipAtPoint(audio.audioClip, vector3);
            if (callback != null)
            {
                callback.Invoke(audio.audioClip.length);
            }
        }
        //弃用
        public void PlayFX(string key, Vector3 vector3 = default , LuaFunction callback = null) 
        {
            SoundItem audio = FX.Find(delegate (SoundItem s) { return s.Name == key; });
            AudioSource.PlayClipAtPoint( audio.audioClip, vector3);
            if (callback!=null)
            {
                callback.Action(audio.audioClip.length);
            }
        }

        private bool Contains(List<SoundItem> soundItems,string key,ref int index) 
        {
            bool had = false;
            for (int i = 0; i < soundItems.Count; i++)
            {
                if (soundItems[i].Name == key && soundItems[i].audioClip != null)
                {
                    index = i;
                    had = true;
                    break;
                }
            }

            return had;
        }

        private void OnDestroy()
        {
#if !BC_TEST
                War.Script.MusicController.AS.mute = false;
                AudioListener audioListener = War.Script.MusicController.AS.gameObject.GetComponent<AudioListener>();
                audioListener.enabled = true;

#endif
            Instance = null;
        }

    }
}