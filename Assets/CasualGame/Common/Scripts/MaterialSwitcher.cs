using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace bc.MiniGameBase
{
    public class MaterialSwitcher : MonoBehaviour
    {
        public List<Renderer> renderers = new List<Renderer>();
        public List<Material> materials = new List<Material>();

        [HideInInspector]
        public Dictionary<string, Material> materialDic;

        private void Check()
        {
            if (materialDic == null)
                materialDic = materials.ToDictionary(x => x.name, y => y);
        }

        public void SwitchByIndex(int index)
        {
            Check();
            if (index < 0 || index >= materials.Count)
            {
                Debug.LogError($"����ֵ����ȷ: {index}");
                return;
            }
            SwitchAllRender(materials[index]);
        }

        public void SwitchByName(string matName)
        {
            Check();
            if (materialDic.ContainsKey(matName))
            {
                SwitchAllRender(materialDic[matName]);
            }
            else
            {
                Debug.LogError($"�Ҳ�����Ӧ���ֵ�Sprite: {matName}");
            }
        }

        private void SwitchAllRender(Material material)
        {
            foreach(var renderer in renderers)
            {
                if (!renderer) continue;

                renderer.material = material;
            }
        }
    }
}