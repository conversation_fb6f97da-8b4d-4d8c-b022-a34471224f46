using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace bc.MiniGameBase
{
    [System.Serializable]
    public class CasualGameTagObject
    {
        public Object Object;
        public string Tag;
    }

    [DefaultExecutionOrder(80)]
    public class CasualGameTag : MonoBehaviour
    {
        [HideInInspector][SerializeField]
        public List<CasualGameTagObject> mObjects;

        protected virtual void Awake()
        {
            //if(mObjects!=null&&mObjects.Count>0)
            //{
            //    foreach (var tagObj in mObjects)
            //    {
            //        var obj = tagObj.Object as GameObject;
            //        obj.tag = tagObj.Tag;
            //    }
            //}

            //Destroy(this);
        }
    }
}