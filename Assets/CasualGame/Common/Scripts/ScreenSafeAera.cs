using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using com.notch;
using System.Linq;
using bc.MiniGameBase;
using UnityEngine.UIElements;
using XLua;
using System;
using UnityEngine.SocialPlatforms;

[RequireComponent(typeof(RectTransform))]
public class ScreenSafeAera : MonoBehaviour
{
    public static ScreenSafeAera Instance;

    private RectTransform s_rt;
    public void Awake()
    {
        Instance = this;
    }

    public void Start()
    {
        s_rt = GetComponent<RectTransform>();
        CallFun("bc_screen_safearea", "Init");

        OnScreenSizeChanged("",Vector2.zero, Vector2.zero);
    }
    public void OnDestroy()
    {
        CallFun("bc_screen_safearea", "Dispose");
    }

    public void OnScreenSizeChanged(string eventName,Vector2 newSize, Vector2 oldSize)
    {
        bool isfold = Convert.ToBoolean(CallFun("screen_util", "IsFoldableScreen")[0]);
        if (isfold)
        {
            RTFSetHalfScreen();
        }
        else
        {
            RTFSetFullScreen();
        }
    }


    private void RTFSetHalfScreen()
    {
        s_rt.anchorMin = new Vector2(.5f, .5f);
        s_rt.anchorMax = new Vector2(.5f, .5f);
        s_rt.sizeDelta = new Vector2(720, 1280);
    }
    private void RTFSetFullScreen()
    {
        Vector2 prePos = s_rt.anchoredPosition;
        s_rt.anchorMin = new Vector2(0, 0);
        s_rt.anchorMax = new Vector2(1, 1);
        s_rt.offsetMin = new Vector2(0, 0);
        s_rt.offsetMax = new Vector2(0, 0);
        s_rt.anchoredPosition = prePos;
    }
    private object [] CallFun(string luaName,string luaFunc) {

        if (XLuaManager.Instance == null || XLuaManager.Instance.GetLuaEnv() == null)
            return null;
        string luastr = string.Format($"local {luaName} = require ('{luaName}') return {luaName}");
        object[] objectarr = XLuaManager.Instance.GetLuaEnv().DoString(luastr);
        if (objectarr == null)
        {
            Debug.LogWarning("objectarr is null");
            return null;
        }
        else
        {
            LuaTable _lua = objectarr[0] as LuaTable;
            LuaFunction luaFunction = _lua.Get<LuaFunction>($"{luaFunc}") ;
            return luaFunction.Call();
        }
    }
}
