using UnityEngine;
using UnityEngine.UI;
namespace bc.MiniGameBase
{
    [RequireComponent(typeof(Text))]
    public class LangToText : MonoBehaviour
    {
        public int ID;
        private Text s_text;

        void Start()
        {
            // string str = "";
            // str = War.Common.GameSchemeProxy.Lang.Get(ID);
            // if (str != "")
            // {
            //     Debug.Log($"Lang@{ID}@{str}");
            //     if (transform.TryGetComponent<Text>(out s_text))
            //     {
            //         s_text.text = str;
            //     }
            // }
        }

    }
}