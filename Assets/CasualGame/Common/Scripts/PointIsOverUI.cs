using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.EventSystems;
using UnityEngine.UI;

#if UNITY_EDITOR

using UnityEditor;
using UnityEditor.Animations;
#endif

public class PointIsOverUI
{

    public static PointIsOverUI Instance = new PointIsOverUI();

    public PointIsOverUI()
    { }

    //方法一， 使用该方法的另一个重载方法，使用时给该方法传递一个整形参数
    // 该参数即使触摸手势的 id
    // int id = Input.GetTouch(0).fingerId;
    public bool IsPointerOverUIObject(int fingerID)
    {
        return EventSystem.current.IsPointerOverGameObject(fingerID);
    }

    public List<RaycastResult> results = new List<RaycastResult>();

    //方法二 通过UI事件发射射线
    //是 2D UI 的位置，非 3D 位置
    public bool IsPointerOverUIObject(Vector2 screenPosition)
    {
        //实例化点击事件
        PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current);
        //将点击位置的屏幕坐标赋值给点击事件
        eventDataCurrentPosition.position = new Vector2(screenPosition.x, screenPosition.y);
        results.Clear();

        //向点击处发射射线
        EventSystem.current.RaycastAll(eventDataCurrentPosition, results);

        return results.Count > 0;
    }

    //方法三 通过画布上的 GraphicRaycaster 组件发射射线
    public bool IsPointerOverUIObject(Canvas canvas, Vector2 screenPosition)
    {
        //实例化点击事件
        PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current)
        {
            //将点击位置的屏幕坐标赋值给点击事件
            position = screenPosition
        };
        //获取画布上的 GraphicRaycaster 组件
        var uiRaycaster = canvas.GetComponent<BaseRaycaster>();
        results.Clear();
        // GraphicRaycaster 发射射线
        uiRaycaster.Raycast(eventDataCurrentPosition, results);

        return results.Count > 0;
    }

    public bool IsOverUI()
    {
#if UNITY_EDITOR
		    return EventSystem.current.IsPointerOverGameObject();
#endif

#if (UNITY_IPHONE || UNITY_ANDROID)
		    if (Input.touchCount >0 && Input.GetTouch(0).phase == TouchPhase.Began)
		    { 
			    var b = PointIsOverUI.Instance.IsPointerOverUIObject(Input.GetTouch(0).position);
			    Debug.LogError("UNITY_ANDROID" + b);

			    return b;
		    }  
		    var bb = EventSystem.current.IsPointerOverGameObject();
		    //Debug.LogError("!!!Default"+ bb);
		    return bb;
#endif
        //Debug.LogError("!!!Other");
        return false;
    }


    public bool IsOverUICanvas(Canvas canvas)
    {
#if UNITY_EDITOR
            return EventSystem.current.IsPointerOverGameObject();
#endif

#if (UNITY_IPHONE || UNITY_ANDROID)
		    if (Input.touchCount >0 && Input.GetTouch(0).phase == TouchPhase.Began)
		    { 
			    var b = PointIsOverUI.Instance.IsPointerOverUIObject(canvas,Input.GetTouch(0).position);
			    Debug.LogError("UNITY_ANDROID" + b);

			    return b;
		    }  
		    var bb = EventSystem.current.IsPointerOverGameObject();
		    //Debug.LogError("!!!Default"+ bb);
		    return bb;
#endif
        //Debug.LogError("!!!Other");
        return false;
    }
}


