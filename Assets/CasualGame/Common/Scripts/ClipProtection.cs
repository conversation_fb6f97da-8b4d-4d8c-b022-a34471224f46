using UnityEngine;
using UnityEngine.U2D;

namespace bc.MiniGameBase
{
    /// <summary>
    /// 防裁剪保护
    /// </summary>
    public class ClipProtection : MonoBehaviour
    {
        public ContactPoint2D m_contactPoint;
        private void OnCollisionEnter2D(Collision2D collision)
        {
            ContactPoint2D point2D = collision.GetContact(0);
            Debug.Log(point2D);
        }

        public SpriteAtlas spriteAtlas;
        public void SpriteAtlasProtection()
        {
            spriteAtlas.GetSprite("1");
            spriteAtlas.GetSprites(new Sprite[2]);
        } 
        
#region Unity
        // public EdgeCollider2D ed2d;
        // public void SetEdgeColliderRadius()
        // {
        //     ed2d.edgeRadius = 0;
        // }
#endregion
    }
}
