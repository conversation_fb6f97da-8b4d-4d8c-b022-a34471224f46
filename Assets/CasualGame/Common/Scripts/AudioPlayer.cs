using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace bc.MiniGameBase
{
    public class AudioPlayer : MonoBehaviour
    {
        public AudioSource audoiSource;
        public List<AudioClip> clipList = new List<AudioClip>();

        [HideInInspector]
        public Dictionary<string, AudioClip> clipDic;

        private void Check()
        {
            if (!audoiSource)
            {
                audoiSource = GetComponent<AudioSource>();
            }

            if (!audoiSource)
            {
                audoiSource = gameObject.AddComponent<AudioSource>();
                audoiSource.playOnAwake = false;
            }

            if (clipDic == null)
                clipDic = clipList.ToDictionary(x => x.name, y => y);
        }

        public AudioClip GetClipByIndex(int index)
        {
            Check();
            if (index < 0 || index >= clipList.Count)
            {
                Debug.LogError($"����ֵ����ȷ: {index}");
                return null;
            }
            return clipList[index];
        }

        public AudioClip GetClipByName(string clipName)
        {
            Check();
            if (clipDic.ContainsKey(clipName))
            {
                return clipDic[clipName];
            }
            else
            {
                Debug.LogError($"�Ҳ�����Ӧ���ֵ�Audio: {clipName}");
                return null;
            }
        }

        public void SwitchByIndex(int index)
        {
            audoiSource.clip = GetClipByIndex(index);
        }

        public void SwitchByName(string clipName)
        {
            audoiSource.clip = GetClipByName(clipName);
        }

        public void PlayOneShotByIndex(int index)
        {
            var clip = GetClipByIndex(index);
            if (null != clip)
                audoiSource.PlayOneShot(clip);
        }

        public void PlayOneShotByName(string clipName)
        {
            var clip = GetClipByName(clipName);
            if (null != clip)
                audoiSource.PlayOneShot(clip);
        }

        public void PlayByIndex(int index)
        {
            var clip = GetClipByIndex(index);
            if (null != clip)
            {
                audoiSource.clip = clip;
                audoiSource.Play();
            }
        }

        public void PlayByName(string clipName)
        {
            var clip = GetClipByName(clipName);
            if (null != clip)
            {
                audoiSource.clip = clip;
                audoiSource.Play();
            }
        }

        public void Stop()
        {
            audoiSource.Stop();
        }
    }
}