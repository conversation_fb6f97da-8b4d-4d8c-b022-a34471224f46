using System.Collections;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace bc.MiniGameBase
{
    public class SpriteRendererSwitcher : MonoBehaviour
    {
        private SpriteRenderer image;
        public List<Sprite> spriteList = new List<Sprite>();
        [HideInInspector]
        public Dictionary<string, Sprite> spriteDic;

        private void Check()
        {
            if(!image)
                image = GetComponent<SpriteRenderer>();

            if (spriteDic == null)
                spriteDic = spriteList.ToDictionary(x => x.name, y => y);
        }

        public Sprite GetSpriteByIndex(int index)
        {
            Check();
            if (index < 0 || index >= spriteList.Count)
            {
                Debug.LogError($"����ֵ����ȷ: {index}");
                return null;
            }
            return spriteList[index];
        }

        public Sprite GetSpriteByName(string clipName)
        {
            Check();
            if (spriteDic.ContainsKey(clipName))
            {
                return spriteDic[clipName];
            }
            else
            {
                Debug.LogError($"�Ҳ�����Ӧ���ֵ�Sprite: {clipName}");
                return null;
            }
        }

        public void SwitchByIndex(int index)
        {
            Check();
            image.sprite = GetSpriteByIndex(index);
        }

        public void SwitchByName(string spriteName)
        {
            Check();
            image.sprite = GetSpriteByName(spriteName);
        }
    }
}
