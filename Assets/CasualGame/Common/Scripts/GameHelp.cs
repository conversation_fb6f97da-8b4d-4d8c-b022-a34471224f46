using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

namespace bc.MiniGameBase
{
    public static class GameHelp
    {
        public static bool ScreenPointToWorldPointInRectangle(RectTransform rect, Vector3 screen, Camera camera, out Vector3 world)
        {
            return RectTransformUtility.ScreenPointToWorldPointInRectangle(rect, screen, camera, out world);
        }

        public static bool IsNull(UnityEngine.Object o)
        {
            return o == null;
        }
    }
}