using System.Collections;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace bc.MiniGameBase
{
    public class SpriteSwitcher : MonoBehaviour
    {
        private Image image;
        private SpriteRenderer sp;
        public List<Sprite> spriteList = new List<Sprite>();
        [HideInInspector]
        public Dictionary<string, Sprite> spriteDic;

        private void Awake()
        {
            if (!image)
                image = GetComponent<Image>();

            if (!sp)
                sp = GetComponent<SpriteRenderer>();
        }

        private void Check()
        {
            if (spriteDic == null)
                spriteDic = spriteList.ToDictionary(x => x.name, y => y);
        }

        public Sprite GetSpriteByIndex(int index)
        {
            Check();
            if (index < 0 || index >= spriteList.Count)
            {
                Debug.LogError($"����ֵ����ȷ: {index}");
                return null;
            }
            return spriteList[index];
        }

        public Sprite GetSpriteByName(string clipName)
        {
            Check();
            if (spriteDic.ContainsKey(clipName))
            {
                return spriteDic[clipName];
            }
            else
            {
                Debug.LogError($"�Ҳ�����Ӧ���ֵ�Sprite: {clipName}");
                return null;
            }
        }

        public void SwitchByIndex(int index)
        {
            Check();

            if (image)
                image.sprite = GetSpriteByIndex(index);

            if (sp)
                sp.sprite = GetSpriteByIndex(index);
        }

        public void SwitchByName(string spriteName)
        {
            Check();

            if (image)
                image.sprite = GetSpriteByName(spriteName);

            if (sp)
                sp.sprite = GetSpriteByName(spriteName);
        }
    }
}
