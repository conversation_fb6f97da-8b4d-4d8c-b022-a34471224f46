using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.IO;

namespace Utility
{
    public class Rom
    {

        public const String TAG = "Rom";

        public const String ROM_MIUI = "MIUI";
        public const String ROM_EMUI = "EMUI";
        public const String ROM_FLYME = "FLYME";
        public const String ROM_OPPO = "OPPO";
        public const String ROM_SMARTISAN = "SMARTISAN";
        public const String ROM_VIVO = "VIVO";
        public const String ROM_QIKU = "QIKU";

        public const String KEY_VERSION_MIUI = "ro.miui.ui.version.name";
        public const String KEY_VERSION_EMUI = "ro.build.version.emui";
        public const String KEY_VERSION_OPPO = "ro.build.version.opporom";
        public const String KEY_VERSION_SMARTISAN = "ro.smartisan.version";
        public const String KEY_VERSION_VIVO = "ro.vivo.os.version";

        public static String sName;
        public static String sVersion;


        public static void Log(string str)
        {
            //Debug.LogWarning(str);
        }
        public static bool isEmui()
        {
            return check(ROM_EMUI);
        }

        public static bool isMiui()
        {
            return check(ROM_MIUI);
        }

        public static bool isVivo()
        {
            return check(ROM_VIVO);
        }

        public static bool isOppo()
        {
            return check(ROM_OPPO);
        }

        public static bool isFlyme()
        {
            return check(ROM_FLYME);
        }

        public static bool is360()
        {
            return check(ROM_QIKU) || check("360");
        }

        public static bool isSmartisan()
        {
            return check(ROM_SMARTISAN);
        }

        public static String getName()
        {
            if (sName == null)
            {
                check("");
            }
            return sName;
        }

        public static String getVersion()
        {
            if (sVersion == null)
            {
                check("");
            }
            return sVersion;
        }

        public static bool check(String rom)
        {
            if (sName != null)
            {
                return sName.Equals(rom);
            }

            if (!string.IsNullOrEmpty(sVersion = getProp(KEY_VERSION_MIUI)))
            {
                sName = ROM_MIUI;
            }
            else if (!string.IsNullOrEmpty(sVersion = getProp(KEY_VERSION_EMUI)))
            {
                sName = ROM_EMUI;
            }
            else if (!string.IsNullOrEmpty(sVersion = getProp(KEY_VERSION_OPPO)))
            {
                sName = ROM_OPPO;
            }
            else if (!string.IsNullOrEmpty(sVersion = getProp(KEY_VERSION_VIVO)))
            {
                sName = ROM_VIVO;
            }
            else if (!string.IsNullOrEmpty(sVersion = getProp(KEY_VERSION_SMARTISAN)))
            {
                sName = ROM_SMARTISAN;
            }
            else
            {
                //				sVersion = Build.DISPLAY;
                //				if (sVersion.ToUpper().contains(ROM_FLYME)) {
                //					sName = ROM_FLYME;
                //				} else {
                //					sVersion = Build.UNKNOWN;
                //					sName = Build.MANUFACTURER.toUpperCase();
                //				}
                sName = "";
            }
            return sName.Equals(rom);
        }

        public static String getProp(String name)
        {
            String line = null;
            AndroidJavaObject input = null;
            try
            {
                var cProcess = new AndroidJavaClass("android.os.Process");
                var cRuntime = new AndroidJavaClass("java.lang.Runtime");
                var p = cRuntime.CallStatic<AndroidJavaObject>("getRuntime").Call<AndroidJavaObject>("exec", "getprop " + name);
                var inputStream = p.Call<AndroidJavaObject>("getInputStream");
                var inputStreamReader = new AndroidJavaObject("java.io.InputStreamReader", inputStream);
                input = new AndroidJavaObject("java.io.BufferedReader", inputStreamReader, 1024);
                line = input.Call<string>("readLine");
                input.Call("close");
            }
            catch (Exception ex)
            {
                Log("Unable to read prop " + name + ex.ToString());
                return null;
            }
            finally
            {
                if (input != null)
                {
                    try
                    {
                        input.Call("close");
                    }
                    catch (Exception e)
                    {
                        Log(e.StackTrace);
                    }
                }
            }
            return line;
        }
    }
}