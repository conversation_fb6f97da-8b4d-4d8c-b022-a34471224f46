using System;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;
using System.IO;

namespace bc.MiniGameBase
{
    public class BaseMiniConfig : ScriptableObject
    {
        [SerializeField]
        public List<BaseMiniConfigStruct> miniGameConfigs = new List<BaseMiniConfigStruct>();
    }

    [Serializable]
    public class BaseMiniConfigStruct
    {
        [LabelText("小游戏名")]
        public string MiniGameName;

        [LabelText("使用lua文件启动")]
        public bool UseLuaOpen = true;

        [Sirenix.OdinInspector.FilePath]
        [HideIf(@"UseLuaOpen")]
        [LabelText("启动C#文件名")]
        [OnValueChanged("OnMiniGameEnterCSharpChange")]
        public string MiniGameEnterCSharp;
        
        public bool isJP;

        public bool isVerfy;

        public int levelNum = 64;
        
        private void OnMiniGameEnterCSharpChange()
        {
            MiniGameEnterCSharp = Path.GetFileNameWithoutExtension(MiniGameEnterCSharp);
        }

        [Sirenix.OdinInspector.FilePath]
        [ShowIf(@"UseLuaOpen")]
        [LabelText("启动lua文件名")]
        [OnValueChanged("OnMiniGameEnterLuaChange")]
        public string MiniGameEnterLua;

        private void OnMiniGameEnterLuaChange()
        {
            MiniGameEnterLua = Path.GetFileNameWithoutExtension(MiniGameEnterLua);
        }
    }
}