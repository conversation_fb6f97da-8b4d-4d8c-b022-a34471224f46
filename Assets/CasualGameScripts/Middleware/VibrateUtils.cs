#if WX_WECHAT
using WeChatWASM;
#endif

namespace CasualGame.Middleware
{
    public static class VibrateUtils
    {
        /// <summary>
        /// 表示不同的振动预设类型
        /// 根据机型的马达不同，体感会有所差异
        /// </summary>
        public enum PresetType
        {
            /// <summary>
            /// 选择
            /// </summary>
            Selection = 0,

            /// <summary>
            /// 成功
            /// </summary>
            Success = 1,

            /// <summary>
            /// 警告
            /// </summary>
            Warning = 2,

            /// <summary>
            /// 失败
            /// </summary>
            Failure = 3,

            /// <summary>
            /// 轻度
            /// </summary>
            LightImpact = 4,

            /// <summary>
            /// 中等强度
            /// </summary>
            MediumImpact = 5,

            /// <summary>
            /// 重度
            /// </summary>
            HeavyImpact = 6,

            /// <summary>
            /// 刚性冲击
            /// </summary>
            RigidImpact = 7,

            /// <summary>
            /// 柔性冲击
            /// </summary>
            SoftImpact = 8,

            /// <summary>
            /// 无
            /// </summary>
            None = -1
        }
        
        /// <summary>
        /// 播放振动
        /// </summary>
        /// <param name="duration">持续时间,大于0</param>
        /// <param name="amplitude">振动幅度from 0.0 to 1.0</param>
        /// <param name="frequency">振动频率from 0.0 to 1.0</param>
        public static void PlayVibrate(float duration, float amplitude, float frequency)
        {
#if WX_WECHAT
            var option = new VibrateShortOption();
            WX.VibrateShort(option);
#endif
        }

        /// <summary>
        /// 播放预设振动
        /// </summary>
        /// <param name="presetType">预设振动类型</param>
        public static void PlayPresetVibrate(PresetType presetType)
        {
#if WX_WECHAT
            if (presetType == PresetType.HeavyImpact)
            {
                var option = new VibrateLongOption();
                WX.VibrateLong(option);
            }
            else
            {
                var option = new VibrateShortOption();
                WX.VibrateShort(option);
            }
#endif
        }
    }
}
